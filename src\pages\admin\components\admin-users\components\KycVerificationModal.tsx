import React, { useState } from 'react'
import { X, Check, AlertTriangle, FileImage, User, ZoomIn } from 'lucide-react'
import { user } from '../admin-users.model'

interface KycVerificationModalProps {
  isOpen: boolean
  onClose: () => void
  user: user | null
  onConfirm: (userId: number) => Promise<void>
  onReject: (userId: number, reason: string) => Promise<void>
  isProcessing: boolean
}

function KycVerificationModal({
  isOpen,
  onClose,
  user,
  onConfirm,
  onReject,
  isProcessing
}: KycVerificationModalProps) {
  const [showRejectForm, setShowRejectForm] = useState(false)
  const [rejectionReason, setRejectionReason] = useState('')
  const [zoomedImage, setZoomedImage] = useState<string | null>(null)

  if (!isOpen || !user) return null

  const handleConfirm = async () => {
    try {
      await onConfirm(user.id)
      onClose()
    } catch (error) {
      console.error('Error confirming KYC:', error)
    }
  }

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      alert('Please provide a rejection reason')
      return
    }

    try {
      await onReject(user.id, rejectionReason.trim())
      setRejectionReason('')
      setShowRejectForm(false)
      onClose()
    } catch (error) {
      console.error('Error rejecting KYC:', error)
    }
  }

  const handleRejectClick = () => {
    setShowRejectForm(true)
  }

  const handleCancelReject = () => {
    setShowRejectForm(false)
    setRejectionReason('')
  }

  const handleModalClose = () => {
    setShowRejectForm(false)
    setRejectionReason('')
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl shadow-2xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="p-6 border-b border-gray-700 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">KYC Verification</h2>
              <p className="text-gray-400 text-sm">{user.name} - {user.email}</p>
            </div>
          </div>
          <button
            onClick={handleModalClose}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isProcessing}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6">
          {/* User Information Summary */}
          <div className="mb-6 p-4 bg-gray-700/30 rounded-lg">
            <h3 className="text-lg font-medium text-white mb-3">User Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Name:</span>
                <span className="text-white ml-2">{user.name}</span>
              </div>
              <div>
                <span className="text-gray-400">Email:</span>
                <span className="text-white ml-2">{user.email}</span>
              </div>
              <div>
                <span className="text-gray-400">Phone:</span>
                <span className="text-white ml-2">{user.country_code} {user.phone}</span>
              </div>
              <div>
                <span className="text-gray-400">Current Status:</span>
                <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                  user.kyc_status === 'verified'
                    ? 'bg-green-900/20 text-green-400'
                    : user.kyc_status === 'pending'
                    ? 'bg-yellow-900/20 text-yellow-400'
                    : 'bg-red-900/20 text-red-400'
                }`}>
                  {user.kyc_status}
                </span>
              </div>
            </div>
          </div>

          {/* KYC Documents */}
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-white flex items-center space-x-2">
              <FileImage className="w-5 h-5 text-blue-400" />
              <span>KYC Documents</span>
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* National ID */}
              <div className="space-y-3">
                <h4 className="text-md font-medium text-gray-300">National ID</h4>
                {user.national_id ? (
                  <div className="border border-gray-600 rounded-lg overflow-hidden bg-gray-700">
                    <div className="relative group cursor-pointer" onClick={() => setZoomedImage(user.national_id!)}>
                      <img
                        src={user.national_id}
                        alt="National ID"
                        className="w-full h-64 object-contain bg-white transition-transform group-hover:scale-105"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = `
                              <div class="w-full h-64 bg-gray-700 flex items-center justify-center">
                                <div class="text-center text-gray-400">
                                  <svg class="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                                  </svg>
                                  <p class="text-sm">Image not available</p>
                                  <p class="text-xs mt-1 break-all">${user.national_id}</p>
                                </div>
                              </div>
                            `;
                          }
                        }}
                        onLoad={(e) => {
                          console.log('National ID image loaded successfully');
                        }}
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                        <ZoomIn className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                    <div className="p-3 bg-gray-700/50 border-t border-gray-600">
                      <p className="text-xs text-gray-400">National ID Document - Click to zoom</p>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-64 bg-gray-700 border border-gray-600 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <FileImage className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-400 text-sm">No National ID provided</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Passport */}
              <div className="space-y-3">
                <h4 className="text-md font-medium text-gray-300">Passport</h4>
                {user.passport ? (
                  <div className="border border-gray-600 rounded-lg overflow-hidden bg-gray-700">
                    <div className="relative group cursor-pointer" onClick={() => setZoomedImage(user.passport!)}>
                      <img
                        src={user.passport}
                        alt="Passport"
                        className="w-full h-64 object-contain bg-white transition-transform group-hover:scale-105"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = `
                              <div class="w-full h-64 bg-gray-700 flex items-center justify-center">
                                <div class="text-center text-gray-400">
                                  <svg class="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                                  </svg>
                                  <p class="text-sm">Image not available</p>
                                  <p class="text-xs mt-1 break-all">${user.passport}</p>
                                </div>
                              </div>
                            `;
                          }
                        }}
                        onLoad={(e) => {
                          console.log('Passport image loaded successfully');
                        }}
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                        <ZoomIn className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                    <div className="p-3 bg-gray-700/50 border-t border-gray-600">
                      <p className="text-xs text-gray-400">Passport Document - Click to zoom</p>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-64 bg-gray-700 border border-gray-600 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <FileImage className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-400 text-sm">No Passport provided</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Rejection Form */}
          {showRejectForm && (
            <div className="mt-6 p-4 bg-red-900/10 border border-red-900/30 rounded-lg">
              <div className="flex items-center space-x-2 mb-4">
                <AlertTriangle className="w-5 h-5 text-red-400" />
                <h4 className="text-lg font-medium text-red-400">Rejection Reason</h4>
              </div>
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-300">
                  Please provide a detailed reason for rejecting this KYC application:
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Enter the reason for rejection (e.g., unclear document, invalid information, etc.)"
                  className="w-full h-24 px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-vertical"
                  disabled={isProcessing}
                />
                <p className="text-xs text-gray-400">
                  This reason will be sent to the user for their reference.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Modal Actions */}
        <div className="p-6 border-t border-gray-700 flex justify-end space-x-4">
          {showRejectForm ? (
            <>
              <button
                onClick={handleCancelReject}
                className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors"
                disabled={isProcessing}
              >
                Cancel
              </button>
              <button
                onClick={handleReject}
                disabled={isProcessing || !rejectionReason.trim()}
                className="flex items-center space-x-2 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <X className="w-4 h-4" />
                <span>{isProcessing ? 'Rejecting...' : 'Reject KYC'}</span>
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleModalClose}
                className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors"
                disabled={isProcessing}
              >
                Cancel
              </button>
              {user.kyc_status === 'pending' && (
                <>
                  <button
                    onClick={handleRejectClick}
                    className="flex items-center space-x-2 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    disabled={isProcessing}
                  >
                    <X className="w-4 h-4" />
                    <span>Reject</span>
                  </button>
                  <button
                    onClick={handleConfirm}
                    disabled={isProcessing}
                    className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Check className="w-4 h-4" />
                    <span>{isProcessing ? 'Confirming...' : 'Confirm'}</span>
                  </button>
                </>
              )}
            </>
          )}
        </div>
      </div>

      {/* Image Zoom Modal */}
      {zoomedImage && (
        <div
          className="fixed inset-0 bg-black/80 flex items-center justify-center z-[100] p-4"
          onClick={() => setZoomedImage(null)}
        >
          <div className="relative max-w-[90vw] max-h-[90vh]">
            <img
              src={zoomedImage}
              alt="Zoomed document"
              className="max-w-full max-h-full object-contain"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={() => setZoomedImage(null)}
              className="absolute top-4 right-4 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default KycVerificationModal
