// Design System Constants and Utilities for Fruit-O-Cart

export const colors = {
  // Primary backgrounds
  primary: '#0D1117',      // Main background
  secondary: '#1A1F2E',    // Card background
  tertiary: '#131722',     // Input/button background

  // Text colors
  textPrimary: '#FFFFFF',
  textSecondary: '#E5E7EB',
  textTertiary: '#9CA3AF',
  textMuted: '#6B7280',

  // Accent colors
  blue: {
    primary: '#4A6FFF',
    secondary: '#6C8FFF',
    hover: '#3B5FEF',
    hoverSecondary: '#5C7FEF',
    light: '#93C5FD',
  },

  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',

  // Border colors
  border: '#374151',
  borderLight: '#4B5563',
  borderDark: '#1F2937',

  // Gray scale (Tailwind compatible)
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
} as const;

export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '0.75rem',    // 12px
  lg: '1rem',       // 16px
  xl: '1.5rem',     // 24px
  '2xl': '2rem',    // 32px
  '3xl': '3rem',    // 48px
  '4xl': '4rem',    // 64px
} as const;

export const borderRadius = {
  sm: '0.25rem',    // 4px
  md: '0.5rem',     // 8px
  lg: '0.75rem',    // 12px
  xl: '1rem',       // 16px
  '2xl': '1.5rem',  // 24px
  full: '9999px',
} as const;

export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
} as const;

// Component style generators
export const getCardStyles = (variant: 'primary' | 'secondary' = 'primary') => {
  const baseStyles = 'rounded-xl shadow-lg border transition-all duration-200';

  switch (variant) {
    case 'primary':
      return `${baseStyles} bg-[${colors.secondary}] border-gray-700`;
    case 'secondary':
      return `${baseStyles} bg-[${colors.tertiary}] border-gray-600`;
    default:
      return `${baseStyles} bg-[${colors.secondary}] border-gray-700`;
  }
};

export const getButtonStyles = (variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary') => {
  const baseStyles = 'px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center';

  switch (variant) {
    case 'primary':
      return `${baseStyles} bg-gradient-to-r from-[${colors.blue.primary}] to-[${colors.blue.secondary}] hover:from-[${colors.blue.hover}] hover:to-[${colors.blue.hoverSecondary}] text-white`;
    case 'secondary':
      return `${baseStyles} bg-[${colors.tertiary}] hover:bg-[${colors.secondary}] text-gray-300 border border-gray-600`;
    case 'outline':
      return `${baseStyles} bg-transparent border border-blue-400 text-blue-400 hover:bg-[${colors.tertiary}]`;
    case 'ghost':
      return `${baseStyles} bg-transparent text-gray-300 hover:bg-[${colors.tertiary}]`;
    default:
      return `${baseStyles} bg-gradient-to-r from-[${colors.blue.primary}] to-[${colors.blue.secondary}] hover:from-[${colors.blue.hover}] hover:to-[${colors.blue.hoverSecondary}] text-white`;
  }
};

export const getInputStyles = () => {
  return `w-full px-4 py-3 bg-[${colors.tertiary}] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`;
};

export const getTextStyles = (variant: 'h1' | 'h2' | 'h3' | 'body' | 'caption' = 'body') => {
  switch (variant) {
    case 'h1':
      return 'text-3xl font-bold text-white';
    case 'h2':
      return 'text-2xl font-semibold text-white';
    case 'h3':
      return 'text-lg font-medium text-white';
    case 'body':
      return 'text-sm text-gray-300';
    case 'caption':
      return 'text-xs text-gray-400';
    default:
      return 'text-sm text-gray-300';
  }
};

export const getStatusStyles = (status: 'success' | 'warning' | 'error' | 'info') => {
  const baseStyles = 'px-2 py-1 rounded-full text-xs font-semibold';

  switch (status) {
    case 'success':
      return `${baseStyles} bg-green-900/30 text-green-400`;
    case 'warning':
      return `${baseStyles} bg-yellow-900/30 text-yellow-400`;
    case 'error':
      return `${baseStyles} bg-red-900/30 text-red-400`;
    case 'info':
      return `${baseStyles} bg-blue-900/30 text-blue-400`;
    default:
      return `${baseStyles} bg-gray-900/30 text-gray-400`;
  }
};

// Animation utilities
export const animations = {
  fadeIn: 'animate-in fade-in duration-200',
  fadeOut: 'animate-out fade-out duration-200',
  slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
  slideOut: 'animate-out slide-out-to-bottom-4 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
  scaleOut: 'animate-out zoom-out-95 duration-200',
} as const;

// Responsive breakpoints
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Common component patterns
export const patterns = {
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  gridCols: {
    1: 'grid grid-cols-1',
    2: 'grid grid-cols-1 md:grid-cols-2',
    3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  },
  gap: {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
  },
} as const;

// Utility functions
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

export const formatCurrency = (amount: number, currency: string = 'USDT'): string => {
  return `${amount.toLocaleString()} ${currency}`;
};

// Color validation and utility functions
export const validateColor = (color: string): boolean => {
  const validColors: string[] = [
    colors.primary, colors.secondary, colors.tertiary,
    colors.textPrimary, colors.textSecondary, colors.textTertiary, colors.textMuted,
    colors.blue.primary, colors.blue.secondary, colors.blue.hover, colors.blue.hoverSecondary, colors.blue.light,
    colors.success, colors.warning, colors.error, colors.info,
    colors.border, colors.borderLight, colors.borderDark,
    ...Object.values(colors.gray)
  ];
  return validColors.includes(color);
};

// Get consistent Tailwind classes for common color patterns
export const getColorClasses = {
  // Background classes
  primaryBg: 'bg-[#0D1117]',
  secondaryBg: 'bg-[#1A1F2E]',
  tertiaryBg: 'bg-[#131722]',

  // Text classes
  primaryText: 'text-white',
  secondaryText: 'text-gray-300',
  tertiaryText: 'text-gray-400',
  mutedText: 'text-gray-500',

  // Border classes
  primaryBorder: 'border-gray-700',
  secondaryBorder: 'border-gray-600',

  // Button classes
  primaryButton: 'bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] hover:from-[#3B5FEF] hover:to-[#5C7FEF] text-white',
  secondaryButton: 'bg-[#131722] text-blue-400 border border-blue-400 hover:bg-[#1A1F2E]',

  // Status classes
  success: 'text-green-400 bg-green-900/30',
  warning: 'text-yellow-400 bg-yellow-900/30',
  error: 'text-red-400 bg-red-900/30',
  info: 'text-blue-400 bg-blue-900/30',
} as const;

// Chart color palette
export const chartColors = {
  primary: '#4A6FFF',
  secondary: '#6C8FFF',
  background: '#131722',
  grid: '#374151',
  text: '#9CA3AF',
  area: 'rgba(74, 111, 255, 0.2)',
} as const;

export const formatPercentage = (value: number): string => {
  return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};
