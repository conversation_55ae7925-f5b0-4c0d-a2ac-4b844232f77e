@import '../../../styles/variables.scss';

// Legacy text-area class (kept for backward compatibility)
.text-area {
    width: 100%;
    border: 2px solid $black_color4;
    border-radius: 8px;
    padding: 12px;
    min-height: 80px;
    font-size: 14px;
    color: $text_primary;
    margin-bottom: 20px;
    background-color: $secondary_background;
    resize: vertical;
    transition: border-color 0.2s ease;

    &:focus {
        outline: none;
        border: 2px solid $primary_color;
        box-shadow: 0 0 0 3px rgba(74, 111, 255, 0.1);
    }

    &::placeholder {
        color: $text_muted;
    }
}

.alert-icons {
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }
}


@keyframes countdown {
    from {
        border: 3px solid var(--primary-color);
        box-shadow: 0 0 0 0 rgba(74, 111, 255, 0.4);
    }
    to {
        border: 3px solid transparent;
        box-shadow: 0 0 0 10px rgba(74, 111, 255, 0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.alert-box {
    position: relative;
    animation: slideIn 0.3s ease-out;
    border-radius: 16px;
    margin: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.3),
        0 10px 10px -5px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.05);

    &[style*="animation"] {
        animation: slideIn 0.3s ease-out, countdown linear forwards;
    }

    // Focus styles for accessibility
    &:focus {
        outline: none;
        box-shadow:
            0 20px 25px -5px rgba(0, 0, 0, 0.3),
            0 10px 10px -5px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.05),
            0 0 0 3px rgba(59, 130, 246, 0.5);
    }
}

// Responsive design
@media (max-width: 640px) {
    .alert-box {
        margin: 10px;
        width: calc(100vw - 20px);
        max-width: none;
    }

    .text-area {
        min-height: 60px;
        font-size: 16px; // Prevent zoom on iOS
    }
}

