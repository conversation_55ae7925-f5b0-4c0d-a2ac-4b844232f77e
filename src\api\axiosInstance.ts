// import axios, {
//   AxiosInstance,
//   AxiosResponse,
//   AxiosError,
//   InternalAxiosRequestConfig,
//   AxiosRequestConfig
// } from 'axios';

// // ==================== TYPE DEFINITIONS ====================

// // Base API Response Interface
// export interface ApiResponse<T = any> {
//   success: boolean;
//   message: string;
//   data?: T;
//   error?: string;
//   errors?: Record<string, string[]>;
//   meta?: {
//     pagination?: {
//       page: number;
//       limit: number;
//       total: number;
//       totalPages: number;
//     };
//     timestamp?: string;
//     requestId?: string;
//   };
// }

// // Token Response Interface
// export interface TokenResponse {
//   access_token: string;
//   refresh_token?: string;
//   token_type?: string;
//   expires_in?: number;
//   user?: any;
//   requiresEmailVerification?: boolean;
//   message?: string;
// }

// // Refresh Token Request Interface
// interface RefreshTokenRequest {
//   refresh_token: string;
// }

// // Extended Axios config with custom flags
// interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
//   _retry?: boolean;
//   skipAuth?: boolean;
//   isFormData?: boolean;
//   timeout?: number;
// }

// // Request Configuration Options
// export interface RequestConfig extends AxiosRequestConfig {
//   skipAuth?: boolean;
//   isFormData?: boolean;
//   timeout?: number;
// }

// // File Upload Configuration
// export interface FileUploadConfig {
//   onUploadProgress?: (progressEvent: any) => void;
//   timeout?: number;
//   maxFileSize?: number; // in bytes
//   allowedTypes?: string[];
// }

// // ==================== CONFIGURATION ====================

// // Environment variables with fallbacks - using single API base URL
// const API_CONFIG = {
//   BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://*************:8000/',
//   TIMEOUT: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000'),
//   MAX_FILE_SIZE: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'), // 10MB
//   RETRY_ATTEMPTS: parseInt(process.env.NEXT_PUBLIC_RETRY_ATTEMPTS || '3'),
//   AUTH_PREFIX: '/auth', // Auth endpoints prefix
// } as const;

// // Create single API instance for both auth and regular endpoints
// const api: AxiosInstance = axios.create({
//   baseURL: API_CONFIG.BASE_URL,
//   timeout: API_CONFIG.TIMEOUT,
//   headers: {
//     'Accept': 'application/json',
//   },
// });

// // ==================== UTILITY FUNCTIONS ====================

// // JWT validation
// const isValidJWT = (token: string | null): token is string => {
//   if (!token) return false;
//   try {
//     const parts = token.split('.');
//     if (parts.length !== 3) return false;

//     // Check if token is expired
//     const payload = JSON.parse(atob(parts[1]));
//     const currentTime = Math.floor(Date.now() / 1000);
//     return payload.exp > currentTime;
//   } catch {
//     return false;
//   }
// };

// // Token management functions
// const getAccessToken = (): string | null => {
//   if (typeof window !== 'undefined') {
//     return localStorage.getItem('access_token');
//   }
//   return null;
// };

// const getRefreshToken = (): string | null => {
//   if (typeof window !== 'undefined') {
//     return localStorage.getItem('refresh_token');
//   }
//   return null;
// };

// const setTokens = (accessToken: string, refreshToken?: string): void => {
//   if (typeof window !== 'undefined') {
//     localStorage.setItem('access_token', accessToken);
//     if (refreshToken) {
//       localStorage.setItem('refresh_token', refreshToken);
//     }
//   }
// };

// const clearTokens = (): void => {
//   if (typeof window !== 'undefined') {
//     localStorage.removeItem('access_token');
//     localStorage.removeItem('refresh_token');
//     localStorage.removeItem('user_data');
//   }
// };

// const redirectToLogin = (): void => {
//   if (typeof window !== 'undefined') {
//     // Clear any existing tokens
//     clearTokens();
//     // Redirect to login page
//     window.location.href = '/auth/login';
//   }
// };

// // Content type detection
// const isFormData = (data: any): boolean => {
//   return data instanceof FormData;
// };

// const isFile = (data: any): boolean => {
//   return data instanceof File || data instanceof Blob;
// };

// // Check if request is for auth endpoint
// const isAuthEndpoint = (url: string): boolean => {
//   return url.startsWith(API_CONFIG.AUTH_PREFIX) || url.startsWith('/auth');
// };

// // File validation
// const validateFile = (file: File, config?: FileUploadConfig): { valid: boolean; error?: string } => {
//   if (!file) {
//     return { valid: false, error: 'No file provided' };
//   }

//   // Check file size
//   const maxSize = config?.maxFileSize || API_CONFIG.MAX_FILE_SIZE;
//   if (file.size > maxSize) {
//     return {
//       valid: false,
//       error: `File size exceeds ${(maxSize / 1024 / 1024).toFixed(1)}MB limit`
//     };
//   }

//   // Check file type
//   if (config?.allowedTypes && config.allowedTypes.length > 0) {
//     const fileType = file.type;
//     const isAllowed = config.allowedTypes.some(type => {
//       if (type.includes('*')) {
//         return fileType.startsWith(type.replace('*', ''));
//       }
//       return fileType === type;
//     });

//     if (!isAllowed) {
//       return {
//         valid: false,
//         error: `File type not allowed. Allowed types: ${config.allowedTypes.join(', ')}`
//       };
//     }
//   }

//   return { valid: true };
// };

// // ==================== INTERCEPTORS ====================

// // Request interceptor
// api.interceptors.request.use(
//   (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
//     const customConfig = config as CustomAxiosRequestConfig;

//     // Initialize headers if not present
//     customConfig.headers = customConfig.headers ?? {};

//     // Handle authentication - skip for auth endpoints and when explicitly disabled
//     const isAuthRequest = isAuthEndpoint(customConfig.url || '');
//     if (!customConfig.skipAuth && !isAuthRequest) {
//       const token = getAccessToken();
//       if (isValidJWT(token)) {
//         customConfig.headers['Authorization'] = `Bearer ${token}`;
//       } else if (token) {
//         // Invalid token, clear it
//         clearTokens();
//       }
//     }

//     // Handle content type based on data type
//     if (customConfig.data) {
//       if (isFormData(customConfig.data)) {
//         // For FormData, let browser set Content-Type with boundary
//         delete customConfig.headers['Content-Type'];
//         customConfig.isFormData = true;
//       } else if (isFile(customConfig.data)) {
//         // For direct file uploads
//         customConfig.headers['Content-Type'] = 'application/octet-stream';
//       } else if (typeof customConfig.data === 'object') {
//         // For JSON data
//         customConfig.headers['Content-Type'] = 'application/json';
//       }
//     } else if (!customConfig.headers['Content-Type']) {
//       // Default to JSON for requests without data
//       customConfig.headers['Content-Type'] = 'application/json';
//     }

//     // Add request timestamp for debugging
//     customConfig.headers['X-Request-Time'] = new Date().toISOString();

//     return customConfig;
//   },
//   (error: AxiosError) => {
//     console.error('Request interceptor error:', error);
//     return Promise.reject(error);
//   }
// );

// // Response interceptor
// api.interceptors.response.use(
//   (response: AxiosResponse) => {
//     // Log successful responses in development
//     if (process.env.NODE_ENV === 'development') {
//       const isAuth = isAuthEndpoint(response.config.url || '');
//       const prefix = isAuth ? 'AUTH' : 'API';
//       console.log(`✅ ${prefix} ${response.config.method?.toUpperCase()} ${response.config.url}:`, response.status);
//     }
//     return response;
//   },
//   async (error: AxiosError) => {
//     const originalRequest = error.config as CustomAxiosRequestConfig;

//     // Log errors in development
//     if (process.env.NODE_ENV === 'development') {
//       const isAuth = isAuthEndpoint(originalRequest?.url || '');
//       const prefix = isAuth ? 'AUTH' : 'API';
//       console.error(`❌ ${prefix} ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}:`, error.response?.status, error.message);
//     }

//     // Don't retry if already retried, auth is skipped, or this is an auth endpoint
//     const isAuthRequest = isAuthEndpoint(originalRequest?.url || '');
//     if (originalRequest?._retry || originalRequest?.skipAuth || isAuthRequest) {
//       return Promise.reject(error);
//     }

//     // Handle authentication errors (401/403)
//     if (error.response && (error.response.status === 401 || error.response.status === 403)) {
//       originalRequest._retry = true;

//       try {
//         const refreshToken = getRefreshToken();

//         if (!refreshToken) {
//           clearTokens();
//           redirectToLogin();
//           return Promise.reject(error);
//         }

//         // Attempt to refresh token
//         const response = await api.post<TokenResponse>(`${API_CONFIG.AUTH_PREFIX}/refresh-token`, {
//           refresh_token: refreshToken
//         } as RefreshTokenRequest);

//         if (response.data?.access_token) {
//           // Update tokens
//           setTokens(response.data.access_token, response.data.refresh_token);

//           // Update authorization header for retry
//           if (originalRequest.headers) {
//             originalRequest.headers['Authorization'] = `Bearer ${response.data.access_token}`;
//           }

//           // Retry original request
//           return api(originalRequest);
//         } else {
//           throw new Error('Invalid token response');
//         }
//       } catch (refreshError) {
//         console.error('Token refresh failed:', refreshError);
//         clearTokens();
//         redirectToLogin();
//         return Promise.reject(refreshError);
//       }
//     }

//     // Handle network errors
//     if (!error.response) {
//       console.error('Network error:', error.message);
//       return Promise.reject({
//         ...error,
//         message: 'Network error. Please check your internet connection.',
//       });
//     }

//     // Handle server errors
//     if (error.response.status >= 500) {
//       console.error('Server error:', error.response.status, error.response.data);
//       return Promise.reject({
//         ...error,
//         message: 'Server error. Please try again later.',
//       });
//     }

//     return Promise.reject(error);
//   }
// );

// // ==================== API METHODS ====================

// // Authentication API methods - now using the same instance with auth prefix
// export const authApiMethods = {
//   // User authentication
//   login: async (credentials: { email: string; password: string }) => {
//     return api.post<TokenResponse>(`${API_CONFIG.AUTH_PREFIX}/login`, credentials);
//   },

//   signup: async (userData: {
//     fullName: string;
//     email: string;
//     password: string;
//     phoneNumber: string;
//     dateOfBirth: string;
//     nationality: string;
//     countryOfResidence: string;
//     currency: string;
//     address: string;
//     referralCode?: string;
//     termsAccepted: boolean;
//     privacyAccepted: boolean;
//     [key: string]: any;
//   }) => {
//     return api.post<TokenResponse>(`${API_CONFIG.AUTH_PREFIX}/signup`, userData);
//   },

//     signupWithFormData: async (formData: FormData) => {
//     return api.post<TokenResponse>(`${API_CONFIG.AUTH_PREFIX}/signup`, formData, {
//       isFormData: true,
//       headers: {
//         // Let browser set Content-Type with boundary for FormData
//         'Content-Type': undefined
//       }
//     });
//   },

//   // Password management
//   forgotPassword: async (email: string) => {
//     return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/forgot-password`, { email });
//   },

//   resetPassword: async (token: string, newPassword: string) => {
//     return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/reset-password`, { token, password: newPassword });
//   },

//   // Email verification
//   verifyEmail: async (token: string) => {
//     return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/verify-email`, { token });
//   },

//   sendVerificationEmail: async (email: string) => {
//     return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/send-verification-email`, { email });
//   },

//   // Token management
//   refreshToken: async (refreshToken: string) => {
//     return api.post<TokenResponse>(`${API_CONFIG.AUTH_PREFIX}/refresh-token`, { refresh_token: refreshToken });
//   },

//   logout: async () => {
//     const token = getAccessToken();
//     if (token) {
//       return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/logout`, {}, {
//         headers: { Authorization: `Bearer ${token}` }
//       });
//     }
//     return Promise.resolve({ data: { success: true, message: 'Logged out locally' } });
//   },

//   // OTP methods
//   sendOTP: async (email: string, type: 'email_verification' | 'password_reset' = 'email_verification') => {
//     return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/send-otp`, { email, type });
//   },

//   verifyOTP: async (email: string, otp: string, type: 'email_verification' | 'password_reset' = 'email_verification') => {
//     return api.post<ApiResponse>(`${API_CONFIG.AUTH_PREFIX}/verify-otp`, { email, otp, type });
//   }
// };

// // Main API methods with authentication
// export const apiMethods = {
//   // Standard CRUD operations
//   get: <T = any>(url: string, config?: RequestConfig) =>
//     api.get<T>(url, config as CustomAxiosRequestConfig),

//   post: <T = any>(url: string, data?: any, config?: RequestConfig) =>
//     api.post<T>(url, data, config as CustomAxiosRequestConfig),

//   put: <T = any>(url: string, data?: any, config?: RequestConfig) =>
//     api.put<T>(url, data, config as CustomAxiosRequestConfig),

//   patch: <T = any>(url: string, data?: any, config?: RequestConfig) =>
//     api.patch<T>(url, data, config as CustomAxiosRequestConfig),

//   delete: <T = any>(url: string, config?: RequestConfig) =>
//     api.delete<T>(url, config as CustomAxiosRequestConfig),

//   // File upload methods
//   uploadFile: async <T = any>(
//     url: string,
//     file: File,
//     config?: FileUploadConfig & RequestConfig
//   ): Promise<AxiosResponse<T>> => {
//     // Validate file
//     const validation = validateFile(file, config);
//     if (!validation.valid) {
//       throw new Error(validation.error);
//     }

//     const formData = new FormData();
//     formData.append('file', file);

//     return api.post<T>(url, formData, {
//       ...config,
//       isFormData: true,
//       onUploadProgress: config?.onUploadProgress,
//       timeout: config?.timeout || API_CONFIG.TIMEOUT * 2, // Double timeout for uploads
//     } as CustomAxiosRequestConfig);
//   },

//   uploadMultipleFiles: async <T = any>(
//     url: string,
//     files: File[],
//     config?: FileUploadConfig & RequestConfig
//   ): Promise<AxiosResponse<T>> => {
//     // Validate all files
//     for (const file of files) {
//       const validation = validateFile(file, config);
//       if (!validation.valid) {
//         throw new Error(`${file.name}: ${validation.error}`);
//       }
//     }

//     const formData = new FormData();
//     files.forEach((file, index) => {
//       formData.append(`files[${index}]`, file);
//     });

//     return api.post<T>(url, formData, {
//       ...config,
//       isFormData: true,
//       onUploadProgress: config?.onUploadProgress,
//       timeout: config?.timeout || API_CONFIG.TIMEOUT * 3, // Triple timeout for multiple uploads
//     } as CustomAxiosRequestConfig);
//   },

//   uploadWithData: async <T = any>(
//     url: string,
//     file: File,
//     data: Record<string, any>,
//     config?: FileUploadConfig & RequestConfig
//   ): Promise<AxiosResponse<T>> => {
//     // Validate file
//     const validation = validateFile(file, config);
//     if (!validation.valid) {
//       throw new Error(validation.error);
//     }

//     const formData = new FormData();
//     formData.append('file', file);

//     // Append additional data
//     Object.keys(data).forEach(key => {
//       const value = data[key];
//       if (value !== null && value !== undefined) {
//         formData.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value));
//       }
//     });

//     return api.post<T>(url, formData, {
//       ...config,
//       isFormData: true,
//       onUploadProgress: config?.onUploadProgress,
//       timeout: config?.timeout || API_CONFIG.TIMEOUT * 2,
//     } as CustomAxiosRequestConfig);
//   },

//   // Pagination helper
//   getPaginated: async <T = any>(
//     url: string,
//     page: number = 1,
//     limit: number = 10,
//     config?: RequestConfig
//   ): Promise<AxiosResponse<ApiResponse<T[]>>> => {
//     const params = new URLSearchParams({
//       page: page.toString(),
//       limit: limit.toString(),
//       ...(config?.params || {})
//     });

//     return api.get<ApiResponse<T[]>>(`${url}?${params}`, config as CustomAxiosRequestConfig);
//   },

//   // Search helper
//   search: async <T = any>(
//     url: string,
//     query: string,
//     filters?: Record<string, any>,
//     config?: RequestConfig
//   ): Promise<AxiosResponse<ApiResponse<T[]>>> => {
//     const params = new URLSearchParams({
//       q: query,
//       ...(filters || {}),
//       ...(config?.params || {})
//     });

//     return api.get<ApiResponse<T[]>>(`${url}?${params}`, config as CustomAxiosRequestConfig);
//   },

//   // No-auth methods
//   withoutAuth: {
//     get: <T = any>(url: string, config?: RequestConfig) =>
//       api.get<T>(url, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig),

//     post: <T = any>(url: string, data?: any, config?: RequestConfig) =>
//       api.post<T>(url, data, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig),

//     put: <T = any>(url: string, data?: any, config?: RequestConfig) =>
//       api.put<T>(url, data, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig),

//     patch: <T = any>(url: string, data?: any, config?: RequestConfig) =>
//       api.patch<T>(url, data, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig),

//     delete: <T = any>(url: string, config?: RequestConfig) =>
//       api.delete<T>(url, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig),
//   }
// };

// // ==================== UTILITIES & EXPORTS ====================

// // Token utilities
// export const tokenUtils = {
//   getAccessToken,
//   getRefreshToken,
//   setTokens,
//   clearTokens,
//   isValidJWT,
//   isAuthenticated: (): boolean => isValidJWT(getAccessToken()),

//   // Get user data from token
//   getUserFromToken: (): any | null => {
//     const token = getAccessToken();
//     if (!isValidJWT(token)) return null;

//     try {
//       const payload = JSON.parse(atob(token!.split('.')[1]));
//       return payload.user || payload;
//     } catch {
//       return null;
//     }
//   },

//   // Check if token is about to expire (within 5 minutes)
//   isTokenExpiringSoon: (): boolean => {
//     const token = getAccessToken();
//     if (!isValidJWT(token)) return true;

//     try {
//       const payload = JSON.parse(atob(token!.split('.')[1]));
//       const currentTime = Math.floor(Date.now() / 1000);
//       const expirationTime = payload.exp;
//       const fiveMinutes = 5 * 60; // 5 minutes in seconds

//       return (expirationTime - currentTime) <= fiveMinutes;
//     } catch {
//       return true;
//     }
//   }
// };

// // File utilities
// export const fileUtils = {
//   validateFile,
//   isFormData,
//   isFile,

//   // Convert file to base64
//   fileToBase64: (file: File): Promise<string> => {
//     return new Promise((resolve, reject) => {
//       const reader = new FileReader();
//       reader.readAsDataURL(file);
//       reader.onload = () => resolve(reader.result as string);
//       reader.onerror = error => reject(error);
//     });
//   },

//   // Format file size
//   formatFileSize: (bytes: number): string => {
//     if (bytes === 0) return '0 Bytes';
//     const k = 1024;
//     const sizes = ['Bytes', 'KB', 'MB', 'GB'];
//     const i = Math.floor(Math.log(bytes) / Math.log(k));
//     return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
//   },

//   // Get file extension
//   getFileExtension: (filename: string): string => {
//     return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
//   }
// };

// // API utilities
// export const apiUtils = {
//   // Build query string from object
//   buildQueryString: (params: Record<string, any>): string => {
//     const searchParams = new URLSearchParams();
//     Object.keys(params).forEach(key => {
//       const value = params[key];
//       if (value !== null && value !== undefined && value !== '') {
//         searchParams.append(key, String(value));
//       }
//     });
//     return searchParams.toString();
//   },

//   // Handle API errors consistently
//   handleApiError: (error: any): { message: string; status?: number; errors?: any } => {
//     if (error.response) {
//       // Server responded with error status
//       return {
//         message: error.response.data?.message || 'An error occurred',
//         status: error.response.status,
//         errors: error.response.data?.errors
//       };
//     } else if (error.request) {
//       // Network error
//       return {
//         message: 'Network error. Please check your connection.',
//         status: 0
//       };
//     } else {
//       // Other error
//       return {
//         message: error.message || 'An unexpected error occurred',
//       };
//     }
//   },

//   // Retry function for failed requests
//   retry: async <T>(
//     fn: () => Promise<T>,
//     retries: number = API_CONFIG.RETRY_ATTEMPTS,
//     delay: number = 1000
//   ): Promise<T> => {
//     try {
//       return await fn();
//     } catch (error) {
//       if (retries > 0) {
//         await new Promise(resolve => setTimeout(resolve, delay));
//         return apiUtils.retry(fn, retries - 1, delay * 2); // Exponential backoff
//       }
//       throw error;
//     }
//   }
// };

// // Configuration utilities
// export const configUtils = {
//   getApiConfig: () => API_CONFIG,

//   // Check if running in development
//   isDevelopment: () => process.env.NODE_ENV === 'development',

//   // Check if running in production
//   isProduction: () => process.env.NODE_ENV === 'production',

//   // Get environment variable with fallback
//   getEnvVar: (key: string, fallback: string = ''): string => {
//     return process.env[key] || fallback;
//   }
// };

// // Export main instance
// export { api as default };


import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Interface for the token refresh response
interface TokenRefreshResponse {
  access_token: string;
  refresh_token?: string;
}

// Interface for the refresh request payload
interface RefreshTokenRequest {
  refresh_token: string;
}

// Create an Axios instance with a base URL
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://192.168.0.107:8000'
});

// Helper function to validate JWT
const isValidJWT = (token: string | null): token is string => {
  return token !== null && token.split('.').length === 3;
};

// Request interceptor
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token: string | null = localStorage.getItem('token');
    
    if (isValidJWT(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      localStorage.removeItem('token'); // Clear invalid token
    }
    
    if (!config.url?.includes('/json/')) {
      config.headers['Content-Type'] = 'application/json';
    }
    
    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Create a separate axios instance for auth requests to prevent infinite loops
const authApi: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || 'http://192.168.0.106:8000'
});

// Extended InternalAxiosRequestConfig to include retry flag
interface ExtendedInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedInternalAxiosRequestConfig;
    
    // Prevent infinite retry loops
    if (originalRequest._retry) {
      return Promise.reject(error);
    }
    
    if (error.response && (error.response.status === 403 || error.response.status === 401)) {
      // originalRequest._retry = true;
      
      try {
        // Get the refresh token from storage
        const refreshToken: string | null = localStorage.getItem('refresh_token');
        
        if (!refreshToken) {
          // No refresh token available, redirect to login
          localStorage.removeItem('access_token');
          // window.location.href = '/';
          return Promise.reject(error);
        }
        
        // Call your refresh token endpoint with the separate instance
        const response: AxiosResponse<TokenRefreshResponse> = await authApi.post<TokenRefreshResponse>(
          '/auth/refresh',
          {
            refresh_token: refreshToken
          } as RefreshTokenRequest
        );
        
        // If successful, update the tokens
        if (response.data && response.data.access_token) {
          localStorage.setItem('access_token', response.data.access_token);
          
          if (response.data.refresh_token) {
            localStorage.setItem('refresh_token', response.data.refresh_token);
          }
          
          // Update the auth header and retry
          originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;
          return api(originalRequest);
        } else {
          // Invalid response format
          throw new Error('Invalid token response');
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        // If refresh fails, clear tokens and redirect
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        // window.location.href = '/';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

export default api;