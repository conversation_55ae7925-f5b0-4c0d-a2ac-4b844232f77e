// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";
import * as echarts from "echarts";
const App: React.FC = () => {
  // Theme colors based on the image
  const theme = {
    background: "#0B1017",
    cardBg: "#151B26",
    primary: "#4A6FFF",
    secondary: "#5D6B98",
    text: {
      primary: "#FFFFFF",
      secondary: "#8B96B9",
      muted: "#5D6B98",
    },
    border: "#1F2738",
    warning: {
      border: "#FFB547",
      bg: "#1F2738",
      text: "#FFB547",
    },
    success: {
      light: "#47CD89",
      dark: "#1F4B3F",
    },
    input: {
      bg: "#151B26",
      border: "#1F2738",
      focus: "#4A6FFF",
    },
  };
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      message: "Your deposit has been confirmed",
      time: "2 hours ago",
      read: false,
    },
    {
      id: 2,
      message: "Daily profit added: +2.5%",
      time: "5 hours ago",
      read: false,
    },
    {
      id: 3,
      message: "New referral joined your network",
      time: "Yesterday",
      read: true,
    },
  ]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [selectedAmount, setSelectedAmount] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isCustomAmount, setIsCustomAmount] = useState(false);
  const depositPackages = [
    {
      name: "Starter Pack",
      value: "25",
      image:
        "https://readdy.ai/api/search-image?query=A%20minimalist%20illustration%20of%20a%20seedling%20or%20small%20plant%20emerging%20from%20soil%2C%20symbolizing%20growth%20and%20beginnings%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=1&orientation=squarish",
    },
    {
      name: "Growth Pack",
      value: "50",
      image:
        "https://readdy.ai/api/search-image?query=A%20modern%20minimalist%20illustration%20of%20ascending%20steps%20or%20staircase%2C%20representing%20progress%20and%20advancement%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=2&orientation=squarish",
    },
    {
      name: "Professional Pack",
      value: "100",
      image:
        "https://readdy.ai/api/search-image?query=A%20sleek%20briefcase%20or%20professional%20tool%20illustration%2C%20symbolizing%20business%20and%20expertise%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=3&orientation=squarish",
    },
    {
      name: "Enterprise Pack",
      value: "200",
      image:
        "https://readdy.ai/api/search-image?query=A%20modern%20building%20or%20skyscraper%20illustration%2C%20representing%20enterprise%20and%20large%20scale%20operations%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=4&orientation=squarish",
    },
  ];
  const [transactionId, setTransactionId] = useState("");
  const [copySuccess, setCopySuccess] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const unreadNotificationsCount = notifications.filter((n) => !n.read).length;
  const adminWalletAddress = "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F";
  const depositOptions = [
    { value: "50", label: "50 USDT" },
    { value: "100", label: "100 USDT" },
    { value: "250", label: "250 USDT" },
    { value: "500", label: "500 USDT" },
    { value: "1000", label: "1000 USDT" },
  ];
  const depositHistory = [
    {
      id: 1,
      date: "Apr 29, 2025 14:32",
      amount: 100,
      txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      status: "Approved",
    },
    {
      id: 2,
      date: "Apr 27, 2025 09:15",
      amount: 250,
      txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      status: "Approved",
    },
    {
      id: 3,
      date: "Apr 25, 2025 18:45",
      amount: 500,
      txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      status: "Pending",
    },
    {
      id: 4,
      date: "Apr 20, 2025 11:20",
      amount: 50,
      txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      status: "Rejected",
    },
    {
      id: 5,
      date: "Apr 15, 2025 16:05",
      amount: 100,
      txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      status: "Approved",
    },
  ];
  const markAllAsRead = () => {
    setNotifications(notifications.map((n) => ({ ...n, read: true })));
  };
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };
  const handleSubmitDeposit = (e: React.FormEvent) => {
    e.preventDefault();
    if (Number(selectedAmount) < 25) {
      return;
    }
    // Handle deposit submission logic here
    alert(
      `Deposit request submitted: ${selectedAmount} USDT with Transaction ID: ${transactionId}`,
    );
    setSelectedAmount("");
    setTransactionId("");
    setIsCustomAmount(false);
  };
  const filteredHistory = depositHistory
    .filter(
      (item) =>
        item.txId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.date.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.amount.toString().includes(searchTerm),
    )
    .sort((a, b) => {
      if (sortBy === "date") {
        return sortDirection === "asc"
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortBy === "amount") {
        return sortDirection === "asc"
          ? a.amount - b.amount
          : b.amount - a.amount;
      }
      return 0;
    });
  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredHistory.length / itemsPerPage);
  const paginatedHistory = filteredHistory.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };
  const totalDeposited = depositHistory
    .filter((item) => item.status === "Approved")
    .reduce((sum, item) => sum + item.amount, 0);
  const currentBalance = 5280.42; // This would come from your actual data
  const usdtRate = 1.0; // This would be fetched from an API
  const renderDeposit = () => {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center mb-6">
          <a
            href="https://readdy.ai/home/<USER>/f497f316-1768-4167-b8d2-93012cb5b7ad"
            data-readdy="true"
            className="text-indigo-600 hover:text-indigo-800 flex items-center mr-4 cursor-pointer"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            <span>Back to Dashboard</span>
          </a>
          <h1 className="text-2xl font-bold text-gray-900">Deposit USDT</h1>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Admin Wallet Section */}
          <div className="lg:col-span-2 bg-[#151C2D] rounded-lg shadow-md p-6 border border-[#1E293B]">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              Admin USDT Wallet (TRC20)
            </h2>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="border border-[#1E293B] rounded-lg p-4 bg-[#0B1221] mb-4 relative">
                  <p className="text-sm font-medium text-gray-800 break-all">
                    {adminWalletAddress}
                  </p>
                  <button
                    onClick={() => copyToClipboard(adminWalletAddress)}
                    className="absolute right-2 top-2 text-indigo-600 hover:text-indigo-800 cursor-pointer"
                  >
                    <i className="fas fa-copy"></i>
                  </button>
                  {copySuccess && (
                    <div className="absolute -top-10 right-0 bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                      Copied to clipboard!
                    </div>
                  )}
                </div>
                <div className="bg-[#1E293B] border-l-4 border-yellow-400 p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        <strong>Important:</strong> Only send USDT via the TRC20
                        network. Other networks are not supported and may result
                        in loss of funds.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="text-sm text-[#94A3B8] mb-4">
                  <p className="mb-2">
                    <strong className="text-white">
                      Current Exchange Rate:
                    </strong>{" "}
                    1 USDT = ${usdtRate.toFixed(2)} USD
                  </p>
                  <p>
                    After sending USDT, please submit your transaction ID in the
                    form below to complete your deposit request.
                  </p>
                </div>
              </div>
              <div className="w-full md:w-48 flex flex-col items-center">
                <div className="bg-white p-2 border border-gray-200 rounded-lg mb-2">
                  <div className="bg-gray-100 p-4 rounded">
                    <img
                      src="https://readdy.ai/api/search-image?query=QR%20code%20for%20cryptocurrency%20wallet%20address%2C%20clean%20minimalist%20design%2C%20high%20contrast%20black%20pattern%20on%20white%20background%2C%20clear%20and%20scannable%2C%20professional%20fintech%20style%2C%20digital%20payment%20technology&width=200&height=200&seq=1&orientation=squarish"
                      alt="USDT Wallet QR Code"
                      className="w-full h-auto"
                    />
                  </div>
                </div>
                <p className="text-xs text-center text-gray-500">
                  Scan to deposit USDT
                </p>
              </div>
            </div>
          </div>
          {/* Summary Section */}
          <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              Summary
            </h2>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-500 mb-1">
                  Total Deposited Amount
                </p>
                <div className="flex items-baseline">
                  <span className="text-2xl font-bold text-gray-900">
                    {totalDeposited}
                  </span>
                  <span className="ml-1 text-sm text-gray-500">USDT</span>
                </div>
                <p className="text-xs text-gray-500">
                  (≈ ${(totalDeposited * usdtRate).toFixed(2)} USD)
                </p>
              </div>
              <div className="border-t border-gray-100 pt-4">
                <p className="text-sm text-gray-500 mb-1">Current Balance</p>
                <div className="flex items-baseline">
                  <span className="text-2xl font-bold text-gray-900">
                    {currentBalance.toFixed(2)}
                  </span>
                  <span className="ml-1 text-sm text-gray-500">USDT</span>
                </div>
                <p className="text-xs text-gray-500">
                  (≈ ${(currentBalance * usdtRate).toFixed(2)} USD)
                </p>
                <div className="flex items-center mt-1 text-green-600 text-xs">
                  <i className="fas fa-arrow-up mr-1"></i>
                  <span>+2.7% from yesterday</span>
                </div>
              </div>
              <div className="border-t border-gray-100 pt-4 text-xs text-gray-500">
                <p>Last updated: Apr 29, 2025, 14:30</p>
              </div>
            </div>
          </div>
        </div>
        {/* Deposit Request Form */}
        <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            Deposit Request Form
          </h2>
          <form onSubmit={handleSubmitDeposit} className="max-w-2xl">
            <div className="mb-3">
              <label
                htmlFor="network"
                className="block text-sm font-medium text-white mb-1"
              >
                Select Network
              </label>
              <select
                id="network"
                className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] mb-4 bg-[#0B1221] text-white"
                defaultValue="trc20"
              >
                <option value="trc20">TRC20 (Recommended)</option>
                <option value="erc20" disabled>
                  ERC20 (Not Supported)
                </option>
                <option value="bep20" disabled>
                  BEP20 (Not Supported)
                </option>
              </select>
              <label
                htmlFor="amount"
                className="block text-sm font-medium text-white mb-1"
              >
                Amount (USDT)
              </label>
              <div className="space-y-2">
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="w-full px-4 py-3 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] mb-2 bg-[#0B1221] text-white flex items-center justify-between"
                  >
                    <div className="flex items-center">
                      {selectedAmount ? (
                        <>
                          <img
                            src={
                              depositPackages.find(
                                (pkg) => pkg.value === selectedAmount,
                              )?.image || ""
                            }
                            alt=""
                            className="w-8 h-8 rounded-full object-cover mr-3"
                          />
                          <div className="flex flex-col items-start">
                            <span className="text-[#94A3B8] text-sm">
                              {depositPackages.find(
                                (pkg) => pkg.value === selectedAmount,
                              )?.name || "Custom Amount"}
                            </span>
                            <span className="text-white font-semibold">
                              {selectedAmount} USDT
                            </span>
                          </div>
                        </>
                      ) : (
                        <span className="text-[#5D6B98]">
                          Select a deposit package
                        </span>
                      )}
                    </div>
                    <i
                      className={`fas fa-chevron-${isDropdownOpen ? "up" : "down"} text-[#5D6B98]`}
                    ></i>
                  </button>
                  {isDropdownOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-[#0B1221] border border-[#1E293B] rounded-lg shadow-lg">
                      <div className="py-2 max-h-60 overflow-y-auto">
                        {depositPackages.map((pkg) => (
                          <button
                            key={pkg.value}
                            type="button"
                            onClick={() => {
                              setSelectedAmount(pkg.value);
                              setIsDropdownOpen(false);
                            }}
                            className="w-full px-4 py-2 flex items-center hover:bg-[#1E293B] transition-colors"
                          >
                            <img
                              src={pkg.image}
                              alt={pkg.name}
                              className="w-8 h-8 rounded-full object-cover mr-3"
                            />
                            <div className="flex flex-col items-start">
                              <span className="text-white">{pkg.name}</span>
                              <span className="text-sm text-[#5D6B98]">
                                {pkg.value} USDT
                              </span>
                            </div>
                          </button>
                        ))}
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedAmount("");
                            setIsDropdownOpen(false);
                          }}
                          className="w-full px-4 py-2 flex items-center hover:bg-[#1E293B] transition-colors"
                        >
                          <div className="w-8 h-8 rounded-full bg-[#1E293B] flex items-center justify-center mr-3">
                            <i className="fas fa-pen-to-square text-[#5D6B98]"></i>
                          </div>
                          <span className="text-white">Manual Entry</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                {selectedAmount === "" && (
                  <div className="relative">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="25"
                        step="1"
                        value={selectedAmount}
                        onChange={(e) => {
                          const value = e.target.value;
                          setSelectedAmount(value);
                        }}
                        placeholder="Enter amount (min. 25 USDT)"
                        className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98]"
                        required
                      />
                      <span className="absolute right-3 text-[#5D6B98] text-sm">
                        USDT
                      </span>
                    </div>
                  </div>
                )}
                {selectedAmount !== "" && !isNaN(Number(selectedAmount)) && (
                  <div className="text-sm text-[#94A3B8]">
                    Approximate value: $
                    {(Number(selectedAmount) * usdtRate).toFixed(2)} USD
                  </div>
                )}
                {selectedAmount !== "" && Number(selectedAmount) < 25 && (
                  <div className="text-sm text-[#DC2626]">
                    <i className="fas fa-exclamation-circle mr-1"></i>
                    Minimum deposit amount is 25 USDT
                  </div>
                )}
              </div>
            </div>
            <div className="mb-4">
              <label
                htmlFor="transactionId"
                className="block text-sm font-medium text-white mb-1"
              >
                Enter Transaction ID
              </label>
              <input
                type="text"
                id="transactionId"
                value={transactionId}
                onChange={(e) => setTransactionId(e.target.value)}
                placeholder="Enter the transaction ID from your wallet"
                className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98]"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                You can find the transaction ID in your wallet after sending
                USDT to the admin wallet address.
              </p>
            </div>
            <div className="bg-[#1E293B] border-l-4 border-yellow-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Please ensure you enter the correct transaction ID.
                    Incorrect IDs may delay the processing of your deposit.
                  </p>
                </div>
              </div>
            </div>
            <button
              type="submit"
              className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white px-6 py-3 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity !rounded-button whitespace-nowrap cursor-pointer shadow-lg"
            >
              Submit Deposit Request
            </button>
          </form>
        </div>
        {/* Deposit Logs Table */}
        <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            Deposit History
          </h2>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Search deposits..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98] text-sm"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="fas fa-search text-gray-400"></i>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-[#0B1017]">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-[#94A3B8] uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort("date")}
                  >
                    <div className="flex items-center">
                      Date & Time
                      {sortBy === "date" && (
                        <i
                          className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                        ></i>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort("amount")}
                  >
                    <div className="flex items-center">
                      Amount (USDT)
                      {sortBy === "amount" && (
                        <i
                          className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                        ></i>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Transaction ID
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-[#151C2D] divide-y divide-[#1E293B]">
                {paginatedHistory.length > 0 ? (
                  paginatedHistory.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        {item.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        {item.amount} USDT
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <span className="truncate max-w-xs">
                            {item.txId.substring(0, 10)}...
                          </span>
                          <button
                            onClick={() => copyToClipboard(item.txId)}
                            className="ml-2 text-indigo-600 hover:text-indigo-800 cursor-pointer"
                          >
                            <i className="fas fa-copy"></i>
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            item.status === "Approved"
                              ? "bg-[#065F46] text-[#10B981]"
                              : item.status === "Pending"
                                ? "bg-[#854D0E] text-[#F59E0B]"
                                : "bg-[#991B1B] text-[#F87171]"
                          }`}
                        >
                          {item.status}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={4}
                      className="px-6 py-4 text-center text-sm text-gray-500"
                    >
                      No deposit history found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-[#94A3B8]">
                Showing{" "}
                <span className="font-medium">
                  {(currentPage - 1) * itemsPerPage + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, filteredHistory.length)}
                </span>{" "}
                of <span className="font-medium">{filteredHistory.length}</span>{" "}
                results
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-md ${currentPage === 1 ? "bg-[#1E293B] text-[#64748B] cursor-not-allowed" : "bg-[#151C2D] text-[#3B82F6] hover:bg-[#1E293B] cursor-pointer"} border border-[#1E293B]`}
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-md ${currentPage === totalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-indigo-600 hover:bg-indigo-50 cursor-pointer"} border border-gray-300`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };
  return (
    <div className="min-h-screen bg-[#0B1221] p-6 text-white">
      <div className="flex items-center mb-4">
        <h1 className="text-2xl font-bold text-gray-900">Deposit USDT</h1>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Admin Wallet Section */}
        <div className="lg:col-span-2 bg-[#151C2D] rounded-lg shadow-md p-6 border border-[#1E293B]">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">
            Admin USDT Wallet (TRC20)
          </h2>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="border border-[#1E293B] rounded-lg p-4 bg-[#0B1221] mb-4 relative">
                <p className="text-sm font-medium text-gray-800 break-all">
                  {adminWalletAddress}
                </p>
                <button
                  onClick={() => copyToClipboard(adminWalletAddress)}
                  className="absolute right-2 top-2 text-indigo-600 hover:text-indigo-800 cursor-pointer"
                >
                  <i className="fas fa-copy"></i>
                </button>
                {copySuccess && (
                  <div className="absolute -top-10 right-0 bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    Copied to clipboard!
                  </div>
                )}
              </div>
              <div className="bg-[#1E293B] border-l-4 border-yellow-400 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      <strong>Important:</strong> Only send USDT via the TRC20
                      network. Other networks are not supported and may result
                      in loss of funds.
                    </p>
                  </div>
                </div>
              </div>
              <div className="text-sm text-[#94A3B8] mb-4">
                <p className="mb-2">
                  <strong className="text-white">Current Exchange Rate:</strong>{" "}
                  1 USDT = ${usdtRate.toFixed(2)} USD
                </p>
                <p>
                  After sending USDT, please submit your transaction ID in the
                  form below to complete your deposit request.
                </p>
              </div>
            </div>
            <div className="w-full md:w-48 flex flex-col items-center">
              <div className="bg-white p-2 border border-gray-200 rounded-lg mb-2">
                <div className="bg-gray-100 p-4 rounded">
                  <img
                    src="https://readdy.ai/api/search-image?query=QR%20code%20for%20cryptocurrency%20wallet%20address%2C%20clean%20minimalist%20design%2C%20high%20contrast%20black%20pattern%20on%20white%20background%2C%20clear%20and%20scannable%2C%20professional%20fintech%20style%2C%20digital%20payment%20technology&width=200&height=200&seq=1&orientation=squarish"
                    alt="USDT Wallet QR Code"
                    className="w-full h-auto"
                  />
                </div>
              </div>
              <p className="text-xs text-center text-gray-500">
                Scan to deposit USDT
              </p>
            </div>
          </div>
        </div>
        {/* Summary Section */}
        <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Summary</h2>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500 mb-1">
                Total Deposited Amount
              </p>
              <div className="flex items-baseline">
                <span className="text-2xl font-bold text-gray-900">
                  {totalDeposited}
                </span>
                <span className="ml-1 text-sm text-gray-500">USDT</span>
              </div>
              <p className="text-xs text-gray-500">
                (≈ ${(totalDeposited * usdtRate).toFixed(2)} USD)
              </p>
            </div>
            <div className="border-t border-gray-100 pt-4">
              <p className="text-sm text-gray-500 mb-1">Current Balance</p>
              <div className="flex items-baseline">
                <span className="text-2xl font-bold text-gray-900">
                  {currentBalance.toFixed(2)}
                </span>
                <span className="ml-1 text-sm text-gray-500">USDT</span>
              </div>
              <p className="text-xs text-gray-500">
                (≈ ${(currentBalance * usdtRate).toFixed(2)} USD)
              </p>
              <div className="flex items-center mt-1 text-green-600 text-xs">
                <i className="fas fa-arrow-up mr-1"></i>
                <span>+2.7% from yesterday</span>
              </div>
            </div>
            <div className="border-t border-gray-100 pt-4 text-xs text-gray-500">
              <p>Last updated: Apr 29, 2025, 14:30</p>
            </div>
          </div>
        </div>
      </div>
      {/* Deposit Request Form */}
      <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">
          Deposit Request Form
        </h2>
        <form onSubmit={handleSubmitDeposit} className="max-w-2xl">
          <div className="mb-3">
            <label
              htmlFor="network"
              className="block text-sm font-medium text-white mb-1"
            >
              Select Network
            </label>
            <select
              id="network"
              className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] mb-4 bg-[#0B1221] text-white"
              defaultValue="trc20"
            >
              <option value="trc20">TRC20 (Recommended)</option>
              <option value="erc20" disabled>
                ERC20 (Not Supported)
              </option>
              <option value="bep20" disabled>
                BEP20 (Not Supported)
              </option>
            </select>
            <label
              htmlFor="amount"
              className="block text-sm font-medium text-white mb-1"
            >
              Amount (USDT)
            </label>
            <div className="space-y-2">
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full px-4 py-3 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] mb-2 bg-[#0B1221] text-white flex items-center justify-between"
                >
                  <div className="flex items-center">
                    {selectedAmount ? (
                      <>
                        <img
                          src={
                            depositPackages.find(
                              (pkg) => pkg.value === selectedAmount,
                            )?.image || ""
                          }
                          alt=""
                          className="w-8 h-8 rounded-full object-cover mr-3"
                        />
                        <div className="flex flex-col items-start">
                          <span className="text-[#94A3B8] text-sm">
                            {depositPackages.find(
                              (pkg) => pkg.value === selectedAmount,
                            )?.name || "Custom Amount"}
                          </span>
                          <span className="text-white font-semibold">
                            {selectedAmount} USDT
                          </span>
                        </div>
                      </>
                    ) : (
                      <span className="text-[#5D6B98]">
                        Select a deposit package
                      </span>
                    )}
                  </div>
                  <i
                    className={`fas fa-chevron-${isDropdownOpen ? "up" : "down"} text-[#5D6B98]`}
                  ></i>
                </button>
                {isDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-[#0B1221] border border-[#1E293B] rounded-lg shadow-lg">
                    <div className="py-2 max-h-60 overflow-y-auto">
                      {depositPackages.map((pkg) => (
                        <button
                          key={pkg.value}
                          type="button"
                          onClick={() => {
                            setSelectedAmount(pkg.value);
                            setIsDropdownOpen(false);
                          }}
                          className="w-full px-4 py-2 flex items-center hover:bg-[#1E293B] transition-colors"
                        >
                          <img
                            src={pkg.image}
                            alt={pkg.name}
                            className="w-8 h-8 rounded-full object-cover mr-3"
                          />
                          <div className="flex flex-col items-start">
                            <span className="text-white">{pkg.name}</span>
                            <span className="text-sm text-[#5D6B98]">
                              {pkg.value} USDT
                            </span>
                          </div>
                        </button>
                      ))}
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedAmount("");
                          setIsDropdownOpen(false);
                        }}
                        className="w-full px-4 py-2 flex items-center hover:bg-[#1E293B] transition-colors"
                      >
                        <div className="w-8 h-8 rounded-full bg-[#1E293B] flex items-center justify-center mr-3">
                          <i className="fas fa-pen-to-square text-[#5D6B98]"></i>
                        </div>
                        <span className="text-white">Manual Entry</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
              {selectedAmount === "" && (
                <div className="relative">
                  <div className="flex items-center">
                    <input
                      type="number"
                      min="25"
                      step="1"
                      value={selectedAmount}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSelectedAmount(value);
                      }}
                      placeholder="Enter amount (min. 25 USDT)"
                      className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98]"
                      required
                    />
                    <span className="absolute right-3 text-[#5D6B98] text-sm">
                      USDT
                    </span>
                  </div>
                </div>
              )}
              {selectedAmount !== "" && !isNaN(Number(selectedAmount)) && (
                <div className="text-sm text-[#94A3B8]">
                  Approximate value: $
                  {(Number(selectedAmount) * usdtRate).toFixed(2)} USD
                </div>
              )}
              {selectedAmount !== "" && Number(selectedAmount) < 25 && (
                <div className="text-sm text-[#DC2626]">
                  <i className="fas fa-exclamation-circle mr-1"></i>
                  Minimum deposit amount is 25 USDT
                </div>
              )}
            </div>
          </div>
          <div className="mb-4">
            <label
              htmlFor="transactionId"
              className="block text-sm font-medium text-white mb-1"
            >
              Enter Transaction ID
            </label>
            <input
              type="text"
              id="transactionId"
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Enter the transaction ID from your wallet"
              className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98]"
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              You can find the transaction ID in your wallet after sending USDT
              to the admin wallet address.
            </p>
          </div>
          <div className="bg-[#1E293B] border-l-4 border-yellow-400 p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="fas fa-exclamation-triangle text-yellow-400"></i>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Please ensure you enter the correct transaction ID. Incorrect
                  IDs may delay the processing of your deposit.
                </p>
              </div>
            </div>
          </div>
          <button
            type="submit"
            className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white px-6 py-3 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity !rounded-button whitespace-nowrap cursor-pointer shadow-lg"
          >
            Submit Deposit Request
          </button>
        </form>
      </div>
      {/* Deposit Logs Table */}
      <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">
          Deposit History
        </h2>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Search deposits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98] text-sm"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="fas fa-search text-gray-400"></i>
            </div>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#0B1017]">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-[#94A3B8] uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("date")}
                >
                  <div className="flex items-center">
                    Date & Time
                    {sortBy === "date" && (
                      <i
                        className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                      ></i>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("amount")}
                >
                  <div className="flex items-center">
                    Amount (USDT)
                    {sortBy === "amount" && (
                      <i
                        className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                      ></i>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Transaction ID
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-[#151C2D] divide-y divide-[#1E293B]">
              {paginatedHistory.length > 0 ? (
                paginatedHistory.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {item.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {item.amount} USDT
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <span className="truncate max-w-xs">
                          {item.txId.substring(0, 10)}...
                        </span>
                        <button
                          onClick={() => copyToClipboard(item.txId)}
                          className="ml-2 text-indigo-600 hover:text-indigo-800 cursor-pointer"
                        >
                          <i className="fas fa-copy"></i>
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.status === "Approved"
                            ? "bg-[#065F46] text-[#10B981]"
                            : item.status === "Pending"
                              ? "bg-[#854D0E] text-[#F59E0B]"
                              : "bg-[#991B1B] text-[#F87171]"
                        }`}
                      >
                        {item.status}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={4}
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No deposit history found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-[#94A3B8]">
              Showing{" "}
              <span className="font-medium">
                {(currentPage - 1) * itemsPerPage + 1}
              </span>{" "}
              to{" "}
              <span className="font-medium">
                {Math.min(currentPage * itemsPerPage, filteredHistory.length)}
              </span>{" "}
              of <span className="font-medium">{filteredHistory.length}</span>{" "}
              results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md ${currentPage === 1 ? "bg-[#1E293B] text-[#64748B] cursor-not-allowed" : "bg-[#151C2D] text-[#3B82F6] hover:bg-[#1E293B] cursor-pointer"} border border-[#1E293B]`}
              >
                Previous
              </button>
              <button
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md ${currentPage === totalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-indigo-600 hover:bg-indigo-50 cursor-pointer"} border border-gray-300`}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
export default App;
