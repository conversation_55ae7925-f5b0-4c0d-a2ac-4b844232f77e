import { CloudUpload } from 'lucide-react'
import React, { useState, useEffect, ChangeEvent } from 'react'
import { product } from '../../../../user/sub-pages/product/product.model'



// Product Form Component (Create/Edit)
interface ProductFormProps {
  productId?: number // If provided, component will be in edit mode
  initialData?: Partial<product>
  onSubmit: (data: FormData, isEdit: boolean) => Promise<void>
  onCancel?: () => void
  loading?: boolean
}

const ProductForm: React.FC<ProductFormProps> = ({
  productId,
  initialData,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [productName, setProductName] = useState<string>(initialData?.name || '')
  const [productDescription, setProductDescription] = useState<string>(initialData?.description || '')
  const [productImage, setProductImage] = useState<File | null>(null)
  const [currentImageUrl, setCurrentImageUrl] = useState<string>(initialData?.image_url || '')

  const isEditMode = !!productId

  useEffect(() => {
    if (initialData) {
      setProductName(initialData.name || '')
      setProductDescription(initialData.description || '')
      setCurrentImageUrl(initialData.image_url || '')
    }
  }, [initialData])

  const handleSubmit = async (): Promise<void> => {
    const formData = new FormData()
    formData.append('name', productName)
    formData.append('description', productDescription)
    
    if (productImage) {
      formData.append('image', productImage)
    }

    if (isEditMode && productId) {
      formData.append('id', productId.toString())
    }

    try {
      await onSubmit(formData, isEditMode)
      
      // Reset form only if creating new product
      if (!isEditMode) {
        setProductName('')
        setProductDescription('')
        setProductImage(null)
        setCurrentImageUrl('')
      }
    } catch (error) {
      console.error('Error submitting product:', error)
    }
  }

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0]
    if (file) {
      setProductImage(file)
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      setCurrentImageUrl(previewUrl)
    }
  }

  const handleCancel = () => {
    if (isEditMode) {
      // Reset to initial values
      setProductName(initialData?.name || '')
      setProductDescription(initialData?.description || '')
      setProductImage(null)
      setCurrentImageUrl(initialData?.image_url || '')
    } else {
      // Clear form
      setProductName('')
      setProductDescription('')
      setProductImage(null)
      setCurrentImageUrl('')
    }
    onCancel?.()
  }

  return (
    <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700">
      <div className="p-6 border-b border-gray-700">
        <h3 className="text-xl font-bold text-white mb-6">
          {isEditMode ? 'Edit Product' : 'Add New Product'}
        </h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Product Name
              </label>
              <input
                type="text"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                placeholder="Enter product name"
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
                disabled={loading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Product Image
              </label>
              <div className="flex items-center">
                <div className="flex-1">
                  <div className="relative border border-gray-600 rounded-lg px-4 py-3 bg-gray-700">
                    <input
                      type="file"
                      onChange={handleImageChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      accept="image/*"
                      disabled={loading}
                    />
                    <div className="flex items-center text-gray-400 text-sm">
                      <CloudUpload className="mr-2 w-5 h-5 text-gray-300" />
                      <span>
                        {productImage ? productImage.name : 
                         currentImageUrl ? 'Current image selected' : 
                         'Choose file or drag & drop'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              {currentImageUrl && (
                <div className="mt-2">
                  <img 
                    src={currentImageUrl} 
                    alt="Preview" 
                    className="h-20 w-20 object-cover rounded-lg border border-gray-600"
                  />
                </div>
              )}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={productDescription}
              onChange={(e) => setProductDescription(e.target.value)}
              placeholder="Enter product description"
              rows={4}
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm resize-none"
              disabled={loading}
            />
          </div>
          <div className="flex justify-end space-x-3">
            {isEditMode && onCancel && (
              <button
                type="button"
                onClick={handleCancel}
                disabled={loading}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors whitespace-nowrap disabled:opacity-50"
              >
                Cancel
              </button>
            )}
            <button
              type="button"
              onClick={handleSubmit}
              disabled={loading || !productName.trim() || !productDescription.trim()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors whitespace-nowrap disabled:opacity-50"
            >
              {loading ? 'Processing...' : isEditMode ? 'Update Product' : 'Add Product'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductForm