import { CloudUpload, ArrowLeft } from 'lucide-react'
import React, { useState, useEffect, ChangeEvent } from 'react'
import { product } from '../../../../user/sub-pages/product/product.model'

// Product Form Component (Create/Edit)
interface ProductFormProps {
  productId?: number // If provided, component will be in edit mode
  initialData?: Partial<product>
  onSubmit: (data: FormData, isEdit: boolean) => Promise<void>
  onCancel?: () => void
  loading?: boolean
  isEdit?: boolean // Explicit prop to determine if in edit mode
}

const ProductForm: React.FC<ProductFormProps> = ({
  productId,
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  isEdit = false
}) => {
  const [productName, setProductName] = useState<string>(initialData?.name || '')
  const [productDescription, setProductDescription] = useState<string>(initialData?.description || '')
  const [productImage, setProductImage] = useState<File | null>(null)
  const [currentImageUrl, setCurrentImageUrl] = useState<string>(initialData?.image_url || '')

  const isEditMode = isEdit || !!productId

  useEffect(() => {
    if (initialData) {
      setProductName(initialData.name || '')
      setProductDescription(initialData.description || '')
      setCurrentImageUrl(initialData.image_url || '')
    }
  }, [initialData])

  const handleSubmit = async (): Promise<void> => {
    const formData = new FormData()
    formData.append('name', productName)
    formData.append('description', productDescription)

    if (productImage) {
      formData.append('image', productImage)
    }

    if (isEditMode && productId) {
      formData.append('id', productId.toString())
    }

    try {
      await onSubmit(formData, isEditMode)

      // Reset form only if creating new product
      if (!isEditMode) {
        setProductName('')
        setProductDescription('')
        setProductImage(null)
        setCurrentImageUrl('')
      }
    } catch (error) {
      console.error('Error submitting product:', error)
    }
  }

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0]
    if (file) {
      setProductImage(file)
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      setCurrentImageUrl(previewUrl)
    }
  }

  const handleCancel = () => {
    if (isEditMode) {
      // Reset to initial values
      setProductName(initialData?.name || '')
      setProductDescription(initialData?.description || '')
      setProductImage(null)
      setCurrentImageUrl(initialData?.image_url || '')
    } else {
      // Clear form
      setProductName('')
      setProductDescription('')
      setProductImage(null)
      setCurrentImageUrl('')
    }
    onCancel?.()
  }

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-2xl border border-gray-700 overflow-hidden">
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center">
            {isEditMode ? (
              <>
                <span className="bg-yellow-500 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold mr-3">EDIT</span>
                Edit Product
              </>
            ) : (
              <>
                <span className="bg-green-500 text-green-900 px-2 py-1 rounded-full text-xs font-semibold mr-3">NEW</span>
                Add New Product
              </>
            )}
          </h3>
          {onCancel && (
            <button
              onClick={onCancel}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 backdrop-blur-sm"
              disabled={loading}
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to List</span>
            </button>
          )}
        </div>
      </div>
      <div className="p-6">
        <div className="space-y-6">
          {/* Product Name Section */}
          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-200 mb-3">
              Product Name <span className="text-red-400">*</span>
            </label>
            <input
              type="text"
              value={productName}
              onChange={(e) => setProductName(e.target.value)}
              placeholder="Enter product name (e.g., Fresh Organic Avocados)"
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm transition-all duration-200 hover:border-gray-500 shadow-sm"
              disabled={loading}
            />
          </div>

          {/* Product Image Section */}
          <div className="space-y-3">
            <label className="block text-sm font-semibold text-gray-200 mb-3">
              Product Image <span className="text-red-400">*</span>
            </label>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Upload Area */}
              <div className="space-y-3">
                <div className="relative border-2 border-dashed border-gray-600 rounded-xl px-6 py-8 bg-gray-700 hover:border-blue-500 hover:bg-gray-650 transition-all duration-300 group cursor-pointer">
                  <input
                    type="file"
                    onChange={handleImageChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept="image/*"
                    disabled={loading}
                  />
                  <div className="flex flex-col items-center text-center">
                    <div className="mb-4 p-3 bg-gray-600 rounded-full group-hover:bg-blue-600 transition-colors duration-300">
                      <CloudUpload className="w-8 h-8 text-gray-300 group-hover:text-white transition-colors" />
                    </div>
                    <div className="space-y-2">
                      {productImage ? (
                        <div className="space-y-1">
                          <p className="text-green-400 font-semibold text-sm">{productImage.name}</p>
                          <p className="text-xs text-gray-400">File selected successfully</p>
                        </div>
                      ) : currentImageUrl ? (
                        <div className="space-y-1">
                          <p className="text-blue-400 font-semibold text-sm">Current image loaded</p>
                          <p className="text-xs text-gray-400">Click to change image</p>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <p className="text-gray-300 font-medium">Drop your image here</p>
                          <p className="text-sm text-gray-400">or click to browse</p>
                          <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Preview Area */}
              {currentImageUrl && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-300">Image Preview</h4>
                  <div className="relative bg-gray-800 rounded-xl p-4 border border-gray-600">
                    <div className="relative inline-block">
                      <img
                        src={currentImageUrl}
                        alt="Product Preview"
                        className="w-full h-32 object-cover rounded-lg border border-gray-500 shadow-lg"
                      />
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full w-7 h-7 flex items-center justify-center text-xs font-bold shadow-lg">
                        ✓
                      </div>
                    </div>
                    <div className="mt-3 text-center">
                      <p className="text-xs text-gray-400">Preview looks good!</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Description Section */}
          <div className="space-y-3">
            <label className="block text-sm font-semibold text-gray-200 mb-3">
              Product Description <span className="text-red-400">*</span>
            </label>
            <div className="relative">
              <textarea
                value={productDescription}
                onChange={(e) => setProductDescription(e.target.value)}
                placeholder="Describe your product in detail. Include key features, benefits, and what makes it special..."
                rows={4}
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm resize-none transition-all duration-200 hover:border-gray-500 shadow-sm"
                disabled={loading}
              />
              <div className="absolute bottom-3 right-3 text-xs text-gray-500">
                {productDescription.length}/500
              </div>
            </div>
          </div>
          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pt-6 border-t border-gray-700">
            {/* Validation Status */}
            <div className="flex items-center space-x-2">
              {productName.trim() && productDescription.trim() ? (
                <div className="flex items-center text-green-400 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Form is ready to submit
                </div>
              ) : (
                <div className="flex items-center text-yellow-400 text-sm">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                  Please fill all required fields
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleSubmit}
                disabled={loading || !productName.trim() || !productDescription.trim()}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-all duration-200 whitespace-nowrap disabled:opacity-50 shadow-lg disabled:from-gray-600 disabled:to-gray-700"
              >
                {loading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isEditMode ? 'Updating...' : 'Creating...'}
                  </span>
                ) : (
                  <span className="flex items-center">
                    {isEditMode ? (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Update Product
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Product
                      </>
                    )}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductForm