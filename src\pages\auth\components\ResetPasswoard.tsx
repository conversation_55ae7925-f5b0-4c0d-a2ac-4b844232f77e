import React, { useState, useEffect } from "react";
import { X, KeyRound, Loader } from "lucide-react";

interface ResetPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialEmail?: string; // Add prop for initial email
}

interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({ 
  isOpen, 
  onClose, 
  initialEmail = "" // Default to empty string if not provided
}) => {
  const [resetStep, setResetStep] = useState(1);
  const [resetEmail, setResetEmail] = useState("");
  const [resetOtp, setResetOtp] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [resetError, setResetError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Set initial email when modal opens
  useEffect(() => {
    if (isOpen && initialEmail) {
      setResetEmail(initialEmail);
    }
  }, [isOpen, initialEmail]);

  // API service methods
  const sendOtpToEmail = async (email: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await fetch('/api/auth/send-reset-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      const data = await response.json();
      return {
        success: response.ok,
        message: data.message || (response.ok ? 'OTP sent successfully' : 'Failed to send OTP')
      };
    } catch (error) {
      console.error('Error sending OTP:', error);
      return {
        success: false,
        message: 'Network error. Please try again.'
      };
    }
  };

  const verifyOtp = async (email: string, otp: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await fetch('/api/auth/verify-reset-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp }),
      });
      
      const data = await response.json();
      return {
        success: response.ok,
        message: data.message || (response.ok ? 'OTP verified successfully' : 'Invalid OTP')
      };
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return {
        success: false,
        message: 'Network error. Please try again.'
      };
    }
  };

  const resetPassword = async (email: string, otp: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp, newPassword }),
      });
      
      const data = await response.json();
      return {
        success: response.ok,
        message: data.message || (response.ok ? 'Password reset successfully' : 'Failed to reset password')
      };
    } catch (error) {
      console.error('Error resetting password:', error);
      return {
        success: false,
        message: 'Network error. Please try again.'
      };
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string): string | null => {
    if (password.length < 8) {
      return "Password must be at least 8 characters long";
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return "Password must contain at least one lowercase letter";
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return "Password must contain at least one uppercase letter";
    }
    if (!/(?=.*\d)/.test(password)) {
      return "Password must contain at least one number";
    }
    return null;
  };

  const handleResetSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetError("");
    setSuccessMessage("");
    setIsLoading(true);

    try {
      if (resetStep === 1) {
        // Step 1: Send OTP
        if (!resetEmail) {
          setResetError("Please enter your email");
          return;
        }

        if (!validateEmail(resetEmail)) {
          setResetError("Please enter a valid email address");
          return;
        }

        const result = await sendOtpToEmail(resetEmail);
        
        if (result.success) {
          setSuccessMessage("OTP sent to your email successfully!");
          setTimeout(() => {
            setResetStep(2);
            setSuccessMessage("");
          }, 1500);
        } else {
          setResetError(result.message);
        }

      } else if (resetStep === 2) {
        // Step 2: Verify OTP
        if (!resetOtp || resetOtp.length !== 6) {
          setResetError("Please enter a valid 6-digit OTP");
          return;
        }

        const result = await verifyOtp(resetEmail, resetOtp);
        
        if (result.success) {
          setSuccessMessage("OTP verified successfully!");
          setTimeout(() => {
            setResetStep(3);
            setSuccessMessage("");
          }, 1500);
        } else {
          setResetError(result.message);
        }

      } else if (resetStep === 3) {
        // Step 3: Reset Password
        if (!newPassword || !confirmPassword) {
          setResetError("Please fill in all fields");
          return;
        }

        if (newPassword !== confirmPassword) {
          setResetError("Passwords do not match");
          return;
        }

        const passwordError = validatePassword(newPassword);
        if (passwordError) {
          setResetError(passwordError);
          return;
        }

        const result = await resetPassword(resetEmail, resetOtp, newPassword);
        
        if (result.success) {
          setSuccessMessage("Password updated successfully!");
          setTimeout(() => {
            handleClose();
          }, 2000);
        } else {
          setResetError(result.message);
        }
      }
    } catch (error) {
      setResetError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setResetStep(1);
    setResetEmail("");
    setResetOtp("");
    setNewPassword("");
    setConfirmPassword("");
    setResetError("");
    setSuccessMessage("");
    setIsLoading(false);
    onClose();
  };

  const handleResendOtp = async () => {
    if (!resetEmail) return;
    
    setIsLoading(true);
    setResetError("");
    
    const result = await sendOtpToEmail(resetEmail);
    
    if (result.success) {
      setSuccessMessage("OTP resent successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } else {
      setResetError(result.message);
    }
    
    setIsLoading(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[#1A1F2E] p-8 rounded-xl max-w-md w-full relative mx-4">
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 text-gray-400 hover:text-white transition-colors"
          disabled={isLoading}
        >
          <X size={20} />
        </button>

        <div className="flex items-center justify-center mb-6">
          <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
            <KeyRound className="text-white" size={24} />
          </div>
        </div>

        <h3 className="text-xl font-bold text-white text-center mb-6">
          {resetStep === 1
            ? "Reset Password"
            : resetStep === 2
              ? "Enter OTP"
              : "Create New Password"}
        </h3>

        <form onSubmit={handleResetSubmit} className="space-y-4">
          {resetStep === 1 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Email Address
              </label>
              <input
                type="email"
                value={resetEmail}
                onChange={(e) => setResetEmail(e.target.value)}
                className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                placeholder="Enter your email"
                disabled={isLoading}
              />
            </div>
          )}

          {resetStep === 2 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Enter OTP
              </label>
              <input
                type="text"
                maxLength={6}
                value={resetOtp}
                onChange={(e) =>
                  setResetOtp(e.target.value.replace(/\D/g, ""))
                }
                className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors text-center text-lg tracking-widest"
                placeholder="000000"
                disabled={isLoading}
              />
              <div className="mt-2 text-center">
                <button
                  type="button"
                  onClick={handleResendOtp}
                  disabled={isLoading}
                  className="text-sm text-blue-600 hover:text-blue-500 transition-colors disabled:opacity-50"
                >
                  Didn't receive OTP? Resend
                </button>
              </div>
            </div>
          )}

          {resetStep === 3 && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  New Password
                </label>
                <input
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                  placeholder="Enter new password"
                  disabled={isLoading}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Confirm Password
                </label>
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                  placeholder="Confirm new password"
                  disabled={isLoading}
                />
              </div>
              <div className="text-xs text-gray-400 mt-2">
                Password must contain at least 8 characters with uppercase, lowercase, and numbers.
              </div>
            </>
          )}

          {resetError && (
            <div className="bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-md text-sm">
              {resetError}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-900/20 border border-green-800 text-green-400 px-4 py-3 rounded-md text-sm">
              {successMessage}
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-3 px-4 rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isLoading ? (
              <span className="flex items-center">
                <Loader className="animate-spin mr-2" size={16} />
                Processing...
              </span>
            ) : (
              <>
                {resetStep === 1
                  ? "Send OTP"
                  : resetStep === 2
                    ? "Verify OTP"
                    : "Update Password"}
              </>
            )}
          </button>
        </form>

        <div className="mt-4 text-center text-xs text-gray-400">
          Step {resetStep} of 3
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordModal;