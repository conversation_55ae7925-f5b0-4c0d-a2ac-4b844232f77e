// // Admin Users Management Models and Interfaces

// import { ApiResponse } from '../../../../api/axiosInstance';

// // ==================== BASE INTERFACES ====================

// // Base pagination interface
// export interface PaginationParams {
//   skip?: number;
//   limit?: number;
// }

// export interface PaginationResponse<T> {
//   total: number;
//   skip: number;
//   limit: number;
//   has_next: boolean;
//   has_prev: boolean;
//   items: T[];
// }

// // ==================== ENUMS ====================

// export type UserRank = 'bronze' | 'silver' | 'gold' | 'platinum';
// export type KycStatus = 'pending' | 'verified' | 'rejected';

// // ==================== MAIN INTERFACES ====================

// // User interface
// export interface User {
//   id: number;
//   name: string;
//   dob: string;
//   nationality: string;
//   country_of_residence: string;
//   preferred_currency: string;
//   address: string;
//   country_code: string;
//   phone: string;
//   email: string;
//   referral_code: string;
//   referrer_id: number | null;
//   national_id: string;
//   passport: string;
//   rank: UserRank;
//   is_admin: boolean;
//   is_active: boolean;
//   is_kyc_verified: boolean;
//   kyc_status: KycStatus;
//   profile_picture: string | null;
//   created_at: string;
//   updated_at: string;
// }

// // ==================== REQUEST INTERFACES ====================

// // Create user request interface
// export interface CreateUserRequest {
//   name: string;
//   dob: string;
//   nationality: string;
//   country_of_residence: string;
//   preferred_currency: string;
//   address: string;
//   country_code: string;
//   phone: string;
//   email: string;
//   password: string;
//   referral_code?: string;
//   referrer_id?: number;
//   national_id: string;
//   passport: string;
// }

// // Update user request interface
// export interface UpdateUserRequest {
//   name?: string;
//   dob?: string;
//   nationality?: string;
//   country_of_residence?: string;
//   preferred_currency?: string;
//   address?: string;
//   country_code?: string;
//   phone?: string;
//   email?: string;
//   referral_code?: string;
//   referrer_id?: number;
//   national_id?: string;
//   passport?: string;
//   rank?: UserRank;
//   is_admin?: boolean;
//   is_active?: boolean;
//   is_kyc_verified?: boolean;
//   kyc_status?: KycStatus;
//   profile_picture?: string;
// }

// // User filters for search/filtering
// export interface UserFilters extends PaginationParams {
//   search?: string;
//   rank?: UserRank;
//   kyc_status?: KycStatus;
//   is_admin?: boolean;
//   is_active?: boolean;
//   is_kyc_verified?: boolean;
//   nationality?: string;
//   preferred_currency?: string;
//   created_from?: string;
//   created_to?: string;
// }

// // ==================== RESPONSE INTERFACES ====================

// // API Response interfaces
// export interface GetUsersResponse extends ApiResponse<PaginationResponse<User>> {}
// export interface GetUserResponse extends ApiResponse<User> {}
// export interface CreateUserResponse extends ApiResponse<User> {}
// export interface UpdateUserResponse extends ApiResponse<User> {}
// export interface DeleteUserResponse extends ApiResponse<{ message: string }> {}

// // ==================== FORM VALIDATION ====================

// // Form validation errors
// export interface UserFormErrors {
//   name?: string;
//   dob?: string;
//   nationality?: string;
//   country_of_residence?: string;
//   preferred_currency?: string;
//   address?: string;
//   country_code?: string;
//   phone?: string;
//   email?: string;
//   password?: string;
//   referral_code?: string;
//   referrer_id?: string;
//   national_id?: string;
//   passport?: string;
//   rank?: string;
//   kyc_status?: string;
//   general?: string;
// }

// // ==================== STATISTICS & ANALYTICS ====================

// // User statistics interface
// export interface UserStatistics {
//   total_users: number;
//   active_users: number;
//   inactive_users: number;
//   admin_users: number;
//   kyc_verified_users: number;
//   kyc_pending_users: number;
//   kyc_rejected_users: number;
//   users_by_rank: {
//     bronze: number;
//     silver: number;
//     gold: number;
//     platinum: number;
//   };
//   users_by_currency: Record<string, number>;
//   recent_registrations: number;
// }


export interface userList {
  total_items: number
  skip: number
  limit: number
  current_page: number
  total_pages: number
  items: user[]
}

export interface user {
  name: string
  dob: string
  nationality: string
  country_of_residence: string
  preferred_currency: string
  address: string
  country_code: string
  phone: string
  email: string
  national_id: string
  passport: string
  rank: string
  is_admin: boolean
  is_active: boolean
  is_kyc_verified: boolean
  kyc_status: string
  profile_picture: string
  id: number
  referral_code: string
  referrer_id: number
  created_at: string
  date?: string
  status?: string
  statusColor?: "green" | "yellow" | "red"
}

// KYC Status Update Interfaces
export interface KycStatusUpdateRequest {
  user_id: number
  kyc_status: 'pending' | 'verified' | 'rejected'
  rejection_reason?: string
}

export interface KycStatusUpdateResponse {
  success: boolean
  message: string
  data?: user
  error?: string
}

// Rejection Reason Interface
export interface RejectionReason {
  reason: string
  details?: string
}

// Status Color Type
export type StatusColor = "green" | "yellow" | "red";