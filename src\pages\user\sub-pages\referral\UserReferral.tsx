// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect, useCallback } from "react";
import * as echarts from "echarts";
import { getUserReferrals } from "./referral-service";
import { ReferralTreeNode } from "./referral.model";

const UserReferral: React.FC = () => {

  const [copySuccess, setCopySuccess] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");

  // API State - Only for referral tree
  const [referralTreeData, setReferralTreeData] = useState<ReferralTreeNode[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Static data for stats and users (not connected to API)
  const referralStats = {
    total_referrals: 42,
    active_referrals: 28,
    total_earnings: 1250.75,
    conversion_rate: 66.7,
    avg_earnings_per_referral: 44.67,
  };

  const referredUsers = [
    {
      id: 1,
      name: "Alice Johnson",
      email: "<EMAIL>",
      date: "2024-01-15",
      status: "Active" as const,
      earnings: 125.50,
      level: 1,
    },
    {
      id: 2,
      name: "Bob Smith",
      email: "<EMAIL>",
      date: "2024-01-20",
      status: "Active" as const,
      earnings: 89.25,
      level: 1,
    },
    {
      id: 3,
      name: "Carol Davis",
      email: "<EMAIL>",
      date: "2024-01-25",
      status: "Inactive" as const,
      earnings: 45.75,
      level: 2,
    },
    {
      id: 4,
      name: "David Wilson",
      email: "<EMAIL>",
      date: "2024-02-01",
      status: "Active" as const,
      earnings: 156.80,
      level: 1,
    },
    {
      id: 5,
      name: "Eva Brown",
      email: "<EMAIL>",
      date: "2024-02-05",
      status: "Active" as const,
      earnings: 78.90,
      level: 2,
    },
  ];

  const referralLink = "https://tradepro.com/ref/johndoe123";
  const currentUserId = 1; // This should come from auth context

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };
  const filteredUsers = referredUsers
    .filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.date.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.earnings.toString().includes(searchTerm),
    )
    .sort((a, b) => {
      if (sortBy === "date") {
        return sortDirection === "asc"
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortBy === "earnings") {
        return sortDirection === "asc"
          ? a.earnings - b.earnings
          : b.earnings - a.earnings;
      } else if (sortBy === "name") {
        return sortDirection === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }
      return 0;
    });
  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };

  // API Loading Functions - Only for referral tree
  const loadReferralData = async () => {
    setIsLoading(true);
    try {
      console.log('Loading referral tree data for user:', currentUserId);
      // Load referral tree data only
      const treeResponse = await getUserReferrals(currentUserId);
      console.log('API Response:', treeResponse);

      // The API returns an array directly
      if (Array.isArray(treeResponse)) {
        console.log('Processing API response array...');
        setReferralTreeData(treeResponse);
      } else {
        console.log('API response is not an array, setting empty data');
        setReferralTreeData([]);
      }

      // Use static data for stats and users (not connected to API)
      // This keeps the rest of the interface functional
    } catch (error) {
      console.error('Error loading referral tree data:', error);
      // Set empty array on error so tree still renders
      setReferralTreeData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Transform API data to ECharts format
  const transformToTreeData = useCallback((nodes: ReferralTreeNode[]): any => {
    console.log('Transforming tree data:', nodes);
    if (!nodes || nodes.length === 0) {
      console.log('No nodes to transform');
      return [];
    }

    const transformed = nodes.map((node, index) => {
      console.log(`Processing node ${index}:`, node);

      // Use the actual API response structure
      const userId = node.user_id;
      const username = node.name || `User ${userId}`;
      const level = node.level;
      const joinDate = new Date().toLocaleDateString(); // API doesn't provide join date
      const totalDeposit = typeof node.total_deposit === 'number' ? node.total_deposit : 0;
      const commissionEarned = parseFloat(node.commission_to_root) || 0;

      const transformedNode = {
        name: `${username} (Level ${level})`,
        value: [
          joinDate,
          `$${totalDeposit.toFixed(2)}`,
          `$${commissionEarned.toFixed(2)}`
        ],
        children: node.legs && Array.isArray(node.legs) && node.legs.length > 0 ? transformToTreeData(node.legs) : undefined
      };

      console.log(`Transformed node ${index}:`, transformedNode);
      return transformedNode;
    });

    console.log('All transformed data:', transformed);
    return transformed;
  }, []);

  useEffect(() => {
    loadReferralData();
  }, [currentPage, searchTerm]);

  useEffect(() => {
    // Initialize referral tree chart
    const referralTreeElement = document.getElementById("referral-tree");
    if (referralTreeElement) {
      const referralTree = echarts.init(referralTreeElement);
      console.log('Updating chart with referralTreeData:', referralTreeData);

      let treeData;

      if (referralTreeData && referralTreeData.length > 0) {
        // Use the actual API data
        const rootUser = referralTreeData[0]; // First item is the root user
        console.log('Root user from API:', rootUser);

        // Transform the legs (children) of the root user
        const transformedChildren = rootUser.legs && Array.isArray(rootUser.legs) ? transformToTreeData(rootUser.legs) : [];
        console.log('Transformed children for chart:', transformedChildren);

        treeData = {
          name: `${rootUser.name} (Level ${rootUser.level})`,
          value: [
            new Date().toLocaleDateString(),
            `$${rootUser.total_deposit.toFixed(2)}`,
            `$${parseFloat(rootUser.commission_to_root).toFixed(2)}`
          ],
          children: transformedChildren,
        };
      } else {
        // Fallback: use test data if no API data is available
        console.log('No API data available, using test data for demonstration');
        treeData = {
          name: "You (Level 0)",
          value: [new Date().toLocaleDateString(), "$0", `$${referralStats.total_earnings.toFixed(2)}`],
          children: [
            {
              name: "Test User 1 (Level 1)",
              value: [new Date().toLocaleDateString(), "$500.00", "$25.00"],
              children: [
                {
                  name: "Test User 2 (Level 2)",
                  value: [new Date().toLocaleDateString(), "$300.00", "$15.00"],
                }
              ]
            },
            {
              name: "Test User 3 (Level 1)",
              value: [new Date().toLocaleDateString(), "$750.00", "$37.50"],
            }
          ],
        };
      }

      console.log('Final tree data for chart:', treeData);
      const option = {
        animation: true,
        backgroundColor: 'transparent',
        tooltip: {
          trigger: "item",
          backgroundColor: 'rgba(15, 23, 42, 0.95)',
          borderColor: '#475569',
          borderWidth: 1,
          textStyle: {
            color: '#f1f5f9',
            fontSize: 12
          },
          formatter: function (params: any) {
            const value = params.data.value;
            return `
<div style="font-family: system-ui; padding: 8px; color: #f1f5f9;">
<div style="font-weight: 600; color: #60a5fa; margin-bottom: 6px;">${params.data.name}</div>
<div style="font-size: 11px; line-height: 1.4;">
<div style="margin-bottom: 2px;"><span style="color: #94a3b8;">Joined:</span> <span style="color: #e2e8f0;">${value[0]}</span></div>
<div style="margin-bottom: 2px;"><span style="color: #94a3b8;">Total Deposited:</span> <span style="color: #10b981;">${value[1]}</span></div>
<div><span style="color: #94a3b8;">Your Bonus:</span> <span style="color: #f59e0b;">${value[2]}</span></div>
</div>
</div>
`;
          },
        },
        series: [
          {
            type: "tree",
            data: [treeData],
            top: "10%",
            left: "3%",
            bottom: "15%",
            right: "3%",
            orient: "TB",
            symbolSize: 40,
            symbol: "roundRect",
            layout: "orthogonal",
            roam: true,
            label: {
              show: true,
              position: "bottom",
              distance: 15,
              fontSize: 12,
              color: "#ffffff",
              fontWeight: 600,
              backgroundColor: 'rgba(30, 41, 59, 0.95)',
              borderColor: '#60a5fa',
              borderWidth: 1,
              borderRadius: 6,
              padding: [6, 10],
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowBlur: 4,
              shadowOffsetY: 2,
              formatter: function (params: any) {
                const name = params.data.name;
                // Clean up the name and make it more readable
                const cleanName = name.replace(/\s*\(Level \d+\)/, '');

                // Split long names into multiple lines for better readability
                if (cleanName.length > 15) {
                  const words = cleanName.split(' ');
                  if (words.length >= 2) {
                    const firstLine = words.slice(0, Math.ceil(words.length / 2)).join(' ');
                    const secondLine = words.slice(Math.ceil(words.length / 2)).join(' ');
                    return firstLine + '\n' + secondLine;
                  }
                }
                return cleanName;
              },
            },
            leaves: {
              label: {
                show: true,
                position: "bottom",
                distance: 15,
                fontSize: 12,
                color: "#ffffff",
                fontWeight: 600,
                backgroundColor: 'rgba(30, 41, 59, 0.95)',
                borderColor: '#60a5fa',
                borderWidth: 1,
                borderRadius: 6,
                padding: [6, 10],
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 4,
                shadowOffsetY: 2,
                formatter: function (params: any) {
                  const name = params.data.name;
                  // Clean up the name and make it more readable
                  const cleanName = name.replace(/\s*\(Level \d+\)/, '');

                  // Split long names into multiple lines for better readability
                  if (cleanName.length > 15) {
                    const words = cleanName.split(' ');
                    if (words.length >= 2) {
                      const firstLine = words.slice(0, Math.ceil(words.length / 2)).join(' ');
                      const secondLine = words.slice(Math.ceil(words.length / 2)).join(' ');
                      return firstLine + '\n' + secondLine;
                    }
                  }
                  return cleanName;
                },
              },
            },
            emphasis: {
              focus: "descendant",
              scale: 1.1,
              itemStyle: {
                borderColor: '#60a5fa',
                borderWidth: 3,
                shadowColor: 'rgba(96, 165, 250, 0.5)',
                shadowBlur: 10,
              },
              label: {
                color: '#ffffff',
                fontSize: 14,
                fontWeight: 700,
                backgroundColor: 'rgba(96, 165, 250, 0.9)',
                borderColor: '#ffffff',
                borderWidth: 2,
                borderRadius: 8,
                padding: [8, 12],
                shadowColor: 'rgba(0, 0, 0, 0.7)',
                shadowBlur: 6,
                shadowOffsetY: 3,
              }
            },
            expandAndCollapse: true,
            animationDuration: 750,
            animationDurationUpdate: 500,
            initialTreeDepth: 3,
            lineStyle: {
              color: "#60a5fa",
              width: 2,
              curveness: 0.3,
              shadowColor: 'rgba(96, 165, 250, 0.3)',
              shadowBlur: 4,
            },
            itemStyle: {
              color: "#1e293b",
              borderColor: "#60a5fa",
              borderWidth: 2,
              shadowColor: 'rgba(96, 165, 250, 0.2)',
              shadowBlur: 6,
            },
          },
        ],
      };
      referralTree.setOption(option);
      const handleResize = () => {
        referralTree.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        referralTree.dispose();
      };
    }
    // Initialize referral growth chart
    const referralChartElement = document.getElementById(
      "referral-growth-chart",
    );
    if (referralChartElement) {
      const referralChart = echarts.init(referralChartElement);
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
          formatter: "{b}: {c} referrals",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
          axisLine: {
            lineStyle: {
              color: "#ddd",
            },
          },
          axisLabel: {
            color: "#666",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#666",
          },
          splitLine: {
            lineStyle: {
              color: "#eee",
            },
          },
        },
        series: [
          {
            data: [5, 8, 12, 18, 25, 42],
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#4f46e5",
            },
            lineStyle: {
              width: 3,
              color: "#4f46e5",
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(79, 70, 229, 0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(79, 70, 229, 0.1)",
                  },
                ],
              },
            },
          },
        ],
      };
      referralChart.setOption(option);
      const handleResize = () => {
        referralChart.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        referralChart.dispose();
      };
    }
    // Initialize earnings chart
    const earningsChartElement = document.getElementById("earnings-chart");
    if (earningsChartElement) {
      const earningsChart = echarts.init(earningsChartElement);
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
          formatter: "{b}: {c}",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
          axisLine: {
            lineStyle: {
              color: "#ddd",
            },
          },
          axisLabel: {
            color: "#666",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#666",
            formatter: "{value}",
          },
          splitLine: {
            lineStyle: {
              color: "#eee",
            },
          },
        },
        series: [
          {
            data: [120, 210, 350, 480, 750, 1250.75],
            type: "bar",
            barWidth: "60%",
            itemStyle: {
              color: "#10b981",
            },
          },
        ],
      };
      earningsChart.setOption(option);
      const handleResize = () => {
        earningsChart.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        earningsChart.dispose();
      };
    }
  }, [referralStats.total_earnings, referralTreeData, transformToTreeData]);
  return (
    <div className="min-h-screen bg-[#0B1221] text-white responsive-container">
      <main className="min-h-screen">
        <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Referral Program</h1>
            {/* Referral Stats */}
            <div className="responsive-grid gap-4 sm:gap-6">
              {/* Referral Statistics */}
              <div className="lg:col-span-3 mobile-card bg-slate-800 rounded-lg shadow-md p-4 sm:p-6 border border-slate-700">
                <h2 className="text-lg sm:text-xl font-semibold text-white mb-4">
                  Referral Statistics
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-6">
                  <div className="mobile-card bg-indigo-900/50 rounded-lg p-4 border border-indigo-800">
                    <p className="text-sm text-indigo-300 mb-1">
                      Total Referrals
                    </p>
                    <div className="flex items-center">
                      <i className="fas fa-users text-indigo-400 mr-2"></i>
                      <span className="text-xl sm:text-2xl font-bold text-indigo-300">
                        {referralStats.total_referrals}
                      </span>
                    </div>
                  </div>
                  <div className="mobile-card bg-green-900/50 rounded-lg p-4 border border-green-800">
                    <p className="text-sm text-green-300 mb-1">
                      Active Referrals
                    </p>
                    <div className="flex items-center">
                      <i className="fas fa-user-check text-green-400 mr-2"></i>
                      <span className="text-xl sm:text-2xl font-bold text-green-300">
                        {referralStats.active_referrals}
                      </span>
                    </div>
                  </div>
                  <div className="mobile-card bg-purple-900/50 rounded-lg p-4 border border-purple-800">
                    <p className="text-sm text-purple-300 mb-1">
                      Total Earnings
                    </p>
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <i className="fas fa-coins text-purple-400 mr-2"></i>
                        <span className="text-lg sm:text-2xl font-bold text-purple-300">
                          {referralStats.total_earnings.toFixed(2)} USDT
                        </span>
                      </div>
                      <div className="flex items-center mt-1">
                        <i className="fas fa-rupee-sign text-purple-400 mr-2"></i>
                        <span className="text-sm text-purple-400">
                          ≈ ₹{(referralStats.total_earnings * 83.2).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Referral Tree View */}
            <div className="mobile-card bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg shadow-md p-4 sm:p-6 border border-slate-700 mb-4 sm:mb-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 sm:mb-6 space-y-4 lg:space-y-0">
                <div className="min-w-0 flex-1">
                  <h2 className="text-lg sm:text-xl font-semibold text-white mb-2">
                    Your Referral Network
                  </h2>
                  <p className="text-sm text-gray-400">
                    Visualizing your multi-level referral network up to 5 levels deep
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
                      <span className="ml-2 text-sm text-gray-400">Active</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-slate-600"></div>
                      <span className="ml-2 text-sm text-gray-400">Inactive</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="mobile-button px-3 sm:px-4 py-2 text-sm bg-slate-700 border border-slate-600 text-gray-300 rounded-lg hover:bg-slate-600 transition-colors shadow-sm cursor-pointer">
                      <i className="fas fa-expand-arrows-alt mr-1 sm:mr-2"></i>
                      <span className="hidden sm:inline">Expand All</span>
                      <span className="sm:hidden">Expand</span>
                    </button>
                    <button className="mobile-button px-3 sm:px-4 py-2 text-sm bg-slate-700 border border-slate-600 text-gray-300 rounded-lg hover:bg-slate-600 transition-colors shadow-sm cursor-pointer">
                      <i className="fas fa-compress-arrows-alt mr-1 sm:mr-2"></i>
                      <span className="hidden sm:inline">Collapse All</span>
                      <span className="sm:hidden">Collapse</span>
                    </button>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-slate-800 to-slate-700 rounded-lg shadow-lg p-4 border border-slate-600">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <i className="fas fa-layer-group text-blue-400"></i>
                      <select className="px-4 py-2 border border-slate-500 bg-slate-600 text-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm">
                        <option value="all">All Levels</option>
                        <option value="1">Level 1</option>
                        <option value="2">Level 2</option>
                        <option value="3">Level 3</option>
                        <option value="4">Level 4</option>
                        <option value="5">Level 5</option>
                      </select>
                    </div>
                    <div className="flex items-center space-x-2 border-l border-slate-600 pl-4">
                      <button className="p-2 text-gray-300 hover:text-blue-400 hover:bg-slate-600 rounded-lg transition-all duration-200 cursor-pointer">
                        <i className="fas fa-search"></i>
                      </button>
                      <button className="p-2 text-gray-300 hover:text-blue-400 hover:bg-slate-600 rounded-lg transition-all duration-200 cursor-pointer">
                        <i className="fas fa-filter"></i>
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-300 hover:text-green-400 hover:bg-slate-600 rounded-lg transition-all duration-200 cursor-pointer" title="Download Tree">
                      <i className="fas fa-download"></i>
                    </button>
                    <button className="p-2 text-gray-300 hover:text-purple-400 hover:bg-slate-600 rounded-lg transition-all duration-200 cursor-pointer" title="Print Tree">
                      <i className="fas fa-print"></i>
                    </button>
                    <button className="p-2 text-gray-300 hover:text-yellow-400 hover:bg-slate-600 rounded-lg transition-all duration-200 cursor-pointer" title="Fullscreen">
                      <i className="fas fa-expand"></i>
                    </button>
                  </div>
                </div>
                <div
                  id="referral-tree"
                  className="responsive-chart h-[400px] sm:h-[500px] lg:h-[700px] w-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-lg border border-slate-600 shadow-inner"
                  style={{
                    background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
                    boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(96, 165, 250, 0.1)'
                  }}
                ></div>

                {/* Tree Legend and Instructions */}
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-slate-700/50 rounded-lg p-3 border border-slate-600">
                    <h4 className="text-sm font-medium text-gray-200 mb-2 flex items-center">
                      <i className="fas fa-info-circle text-blue-400 mr-2"></i>
                      How to Navigate
                    </h4>
                    <ul className="text-xs text-gray-300 space-y-1">
                      <li>• <span className="text-blue-400">Click</span> on nodes to expand/collapse</li>
                      <li>• <span className="text-green-400">Hover</span> to see detailed information</li>
                      <li>• <span className="text-purple-400">Drag</span> to pan around the tree</li>
                      <li>• <span className="text-yellow-400">Scroll</span> to zoom in/out</li>
                    </ul>
                  </div>
                  <div className="bg-slate-700/50 rounded-lg p-3 border border-slate-600">
                    <h4 className="text-sm font-medium text-gray-200 mb-2 flex items-center">
                      <i className="fas fa-palette text-purple-400 mr-2"></i>
                      Color Legend
                    </h4>
                    <div className="space-y-1 text-xs">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded border-2 border-blue-400 bg-slate-800 mr-2"></div>
                        <span className="text-gray-300">Active Referrals</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded border-2 border-gray-500 bg-slate-700 mr-2"></div>
                        <span className="text-gray-300">Inactive Referrals</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-8 h-0.5 bg-blue-400 mr-2"></div>
                        <span className="text-gray-300">Connection Lines</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Referral Link Section */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <h2 className="text-lg font-semibold text-white mb-4">
                Your Referral Link
              </h2>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <div className="mb-4">
                    <p className="text-sm text-gray-400 mb-2">
                      Share this link with friends to earn rewards:
                    </p>
                    <div className="flex items-center">
                      <input
                        type="text"
                        value={referralLink}
                        readOnly
                        className="flex-1 px-4 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-l-lg text-sm focus:ring-indigo-500 focus:border-indigo-500"
                      />
                      <button
                        onClick={() => copyToClipboard(referralLink)}
                        className="bg-indigo-600 text-white px-4 py-2 rounded-r-lg hover:bg-indigo-700 transition-colors cursor-pointer"
                      >
                        {copySuccess ? (
                          <i className="fas fa-check mr-1"></i>
                        ) : (
                          <i className="fas fa-copy mr-1"></i>
                        )}
                        {copySuccess ? "Copied!" : "Copy"}
                      </button>
                    </div>
                  </div>
                  <div className="mb-6">
                    <p className="text-sm text-gray-400 mb-3">Share via:</p>
                    <div className="flex space-x-3">
                      <button className="bg-[#1DA1F2] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button className="bg-[#4267B2] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button className="bg-[#0A66C2] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-linkedin-in"></i>
                      </button>
                      <button className="bg-[#25D366] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-whatsapp"></i>
                      </button>
                      <button className="bg-[#EA4335] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fas fa-envelope"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="bg-slate-700 p-2 border border-slate-600 rounded-lg shadow-sm mb-3">
                    <div className="bg-white p-1 rounded-lg mb-2">
                      <img
                        src="https://readdy.ai/api/search-image?query=QR%20code%20with%20modern%20design%2C%20clean%20black%20and%20white%20pattern%20on%20white%20background%2C%20high%20resolution%2C%20minimalist%20style%2C%20professional%20looking%20QR%20code%20with%20small%20logo%20in%20center&width=150&height=150&seq=1&orientation=squarish"
                        alt="Referral QR Code"
                        className="h-36 w-36 object-cover"
                      />
                    </div>
                  </div>
                  <button
                    onClick={() => setShowQRCode(!showQRCode)}
                    className="text-sm text-indigo-400 hover:text-indigo-300 cursor-pointer"
                  >
                    <i className="fas fa-download mr-1"></i> Download QR Code
                  </button>
                </div>
              </div>
            </div>
            {/* Rewards Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-blue-900 to-indigo-900 rounded-lg shadow-md p-6 border border-blue-800">
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-gray-800 rounded-full p-3">
                    <i className="fas fa-chart-network text-blue-400 text-xl"></i>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-lg font-semibold text-white mb-2">
                      Multi-Level Referral Program
                    </h2>
                    <p className="text-blue-200 mb-4">
                      Earn up to 19.5% of deposits across 5 levels! 💰
                    </p>
                    <div className="space-y-3">
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 1</h3>
                          <span className="text-lg font-bold text-white">
                            7%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Direct referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 2</h3>
                          <span className="text-lg font-bold text-white">
                            5%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Second-tier referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 3</h3>
                          <span className="text-lg font-bold text-white">
                            4%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Third-tier referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 4</h3>
                          <span className="text-lg font-bold text-white">
                            2.5%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Fourth-tier referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 5</h3>
                          <span className="text-lg font-bold text-white">
                            1%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Fifth-tier referral bonus
                        </p>
                      </div>
                    </div>
                    <div className="mt-6 bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                      <div className="flex items-center">
                        <i className="fas fa-lightbulb text-yellow-400 mr-2"></i>
                        <p className="text-white text-sm">
                          Bonus applies every time your referred users make a
                          deposit!
                        </p>
                      </div>
                    </div>
                    <div className="flex mt-4">
                      <button
                        className="text-blue-300 hover:text-blue-200 text-sm underline cursor-pointer"
                      >
                        View full terms and conditions
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-indigo-900 to-purple-900 rounded-lg shadow-md p-6 border border-indigo-800">
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-gray-800 rounded-full p-3">
                    <i className="fas fa-crown text-indigo-400 text-xl"></i>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-lg font-semibold text-white mb-2">
                      Membership Tiers
                    </h2>
                    <p className="text-indigo-200 mb-4">
                      Unlock exclusive benefits based on your referral network
                      and deposits!
                    </p>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#CD7F32] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-medal text-[#CD7F32] mr-2"></i>{" "}
                            Bronze
                          </h3>
                          <span className="text-xs text-white bg-slate-700 px-2 py-1 rounded-full">
                            Current
                          </span>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>25 referrals</li>
                            <li>or ₹25,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#C0C0C0] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-medal text-[#C0C0C0] mr-2"></i>{" "}
                            Silver
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>50 referrals</li>
                            <li>or ₹50,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#FFD700] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-medal text-[#FFD700] mr-2"></i>{" "}
                            Gold
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>75 referrals</li>
                            <li>or ₹75,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#E5E4E2] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-gem text-[#E5E4E2] mr-2"></i>{" "}
                            Platinum
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>100 referrals</li>
                            <li>or ₹1,00,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6 bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                      <div className="flex items-center">
                        <i className="fas fa-info-circle text-yellow-400 mr-2"></i>
                        <p className="text-white text-sm">
                          Higher tiers unlock better rewards and exclusive
                          features!
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Referred Users Table */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <h2 className="text-lg font-semibold text-white mb-4">
                Your Referred Users
              </h2>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fas fa-search text-gray-500"></i>
                  </div>
                </div>
                <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center cursor-pointer">
                  <i className="fas fa-download mr-2"></i> Export Data
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-slate-700">
                  <thead className="bg-slate-900">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                        onClick={() => handleSort("name")}
                      >
                        <div className="flex items-center">
                          User
                          {sortBy === "name" && (
                            <i
                              className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                            ></i>
                          )}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                        onClick={() => handleSort("date")}
                      >
                        <div className="flex items-center">
                          Join Date
                          {sortBy === "date" && (
                            <i
                              className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                            ></i>
                          )}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                        onClick={() => handleSort("earnings")}
                      >
                        <div className="flex items-center">
                          Earnings
                          {sortBy === "earnings" && (
                            <i
                              className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                            ></i>
                          )}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-slate-800 divide-y divide-slate-700">
                    {isLoading ? (
                      <tr>
                        <td colSpan={4} className="px-6 py-8 text-center text-gray-400">
                          Loading referral data...
                        </td>
                      </tr>
                    ) : paginatedUsers.length > 0 ? (
                      paginatedUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-slate-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-800 flex items-center justify-center text-indigo-300 font-medium">
                                {user.name
                                  .split(" ")
                                  .map((n: string) => n[0])
                                  .join("")}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-white">
                                  {user.name}
                                </div>
                                <div className="text-sm text-gray-400">
                                  {user.email}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {user.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                user.status === "Active"
                                  ? "bg-green-800 text-green-300"
                                  : "bg-slate-700 text-gray-400"
                              }`}
                            >
                              {user.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                            ${user.earnings.toFixed(2)}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={4}
                          className="px-6 py-4 text-center text-sm text-gray-400"
                        >
                          No referred users found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-400">
                    Showing{" "}
                    <span className="font-medium">
                      {(currentPage - 1) * itemsPerPage + 1}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(
                        currentPage * itemsPerPage,
                        filteredUsers.length,
                      )}
                    </span>{" "}
                    of{" "}
                    <span className="font-medium">{referredUsers.length}</span>{" "}
                    results (Page {currentPage} of {totalPages})
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.max(prev - 1, 1))
                      }
                      disabled={currentPage === 1 || isLoading}
                      className={`px-3 py-1 rounded-md ${currentPage === 1 || isLoading ? "bg-slate-700 text-gray-500 cursor-not-allowed" : "bg-slate-700 text-gray-300 hover:bg-slate-600 cursor-pointer"} border border-slate-600`}
                    >
                      Previous
                    </button>
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                      }
                      disabled={currentPage === totalPages || isLoading}
                      className={`px-3 py-1 rounded-md ${currentPage === totalPages || isLoading ? "bg-slate-700 text-gray-500 cursor-not-allowed" : "bg-slate-700 text-gray-300 hover:bg-slate-600 cursor-pointer"} border border-slate-600`}
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
        </div>
      </main>
      {/* QR Code Modal */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Download QR Code
              </h3>
              <button
                onClick={() => setShowQRCode(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="flex justify-center mb-6">
              <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-sm">
                <img
                  src="https://readdy.ai/api/search-image?query=QR%20code%20with%20modern%20design%2C%20clean%20black%20and%20white%20pattern%20on%20white%20background%2C%20high%20resolution%2C%20minimalist%20style%2C%20professional%20looking%20QR%20code%20with%20small%20logo%20in%20center&width=250&height=250&seq=2&orientation=squarish"
                  alt="Referral QR Code Large"
                  className="h-64 w-64 object-cover"
                />
              </div>
            </div>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => setShowQRCode(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 cursor-pointer">
                <i className="fas fa-download mr-2"></i> Download
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default UserReferral;




