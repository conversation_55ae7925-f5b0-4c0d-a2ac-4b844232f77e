import { Edit, Flag, Search, ToggleLeft, ToggleRight, ChevronDown } from 'lucide-react'
import React, { useState, useEffect } from 'react'
import { currency, currencyHeader, createCurrencyRequest } from './currency.model'
import {
  getCurrencyList,
  getCurrencyHeader,
  createCurrency,
  updateCurrency,
  deleteCurrency,
  toggleCurrencyStatus
} from './currency-service'
import Pagination from '../../../../components/Pagination/Pagination'
import TableShimmer from '../../../../components/shimmers/TableShimmer'
import { useAlert } from '../../../../components/utils/Alert'
import TableNoDataRow from '../../../../components/utils/TableNoDataRow'

const AdminCurrency: React.FC = () => {
  // State management
  const { fire } = useAlert()
  const [currencies, setCurrencies] = useState<currency[]>([])
  const [headerData, setHeaderData] = useState<currencyHeader | null>(null)
  const [selectedCurrency, setSelectedCurrency] = useState<string>('Select Currency')
  const [exchangeRate, setExchangeRate] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [itemsPerPage, setItemsPerPage] = useState<number>(10)
  const [totalPages, setTotalPages] = useState<number>(0)

  // Loading states
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false) // Shimmer loading for table
  const [isOverlayLoading, setIsOverlayLoading] = useState<boolean>(false) // Overlay loading for actions
  const [isHeaderLoading, setIsHeaderLoading] = useState<boolean>(true) // Loading for header tiles



  const availableCurrencies = [
    { name: "Japanese Yen", code: "JPY" },
    { name: "Canadian Dollar", code: "CAD" },
    { name: "Australian Dollar", code: "AUD" },
    { name: "Swiss Franc", code: "CHF" },
    { name: "Chinese Yuan", code: "CNY" },
  ]

  // API Functions
  const fetchCurrencies = async (
    page: number,
    itemsPerPageCount: number,
    searchValue: string,
    status: string = ""
  ) => {
    try {
      setIsTableLoading(true)
      const response = await getCurrencyList(
        (page - 1) * itemsPerPageCount,
        itemsPerPageCount,
        searchValue,
        status
      )
      setCurrencies(response.items)
      setCurrentPage(response.current_page)
      setTotalPages(response.total_pages)
    } catch (error) {
      console.error("Error fetching currencies:", error)
    } finally {
      setIsTableLoading(false)
    }
  }

  const fetchHeaderData = async () => {
    try {
      setIsHeaderLoading(true)
      const response = await getCurrencyHeader()
      setHeaderData(response)
    } catch (error) {
      console.error("Error fetching header data:", error)
    } finally {
      setIsHeaderLoading(false)
    }
  }

  // Event Handlers
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    fetchCurrencies(1, itemsPerPage, e.target.value, "")
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchCurrencies(page, itemsPerPage, searchTerm, "")
  }

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value)
    fetchCurrencies(1, value, searchTerm, "")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedCurrency !== 'Select Currency' && exchangeRate) {
      setIsOverlayLoading(true)
      try {
        const currencyCode = selectedCurrency.match(/\(([^)]+)\)$/)?.[1] || ''
        const currencyName = selectedCurrency.replace(/\s*\([^)]*\)$/, '')

        const newCurrency: createCurrencyRequest = {
          from_currency: currencyName,
          code: currencyCode,
          conversion_rate: parseFloat(exchangeRate)
        }

        await createCurrency(newCurrency)
        fire({
          icon: 'success',
          title: 'Success',
          text: 'Currency added successfully!'
        })

        setSelectedCurrency('Select Currency')
        setExchangeRate('')
        fetchCurrencies(currentPage, itemsPerPage, searchTerm, "")
        fetchHeaderData()
      } catch (error) {
        console.error('Error adding currency:', error)
        fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to add currency. Please try again.'
        })
      } finally {
        setIsOverlayLoading(false)
      }
    }
  }

  const handleToggleCurrencyStatus = async (id: number, currentStatus: boolean) => {
    setIsOverlayLoading(true)
    try {
      await toggleCurrencyStatus(id, !currentStatus)
      fire({
        icon: 'success',
        title: 'Success',
        text: 'Currency status updated successfully!'
      })
      fetchCurrencies(currentPage, itemsPerPage, searchTerm, "")
    } catch (error) {
      console.error('Error toggling currency status:', error)
      fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to update currency status. Please try again.'
      })
    } finally {
      setIsOverlayLoading(false)
    }
  }

  // Initialize data on component mount
  useEffect(() => {
    fetchCurrencies(1, itemsPerPage, "", "")
    fetchHeaderData()
  }, [itemsPerPage])

  const getStatusBadgeClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-emerald-900/50 text-emerald-300 border border-emerald-800'
      case 'yellow':
        return 'bg-yellow-900/50 text-yellow-300 border border-yellow-800'
      case 'red':
        return 'bg-red-900/50 text-red-300 border border-red-800'
      default:
        return 'bg-gray-700/50 text-gray-300 border border-gray-600'
    }
  }

  return (
    <div className="relative min-h-screen bg-gray-900 text-white p-6">
      {/* Add New Currency */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl mb-8">
        <div className="p-6 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-white mb-4">
            Add New Currency
          </h3>
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row items-end gap-4">
            <div className="w-full sm:w-48">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Currency
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-left text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                >
                  <span className="flex items-center justify-between">
                    <span className={selectedCurrency === 'Select Currency' ? 'text-gray-400' : 'text-white'}>
                      {selectedCurrency}
                    </span>
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  </span>
                </button>

                {isDropdownOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-gray-700 border border-gray-600 rounded-lg shadow-lg">
                    {availableCurrencies.map((currency) => (
                      <button
                        key={currency.code}
                        type="button"
                        onClick={() => {
                          setSelectedCurrency(`${currency.name} (${currency.code})`)
                          setIsDropdownOpen(false)
                        }}
                        className="w-full px-4 py-2 text-left text-white hover:bg-gray-600 first:rounded-t-lg last:rounded-b-lg transition-colors"
                      >
                        {currency.name} ({currency.code})
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="flex-1 max-w-xs w-full">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                USDT Exchange Rate
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.0001"
                  min="0"
                  value={exchangeRate}
                  onChange={(e) => setExchangeRate(e.target.value)}
                  placeholder="Enter rate"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  USDT
                </span>
              </div>
            </div>

            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-colors whitespace-nowrap w-full sm:w-auto"
            >
              Add Currency
            </button>
          </form>
        </div>
      </div>

      {/* Currency List */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl">
        <div className="px-6 py-4 border-b border-gray-700/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h3 className="text-lg font-semibold text-white">
            Currency List
          </h3>
          <div className="w-full sm:w-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search currencies..."
                value={searchTerm}
                onChange={handleSearch}
                className="pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-full sm:w-64"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-700/30">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  USDT Exchange Rate
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Last Updated
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50">
              {isTableLoading ? (
                <TableShimmer rows={itemsPerPage} columns={5} />
              ) : currencies.length > 0 ? (
                currencies.map((currency) => (
                  <tr key={currency.id} className="hover:bg-gray-700/30 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Flag className="mr-3 text-gray-400 w-5 h-5" />
                        <div className="text-sm font-medium text-white">
                          {currency.from_currency} ({currency.code})
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        1 {currency.code} = {currency.conversion_rate} USDT
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-400">
                        {new Date(currency.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClasses(currency.is_active ? 'green' : 'red')}`}>
                        {currency.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-3">
                        <button
                          className="text-blue-400 hover:text-blue-300 transition-colors p-1 disabled:opacity-50"
                          disabled={isOverlayLoading}
                        >
                          <Edit className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleToggleCurrencyStatus(currency.id, currency.is_active)}
                          className="transition-colors p-1 disabled:opacity-50"
                          disabled={isOverlayLoading}
                        >
                          {currency.is_active ? (
                            <ToggleRight className="w-6 h-6 text-emerald-400 hover:text-emerald-300" />
                          ) : (
                            <ToggleLeft className="w-6 h-6 text-gray-400 hover:text-gray-300" />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <TableNoDataRow
                  colSpan={5}
                  type="currency"
                  showSearchHint={!!searchTerm}
                />
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {currencies.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-700/50">
            <Pagination
              handlePage={handlePageChange}
              page={currentPage}
              itemsPerPage={itemsPerPage}
              handleItemsPerPageChange={handleItemsPerPageChange}
              totalPages={totalPages}
            />
          </div>
        )}
      </div>

      {/* Overlay Loading */}
      {isOverlayLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-lg">
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-white">Processing...</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminCurrency