import { Edit, ToggleRight, Trash2, Plus, X, Upload, FileImage, Save } from 'lucide-react'
import React, { useState, useEffect, useRef } from 'react'
import { DepositSlab, UpdateDepositSlabRequest, DepositSlabListResponse, CreateDepositSlabRequest } from './deposit-slabs.model'
import {
  getDepositSlabs,
  createDepositSlab,
  updateDepositSlab,
  deleteDepositSlab
} from './deposit-slabs-service'
import Pagination from '../../../../components/Pagination/Pagination'
import TableShimmer from '../../../../components/shimmers/TableShimmer'
import TableNoDataRow from '../../../../components/utils/TableNoDataRow'

function AdminDepositSlabs() {

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [editingSlabId, setEditingSlabId] = useState<number | null>(null)

  // Form states for modal
  const [formData, setFormData] = useState<CreateDepositSlabRequest>({
    name: '',
    amount: 0,
    image: null,
    is_active: true
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  // File input ref
  const imageInputRef = useRef<HTMLInputElement>(null)

  // Table states
  const [depositSlabs, setDepositSlabs] = useState<DepositSlab[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [totalItems, setTotalItems] = useState(0)
  const [isTableLoading, setIsTableLoading] = useState(true)



  // Modal form handlers
  const openCreateModal = () => {
    setIsEditMode(false)
    setEditingSlabId(null)
    setIsModalOpen(true)
    setFormData({
      name: '',
      amount: 0,
      image: null,
      is_active: true
    })
    setErrors({})
    setImagePreview(null)
  }

  const openEditModal = (slab: DepositSlab) => {
    setIsEditMode(true)
    setEditingSlabId(slab.id)
    setIsModalOpen(true)
    setFormData({
      name: slab.name,
      amount: slab.amount,
      image: null, // Will be handled separately for existing images
      is_active: slab.is_active
    })
    setErrors({})
    // Set image preview to existing image if available
    setImagePreview(slab.image || null)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setIsEditMode(false)
    setEditingSlabId(null)
    setFormData({
      name: '',
      amount: 0,
      image: null,
      is_active: true
    })
    setErrors({})
    setImagePreview(null)
    if (imageInputRef.current) {
      imageInputRef.current.value = ''
    }
  }

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, image: 'Please select a valid image file' }))
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, image: 'Image size must be less than 5MB' }))
      return
    }

    // Update form data
    setFormData(prev => ({ ...prev, image: file }))

    // Create preview
    const reader = new FileReader()
    reader.onload = () => {
      setImagePreview(reader.result as string)
    }
    reader.readAsDataURL(file)

    // Clear any existing error
    if (errors.image) {
      setErrors(prev => ({ ...prev, image: '' }))
    }
  }

  // Remove image
  const removeImage = () => {
    setFormData(prev => ({ ...prev, image: null }))
    setImagePreview(null)
    if (imageInputRef.current) {
      imageInputRef.current.value = ''
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.name?.trim()) {
      newErrors.name = 'Deposit slab name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters long'
    }

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0'
    } else if (formData.amount > 1000000) {
      newErrors.amount = 'Amount cannot exceed 1,000,000'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      // Create FormData for file upload
      const formDataToSend = new FormData()
      formDataToSend.append('name', formData.name!.trim())
      formDataToSend.append('amount', formData.amount!.toString())
      formDataToSend.append('is_active', formData.is_active!.toString())

      // Add image if selected (new image uploaded)
      if (formData.image) {
        formDataToSend.append('image', formData.image)
      }

      let result: DepositSlab

      if (isEditMode && editingSlabId) {
        console.log('Updating deposit slab with FormData:', editingSlabId)
        result = await updateDepositSlab(editingSlabId, formDataToSend)
        console.log('Deposit slab updated successfully:', result)
        alert('Deposit slab updated successfully!')
      } else {
        console.log('Creating deposit slab with FormData')
        result = await createDepositSlab(formDataToSend)
        console.log('Deposit slab created successfully:', result)
        alert('Deposit slab created successfully!')
      }

      // Close modal and refresh list
      closeModal()
      await loadDepositSlabs(currentPage, itemsPerPage)
    } catch (error: any) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} deposit slab:`, error)

      // Handle specific API errors
      if (error?.response?.status === 422) {
        const apiErrors = error.response.data?.detail || []
        let errorMessage = 'Validation error: '
        apiErrors.forEach((err: any, index: number) => {
          if (err.msg) {
            errorMessage += `${index > 0 ? ', ' : ''}${err.msg}`
          }
        })
        alert(errorMessage)
      } else if (error?.response?.status === 400) {
        alert('Bad request. Please check your input data.')
      } else if (error?.response?.status === 500) {
        alert('Server error. Please try again later.')
      } else if (error?.message) {
        alert(`Error: ${error.message}`)
      } else {
        alert(`Failed to ${isEditMode ? 'update' : 'create'} deposit slab. Please try again.`)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Load deposit slabs from API
  const loadDepositSlabs = async (page: number, limit: number) => {
    try {
      setIsTableLoading(true)
      const skip = (page - 1) * limit
      const response = await getDepositSlabs(skip, limit)

      setDepositSlabs(response.items)
      setTotalPages(response.total_pages)
      setCurrentPage(response.current_page)
      setTotalItems(response.total_items)
    } catch (error) {
      console.error('Error loading deposit slabs:', error)
      setDepositSlabs([])
    } finally {
      setIsTableLoading(false)
    }
  }

  // Handle pagination
  const handlePage = (page: number) => {
    setCurrentPage(page)
    loadDepositSlabs(page, itemsPerPage)
  }

  const handleItemsPerPage = (value: number) => {
    setItemsPerPage(value)
    loadDepositSlabs(1, value)
  }



  // Handle delete
  const handleDelete = async (slabId: number) => {
    if (window.confirm('Are you sure you want to delete this deposit slab?')) {
      try {
        await deleteDepositSlab(slabId)
        await loadDepositSlabs(currentPage, itemsPerPage)
        alert('Deposit slab deleted successfully!')
      } catch (error) {
        console.error('Error deleting deposit slab:', error)
        alert('Failed to delete deposit slab')
      }
    }
  }

  // Handle toggle status
  const handleToggleStatus = async (slab: DepositSlab) => {
    try {
      await updateDepositSlab(slab.id, { is_active: !slab.is_active })
      await loadDepositSlabs(currentPage, itemsPerPage)
    } catch (error) {
      console.error('Error toggling status:', error)
      alert('Failed to update status')
    }
  }

  // Load data on component mount
  useEffect(() => {
    loadDepositSlabs(1, itemsPerPage)
  }, [itemsPerPage])

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Add New Deposit Slab Header */}
      <div className="bg-gray-800 rounded-xl shadow-lg mb-6 border border-gray-700">
        <div className="p-6 flex justify-between items-center">
          <div>
            <h3 className="text-xl font-bold text-white">
              Deposit Slabs Management
            </h3>
            <p className="text-sm text-gray-400 mt-1">
              Create and manage deposit amount slabs with images
            </p>
          </div>
          <button
            onClick={openCreateModal}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-5 h-5" />
            <span>Add New Deposit Slab</span>
          </button>
        </div>
      </div>

      {/* Deposit Slabs Table Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700">
        <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
          <h3 className="text-xl font-bold text-white">
            Deposit Slabs
          </h3>
          <div className="flex space-x-2">
            <span className="text-sm text-gray-400">
              Total: {totalItems} deposit slabs
            </span>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-750">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Image
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Amount (USDT)
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Created Date
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-gray-800 divide-y divide-gray-700">
              {isTableLoading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center">
                    <TableShimmer />
                  </td>
                </tr>
              ) : depositSlabs.length === 0 ? (
                <TableNoDataRow
                  colSpan={6}
                  type="general"
                  message="No deposit slabs found"
                  description="No deposit amount slabs have been configured yet"
                />
              ) : (
                depositSlabs.map((slab) => (
                  <tr key={slab.id} className="hover:bg-gray-700 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      {slab.image ? (
                        <img
                          src={slab.image}
                          alt={slab.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No Image</span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">
                        {slab.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        ${slab.amount.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-400">
                        {new Date(slab.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          slab.is_active
                            ? 'bg-green-900 text-green-300'
                            : 'bg-red-900 text-red-300'
                        }`}
                      >
                        {slab.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-3">
                        <button
                          onClick={() => openEditModal(slab)}
                          className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                          title="Edit Deposit Slab"
                        >
                          <Edit className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(slab.id)}
                          className="text-red-400 hover:text-red-300 transition-colors p-1"
                          title="Delete"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleToggleStatus(slab)}
                          className={`transition-colors p-1 ${
                            slab.is_active
                              ? 'text-green-400 hover:text-green-300'
                              : 'text-gray-400 hover:text-gray-300'
                          }`}
                          title={slab.is_active ? 'Deactivate' : 'Activate'}
                        >
                          <ToggleRight className="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Only show pagination when there are items */}
        {!isTableLoading && depositSlabs.length > 0  && (
          <Pagination
            handlePage={handlePage}
            page={currentPage}
            itemsPerPage={itemsPerPage}
            handleItemsPerPageChange={handleItemsPerPage}
            totalPages={totalPages}
          />
        )}
      </div>

      {/* Add Deposit Slab Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl shadow-2xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-700 flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold text-white">
                  {isEditMode ? 'Edit Deposit Slab' : 'Add New Deposit Slab'}
                </h2>
                <p className="text-gray-400 text-sm mt-1">
                  {isEditMode ? 'Update deposit amount slab with image' : 'Create a new deposit amount slab with image'}
                </p>
              </div>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-white transition-colors"
                disabled={isSubmitting}
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Name Field */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                  Deposit Slab Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name || ''}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                    errors.name ? 'border-red-500' : 'border-gray-600'
                  }`}
                  placeholder="Enter deposit slab name (e.g., Silver, Gold, Platinum)"
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                )}
              </div>

              {/* Amount Field */}
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-300 mb-2">
                  Amount (USDT) *
                </label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount || ''}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                    errors.amount ? 'border-red-500' : 'border-gray-600'
                  }`}
                  placeholder="Enter amount (e.g., 1000)"
                  min="0"
                  step="0.01"
                  disabled={isSubmitting}
                />
                {errors.amount && (
                  <p className="mt-1 text-sm text-red-400">{errors.amount}</p>
                )}
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Deposit Slab Image
                </label>
                <div className="space-y-4">
                  {/* Upload Area */}
                  {!imagePreview ? (
                    <div
                      onClick={() => imageInputRef.current?.click()}
                      className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-gray-500 hover:bg-gray-700/20 transition-colors"
                    >
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-400 mb-1">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, JPEG up to 5MB
                      </p>
                    </div>
                  ) : (
                    /* Image Preview */
                    <div className="relative border border-gray-600 rounded-lg overflow-hidden">
                      <img
                        src={imagePreview}
                        alt="Deposit slab preview"
                        className="w-full h-48 object-cover"
                      />
                      <button
                        type="button"
                        onClick={removeImage}
                        className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                        disabled={isSubmitting}
                      >
                        <X className="w-4 h-4" />
                      </button>
                      <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2">
                        <div className="flex items-center space-x-2">
                          <FileImage className="w-4 h-4" />
                          <span className="text-sm truncate">{formData.image?.name}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Hidden File Input */}
                  <input
                    ref={imageInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    disabled={isSubmitting}
                  />

                  {errors.image && (
                    <p className="text-sm text-red-400">{errors.image}</p>
                  )}
                </div>
              </div>

              {/* Active Status */}
              <div>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active || false}
                    onChange={handleInputChange}
                    className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    disabled={isSubmitting}
                  />
                  <div>
                    <span className="text-sm font-medium text-gray-300">Active Status</span>
                    <p className="text-xs text-gray-400">Enable this deposit slab for users</p>
                  </div>
                </label>
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-700">
                <button
                  type="button"
                  onClick={closeModal}
                  className="flex items-center space-x-2 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors"
                  disabled={isSubmitting}
                >
                  <X className="w-4 h-4" />
                  <span>Cancel</span>
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save className="w-4 h-4" />
                  <span>
                    {isSubmitting
                      ? (isEditMode ? 'Updating...' : 'Creating...')
                      : (isEditMode ? 'Update Deposit Slab' : 'Create Deposit Slab')
                    }
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminDepositSlabs