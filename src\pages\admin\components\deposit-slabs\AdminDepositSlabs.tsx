import { Edit, ToggleRight, Trash2 } from 'lucide-react'
import React, { useState, useEffect, ChangeEvent, FormEvent } from 'react'
import { DepositSlab, CreateDepositSlabRequest, UpdateDepositSlabRequest, DepositSlabListResponse } from './deposit-slabs.model'
import {
  getDepositSlabs,
  createDepositSlab,
  updateDepositSlab,
  deleteDepositSlab
} from './deposit-slabs-service'
import Pagination from '../../../../components/Pagination/Pagination'
import TableShimmer from '../../../../components/shimmers/TableShimmer'

function AdminDepositSlabs() {
  // Form states
  const [depositAmount, setDepositAmount] = useState('')
  const [depositName, setDepositName] = useState('')
  const [isActive, setIsActive] = useState(true)

  // Table states
  const [depositSlabs, setDepositSlabs] = useState<DepositSlab[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [totalItems, setTotalItems] = useState(0)
  const [isTableLoading, setIsTableLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Edit states
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editForm, setEditForm] = useState<UpdateDepositSlabRequest>({})

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    if (!depositName.trim() || !depositAmount.trim()) {
      alert('Please fill in all required fields')
      return
    }

    if (Number(depositAmount) <= 0) {
      alert('Amount must be greater than 0')
      return
    }

    setIsSubmitting(true)
    try {
      const newSlab: CreateDepositSlabRequest = {
        name: depositName.trim(),
        amount: Number(depositAmount),
        is_active: isActive
      }

      await createDepositSlab(newSlab)
      setDepositAmount('')
      setDepositName('')
      setIsActive(true)

      // Refresh the list
      await loadDepositSlabs(1, itemsPerPage)
      alert('Deposit slab created successfully!')
    } catch (error) {
      console.error('Error creating deposit slab:', error)
      alert('Failed to create deposit slab')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Load deposit slabs from API
  const loadDepositSlabs = async (page: number, limit: number) => {
    try {
      setIsTableLoading(true)
      const skip = (page - 1) * limit
      const response = await getDepositSlabs(skip, limit)

      setDepositSlabs(response.items)
      setTotalPages(response.total_pages)
      setCurrentPage(response.current_page)
      setTotalItems(response.total_items)
    } catch (error) {
      console.error('Error loading deposit slabs:', error)
      setDepositSlabs([])
    } finally {
      setIsTableLoading(false)
    }
  }

  // Handle pagination
  const handlePage = (page: number) => {
    setCurrentPage(page)
    loadDepositSlabs(page, itemsPerPage)
  }

  const handleItemsPerPage = (value: number) => {
    setItemsPerPage(value)
    loadDepositSlabs(1, value)
  }

  // Handle edit
  const handleEdit = (slab: DepositSlab) => {
    setEditingId(slab.id)
    setEditForm({
      name: slab.name,
      amount: slab.amount,
      is_active: slab.is_active
    })
  }

  // Handle update
  const handleUpdate = async (slabId: number) => {
    try {
      await updateDepositSlab(slabId, editForm)
      setEditingId(null)
      setEditForm({})
      await loadDepositSlabs(currentPage, itemsPerPage)
      alert('Deposit slab updated successfully!')
    } catch (error) {
      console.error('Error updating deposit slab:', error)
      alert('Failed to update deposit slab')
    }
  }

  // Handle delete
  const handleDelete = async (slabId: number) => {
    if (window.confirm('Are you sure you want to delete this deposit slab?')) {
      try {
        await deleteDepositSlab(slabId)
        await loadDepositSlabs(currentPage, itemsPerPage)
        alert('Deposit slab deleted successfully!')
      } catch (error) {
        console.error('Error deleting deposit slab:', error)
        alert('Failed to delete deposit slab')
      }
    }
  }

  // Handle toggle status
  const handleToggleStatus = async (slab: DepositSlab) => {
    try {
      await updateDepositSlab(slab.id, { is_active: !slab.is_active })
      await loadDepositSlabs(currentPage, itemsPerPage)
    } catch (error) {
      console.error('Error toggling status:', error)
      alert('Failed to update status')
    }
  }

  // Load data on component mount
  useEffect(() => {
    loadDepositSlabs(1, itemsPerPage)
  }, [itemsPerPage])

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Add New Deposit Slab Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg mb-6 border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-xl font-bold text-white mb-6">
            Add New Deposit Slab
          </h3>
          <div className="flex items-end gap-4">
            <div className="flex-1 max-w-xs">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Deposit Amount (USDT)
              </label>
              <input
                type="number"
                min="0"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                placeholder="Enter amount"
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
            </div>
            <div className="flex-1 max-w-xs">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Deposit Name
              </label>
              <input
                type="text"
                value={depositName}
                onChange={(e) => setDepositName(e.target.value)}
                placeholder="Enter name"
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
            </div>
            <div className="flex-1 max-w-xs">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-sm font-medium text-gray-300">
                  Active Status
                </span>
              </label>
            </div>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Adding...' : 'Add Deposit Slab'}
            </button>
          </div>
        </div>
      </div>

      {/* Deposit Slabs Table Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700">
        <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
          <h3 className="text-xl font-bold text-white">
            Deposit Slabs
          </h3>
          <div className="flex space-x-2">
            <span className="text-sm text-gray-400">
              Total: {totalItems} deposit slabs
            </span>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-750">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Amount (USDT)
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Created Date
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-gray-800 divide-y divide-gray-700">
              {isTableLoading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center">
                    <TableShimmer />
                  </td>
                </tr>
              ) : depositSlabs.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-400">
                    No deposit slabs found
                  </td>
                </tr>
              ) : (
                depositSlabs.map((slab) => (
                  <tr key={slab.id} className="hover:bg-gray-700 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      {editingId === slab.id ? (
                        <input
                          type="text"
                          value={editForm.name || ''}
                          onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                          className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                        />
                      ) : (
                        <div className="text-sm font-medium text-white">
                          {slab.name}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {editingId === slab.id ? (
                        <input
                          type="number"
                          value={editForm.amount || ''}
                          onChange={(e) => setEditForm({ ...editForm, amount: Number(e.target.value) })}
                          className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                          min="0"
                          step="0.01"
                        />
                      ) : (
                        <div className="text-sm text-gray-300">
                          ${slab.amount.toLocaleString()}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-400">
                        {new Date(slab.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {editingId === slab.id ? (
                        <select
                          value={editForm.is_active ? 'true' : 'false'}
                          onChange={(e) => setEditForm({ ...editForm, is_active: e.target.value === 'true' })}
                          className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                        >
                          <option value="true">Active</option>
                          <option value="false">Inactive</option>
                        </select>
                      ) : (
                        <span
                          className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            slab.is_active
                              ? 'bg-green-900 text-green-300'
                              : 'bg-red-900 text-red-300'
                          }`}
                        >
                          {slab.is_active ? 'Active' : 'Inactive'}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-3">
                        {editingId === slab.id ? (
                          <>
                            <button
                              onClick={() => handleUpdate(slab.id)}
                              className="text-green-400 hover:text-green-300 transition-colors p-1"
                              title="Save"
                            >
                              <i className="fas fa-check w-5 h-5"></i>
                            </button>
                            <button
                              onClick={() => {
                                setEditingId(null)
                                setEditForm({})
                              }}
                              className="text-gray-400 hover:text-gray-300 transition-colors p-1"
                              title="Cancel"
                            >
                              <i className="fas fa-times w-5 h-5"></i>
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => handleEdit(slab)}
                              className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                              title="Edit"
                            >
                              <Edit className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => handleDelete(slab.id)}
                              className="text-red-400 hover:text-red-300 transition-colors p-1"
                              title="Delete"
                            >
                              <Trash2 className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => handleToggleStatus(slab)}
                              className={`transition-colors p-1 ${
                                slab.is_active
                                  ? 'text-green-400 hover:text-green-300'
                                  : 'text-gray-400 hover:text-gray-300'
                              }`}
                              title={slab.is_active ? 'Deactivate' : 'Activate'}
                            >
                              <ToggleRight className="w-5 h-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        <Pagination
          handlePage={handlePage}
          page={currentPage}
          itemsPerPage={itemsPerPage}
          handleItemsPerPageChange={handleItemsPerPage}
          totalPages={totalPages}
        />
      </div>
    </div>
  )
}

export default AdminDepositSlabs