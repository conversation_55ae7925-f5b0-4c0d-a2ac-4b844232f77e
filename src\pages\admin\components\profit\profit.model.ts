// // Admin Profit Management Models and Interfaces

// import { ApiResponse } from '../../../../api/axiosInstance';

// // Base pagination interface
// export interface PaginationParams {
//   skip?: number;
//   limit?: number;
// }

// export interface PaginationResponse<T> {
//   total: number;
//   skip: number;
//   limit: number;
//   has_next: boolean;
//   has_prev: boolean;
//   items: T[];
// }

// // Admin profit interface
// export interface AdminProfit {
//   id: number;
//   today_rate: number;
//   total_user_getting_profit: number;
//   total_net_deposit: string;
//   distributed_amount: string;
//   created_at: string;
//   updated_at?: string;
//   // Additional calculated fields
//   profit_percentage?: number;
//   remaining_amount?: string;
//   distribution_status?: 'pending' | 'processing' | 'completed' | 'failed';
//   notes?: string;
// }

// // Create admin profit request interface
// export interface CreateAdminProfitRequest {
//   today_rate: number;
//   notes?: string;
// }

// // Update admin profit request interface
// export interface UpdateAdminProfitRequest {
//   today_rate?: number;
//   notes?: string;
//   distribution_status?: 'pending' | 'processing' | 'completed' | 'failed';
// }

// // Profit log interface
// export interface ProfitLog {
//   id: number;
//   deposit_id: number;
//   user_id: number;
//   profit_amount: number;
//   profit_percent: number;
//   created_at: string;
//   // Additional fields that might be included
//   user?: {
//     id: number;
//     name: string;
//     email: string;
//   };
//   deposit?: {
//     id: number;
//     amount: number;
//     status: string;
//   };
//   admin_profit_id?: number;
//   distribution_date?: string;
//   status?: 'pending' | 'distributed' | 'failed';
// }

// // Create profit log request interface
// export interface CreateProfitLogRequest {
//   deposit_id: number;
//   user_id: number;
//   profit_amount: number;
//   profit_percent: number;
//   admin_profit_id?: number;
// }

// // API Response interfaces
// export interface GetAdminProfitsResponse extends ApiResponse<PaginationResponse<AdminProfit>> {}
// export interface GetAdminProfitResponse extends ApiResponse<AdminProfit> {}
// export interface CreateAdminProfitResponse extends ApiResponse<AdminProfit> {}
// export interface UpdateAdminProfitResponse extends ApiResponse<AdminProfit> {}
// export interface DeleteAdminProfitResponse extends ApiResponse<{ message: string }> {}

// export interface GetProfitLogsResponse extends ApiResponse<PaginationResponse<ProfitLog>> {}
// export interface GetProfitLogResponse extends ApiResponse<ProfitLog> {}
// export interface CreateProfitLogResponse extends ApiResponse<ProfitLog> {}
// export interface DeleteProfitLogResponse extends ApiResponse<{ message: string }> {}

// // Form validation errors
// export interface AdminProfitFormErrors {
//   today_rate?: string;
//   notes?: string;
//   distribution_status?: string;
//   general?: string;
// }

// export interface ProfitLogFormErrors {
//   deposit_id?: string;
//   user_id?: string;
//   profit_amount?: string;
//   profit_percent?: string;
//   general?: string;
// }

// // Admin profit filters for search/filtering
// export interface AdminProfitFilters extends PaginationParams {
//   search?: string;
//   rate_min?: number;
//   rate_max?: number;
//   distribution_status?: 'pending' | 'processing' | 'completed' | 'failed';
//   created_from?: string;
//   created_to?: string;
// }

// // Profit log filters for search/filtering
// export interface ProfitLogFilters extends PaginationParams {
//   search?: string;
//   user_id?: number;
//   deposit_id?: number;
//   admin_profit_id?: number;
//   status?: 'pending' | 'distributed' | 'failed';
//   amount_min?: number;
//   amount_max?: number;
//   percent_min?: number;
//   percent_max?: number;
//   created_from?: string;
//   created_to?: string;
// }

// // Profit statistics interface
// export interface ProfitStatistics {
//   total_profit_distributed: number;
//   total_profit_pending: number;
//   total_users_receiving_profit: number;
//   average_profit_rate: number;
//   highest_profit_rate: number;
//   lowest_profit_rate: number;
//   total_deposits_with_profit: number;
//   profit_distribution_frequency: {
//     daily: number;
//     weekly: number;
//     monthly: number;
//   };
//   recent_distributions: number;
//   failed_distributions: number;
//   success_rate: number;
// }

// // Profit distribution summary
// export interface ProfitDistributionSummary {
//   date: string;
//   total_amount: number;
//   total_users: number;
//   rate_applied: number;
//   status: 'pending' | 'processing' | 'completed' | 'failed';
//   distribution_id?: number;
// }

// // Bulk profit distribution request
// export interface BulkProfitDistributionRequest {
//   rate: number;
//   user_ids?: number[];
//   deposit_ids?: number[];
//   notes?: string;
//   schedule_date?: string;
// }

// // Profit calculation request
// export interface ProfitCalculationRequest {
//   deposit_amount: number;
//   profit_rate: number;
//   duration_days?: number;
//   compound_interest?: boolean;
// }

// // Profit calculation response
// export interface ProfitCalculationResponse {
//   principal_amount: number;
//   profit_rate: number;
//   duration_days: number;
//   daily_profit: number;
//   total_profit: number;
//   final_amount: number;
//   compound_interest: boolean;
//   breakdown?: Array<{
//     day: number;
//     daily_profit: number;
//     cumulative_profit: number;
//     total_amount: number;
//   }>;
// }

export {};
