import React, { useState } from 'react'
import Header from '../../components/header/Header'
import UserSideBar from '../../components/sidebar/UserSideBar'
import { Outlet } from 'react-router-dom'

function UserLayout() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
        <div className="min-h-screen bg-[#0F1115] flex flex-col">
          <Header
            type='user'
            isMobileMenuOpen={isMobileMenuOpen}
            setIsMobileMenuOpen={setIsMobileMenuOpen}
          />
          {/* Main Content */}
          <div className="flex flex-1 pt-16">
            <UserSideBar
              isMobileMenuOpen={isMobileMenuOpen}
              setIsMobileMenuOpen={setIsMobileMenuOpen}
            />
            {/* Main Content Area */}
            <main className="flex-1 ml-0 md:ml-64 min-h-screen bg-[#0B1221] p-4 md:p-6">
              <Outlet />
            </main>
          </div>
        </div>
  )
}

export default UserLayout