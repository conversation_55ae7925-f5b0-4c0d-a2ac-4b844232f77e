import api from "../../../../api/axiosInstance";

export const getTermsAndConditions = async () => {
  try {
    const response = await api.get("/terms_and_conditions/");
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getPrivacyPolicy = async () => {
  try {
    const response = await api.get("/privacy_and_policys/");
    return response.data;
  } catch (error) {
    throw error;
  }
};
