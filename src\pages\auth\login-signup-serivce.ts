import api from "../../api/axiosInstance";
import {
  LoginRequest,
  loginResponse,
  SendOTPRequest,
  SendOTPResponse,
  VerifyOTPRequest,
  VerifyOTPResponse
} from "./login-signup.model";


export const SignupApi = async (body: FormData) => {
  try {
    const response = await api.post(
      "/users/",
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const loginApi = async (body: LoginRequest) :Promise<loginResponse> => {
  try {
    const response = await api.post("/users/login",body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const forgotPasswordApi = async (body: FormData) => {
  try {
    const response = await api.post("/users/password/reset/",body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Send OTP for email verification
export const sendOTPApi = async (email: string): Promise<SendOTPResponse> => {
  try {
    console.log('Attempting to send OTP to:', email);

    // For now, simulate OTP sending since the endpoint might not be implemented yet
    // TODO: Replace with actual API call when backend endpoint is ready

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return mock success response
    return {
      success: true,
      message: "OTP sent successfully to your email",
      data: {
        message: "OTP sent successfully to your email",
        expiresIn: 300 // 5 minutes
      }
    };

    // Uncomment below when actual API endpoint is available:
    /*
    const response = await api.post("/users/send-otp/", {
      email,
      type: "email_verification"
    });
    return response.data;
    */
  } catch (error) {
    console.error('Send OTP error:', error);
    throw error;
  }
};

// Verify OTP
export const verifyOTPApi = async (email: string, otp: string): Promise<VerifyOTPResponse> => {
  try {
    console.log('Attempting to verify OTP for:', email, 'OTP:', otp);

    // For now, simulate OTP verification since the endpoint might not be implemented yet
    // TODO: Replace with actual API call when backend endpoint is ready

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock verification logic - accept "123456" as valid OTP for testing
    const isValidOTP = otp === "123456" || otp.length === 6;

    if (isValidOTP) {
      return {
        success: true,
        message: "OTP verified successfully",
        data: {
          message: "OTP verified successfully",
          verified: true,
          token: "mock_verification_token"
        }
      };
    } else {
      return {
        success: false,
        message: "Invalid OTP. Please try again.",
        data: {
          message: "Invalid OTP. Please try again.",
          verified: false
        }
      };
    }

    // Uncomment below when actual API endpoint is available:
    /*
    const response = await api.post("/users/verify-otp/", {
      email,
      otp,
      type: "email_verification"
    });
    return response.data;
    */
  } catch (error) {
    console.error('Verify OTP error:', error);
    throw error;
  }
};