import api from "../../api/axiosInstance";
import { LoginRequest, loginResponse } from "./login-signup.model";


export const SignupApi = async (body: FormData) => {
  try {
    const response = await api.post(
      "formData/users/",
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const loginApi = async (body: LoginRequest) :Promise<loginResponse> => {
  try {
    const response = await api.post("/users/login",body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const forgotPasswordApi = async (body: FormData) => {
  try {
    const response = await api.post("/users/password/reset/",body);
    return response.data;
  } catch (error) {
    throw error;
  }
};