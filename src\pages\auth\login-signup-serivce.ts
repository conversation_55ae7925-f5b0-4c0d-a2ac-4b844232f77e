import api from "../../api/axiosInstance";
import {
  LoginRequest,
  loginResponse,
  SendOTPRequest,
  SendOTPResponse,
  VerifyOTPRequest,
  VerifyOTPResponse
} from "./login-signup.model";


export const SignupApi = async (body: FormData) => {
  try {
    const response = await api.post(
      "/users/",
      body
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const loginApi = async (body: LoginRequest) :Promise<loginResponse> => {
  try {
    const response = await api.post("/users/login",body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const forgotPasswordApi = async (body: FormData) => {
  try {
    const response = await api.post("/users/password/reset/",body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Send OTP for email verification
export const sendOTPApi = async (email: string): Promise<SendOTPResponse> => {
  try {
    const response = await api.post("/users/send-otp/", {
      email,
      type: "email_verification"
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Verify OTP
export const verifyOTPApi = async (email: string, otp: string): Promise<VerifyOTPResponse> => {
  try {
    const response = await api.post("/users/verify-otp/", {
      email,
      otp,
      type: "email_verification"
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};