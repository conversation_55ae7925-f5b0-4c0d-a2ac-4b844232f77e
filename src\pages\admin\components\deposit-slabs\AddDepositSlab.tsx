import React, { useState, FormEvent } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Save, X } from 'lucide-react'
import { CreateDepositSlabRequest } from './deposit-slabs.model'
import { createDepositSlab } from './deposit-slabs-service'

function AddDepositSlab() {
  const navigate = useNavigate()
  
  // Form states
  const [formData, setFormData] = useState<CreateDepositSlabRequest>({
    name: '',
    amount: 0,
    is_active: true
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Deposit slab name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters long'
    }

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0'
    } else if (formData.amount > 1000000) {
      newErrors.amount = 'Amount cannot exceed 1,000,000'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      const newSlab = {
        name: formData.name.trim(),
        amount: formData.amount,
        is_active: formData.is_active
      }
      
      await createDepositSlab(newSlab)
      
      // Show success message and redirect
      alert('Deposit slab created successfully!')
      navigate('/admin/deposit-slabs')
    } catch (error: any) {
      console.error('Error creating deposit slab:', error)
      
      // Handle specific API errors
      if (error?.response?.status === 422) {
        const apiErrors = error.response.data?.detail || []
        const newErrors: {[key: string]: string} = {}
        
        apiErrors.forEach((err: any) => {
          if (err.loc && err.loc.length > 1) {
            newErrors[err.loc[1]] = err.msg
          }
        })
        
        if (Object.keys(newErrors).length > 0) {
          setErrors(newErrors)
        } else {
          alert('Validation error. Please check your input.')
        }
      } else {
        alert('Failed to create deposit slab. Please try again.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    if (formData.name || formData.amount > 0) {
      if (window.confirm('Are you sure you want to cancel? All changes will be lost.')) {
        navigate('/admin/deposit-slabs')
      }
    } else {
      navigate('/admin/deposit-slabs')
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-4 mb-4">
          <button
            onClick={() => navigate('/admin/deposit-slabs')}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Deposit Slabs</span>
          </button>
        </div>
        <h1 className="text-3xl font-bold text-white">Add New Deposit Slab</h1>
        <p className="text-gray-400 mt-2">Create a new deposit slab with custom amount and settings</p>
      </div>

      {/* Form Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700 max-w-2xl">
        <div className="p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Deposit Slab Details</h2>
          <p className="text-gray-400 text-sm mt-1">Fill in the information below to create a new deposit slab</p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Name Field */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              Deposit Slab Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                errors.name ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter deposit slab name (e.g., Silver, Gold, Platinum)"
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-400">{errors.name}</p>
            )}
          </div>

          {/* Amount Field */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-300 mb-2">
              Amount (USDT) *
            </label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount || ''}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                errors.amount ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter amount (e.g., 1000)"
              min="0"
              step="0.01"
              disabled={isSubmitting}
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-400">{errors.amount}</p>
            )}
          </div>

          {/* Active Status */}
          <div>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <div>
                <span className="text-sm font-medium text-gray-300">Active Status</span>
                <p className="text-xs text-gray-400">Enable this deposit slab for users</p>
              </div>
            </label>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-700">
            <button
              type="button"
              onClick={handleCancel}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-4 h-4" />
              <span>Cancel</span>
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              <span>{isSubmitting ? 'Creating...' : 'Create Deposit Slab'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddDepositSlab
