import React from 'react';
import { useAlert } from './Alert';

/**
 * Example component demonstrating how to use the Alert component
 * This file shows various use cases and can be used for testing
 */
const AlertExample: React.FC = () => {
  const { fire } = useAlert();

  const showSuccessAlert = () => {
    fire({
      icon: 'success',
      title: 'Success!',
      text: 'Your operation was completed successfully.',
      confirmButtonText: 'OK',
      position: 'center'
    });
  };

  const showErrorAlert = () => {
    fire({
      icon: 'error',
      title: 'Error!',
      text: 'Something went wrong. Please try again.',
      confirmButtonText: 'Retry',
      cancelButtonText: 'Cancel',
      position: 'center',
      onConfirm: () => console.log('User clicked Retry'),
      onCancel: () => console.log('User clicked Cancel')
    });
  };

  const showInfoAlert = () => {
    fire({
      icon: 'info',
      title: 'Information',
      text: 'This is an informational message.',
      confirmButtonText: 'Got it',
      position: 'center'
    });
  };

  const showAutoCloseAlert = () => {
    fire({
      icon: 'success',
      title: 'Auto Close',
      text: 'This alert will close automatically in 3 seconds.',
      autoClose: 3000,
      position: 'top-right'
    });
  };

  const showInputAlert = () => {
    fire({
      icon: 'info',
      title: 'Enter Your Name',
      text: 'Please provide your name below:',
      showInput: true,
      inputType: 'text',
      placeholder: 'Enter your name...',
      confirmButtonText: 'Submit',
      cancelButtonText: 'Cancel',
      position: 'center',
      onConfirm: (inputValue) => {
        console.log('User entered:', inputValue);
        fire({
          icon: 'success',
          title: 'Thank you!',
          text: `Hello, ${inputValue}!`,
          confirmButtonText: 'OK'
        });
      }
    });
  };

  const showTextareaAlert = () => {
    fire({
      icon: 'info',
      title: 'Leave a Comment',
      text: 'Please share your feedback:',
      showInput: true,
      inputType: 'textarea',
      placeholder: 'Enter your comment here...',
      confirmButtonText: 'Submit',
      cancelButtonText: 'Cancel',
      position: 'center',
      onConfirm: (inputValue) => {
        console.log('User comment:', inputValue);
        fire({
          icon: 'success',
          title: 'Comment Submitted',
          text: 'Thank you for your feedback!',
          confirmButtonText: 'OK'
        });
      }
    });
  };

  const showPositionAlert = (position: any) => {
    fire({
      icon: 'info',
      title: `Position: ${position}`,
      text: `This alert is positioned at ${position}`,
      confirmButtonText: 'OK',
      position: position
    });
  };

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold mb-6">Alert Component Examples</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <button
          onClick={showSuccessAlert}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Success Alert
        </button>

        <button
          onClick={showErrorAlert}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          Error Alert
        </button>

        <button
          onClick={showInfoAlert}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Info Alert
        </button>

        <button
          onClick={showAutoCloseAlert}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
        >
          Auto Close Alert
        </button>

        <button
          onClick={showInputAlert}
          className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600"
        >
          Input Alert
        </button>

        <button
          onClick={showTextareaAlert}
          className="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600"
        >
          Textarea Alert
        </button>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Position Examples</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {['center', 'top-left', 'top-right', 'bottom-left', 'bottom-right'].map((position) => (
            <button
              key={position}
              onClick={() => showPositionAlert(position)}
              className="bg-gray-500 text-white px-3 py-2 rounded text-sm hover:bg-gray-600"
            >
              {position}
            </button>
          ))}
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Keyboard Shortcuts:</h3>
        <ul className="text-sm space-y-1">
          <li><kbd className="bg-gray-200 px-2 py-1 rounded">Escape</kbd> - Close alert or trigger cancel action</li>
          <li><kbd className="bg-gray-200 px-2 py-1 rounded">Enter</kbd> - Confirm action (when no input field)</li>
        </ul>
      </div>
    </div>
  );
};

export default AlertExample;
