// // Admin Deposits Management Service

import api from "../../../../api/axiosInstance";
import { depositLIst } from "./deposits.model";

// import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
// import {
//   Deposit,
//   CreateDepositRequest,
//   UpdateDepositRequest,
//   ApproveDepositRequest,
//   GetDepositsResponse,
//   GetDepositResponse,
//   CreateDepositResponse,
//   UpdateDepositResponse,
//   ApproveDepositResponse,
//   DeleteDepositResponse,
//   DepositFilters,
//   DepositStatistics,
//   DepositSummary
// } from './deposits.model';

// class AdminDepositsService {
//   private readonly baseUrl = '/deposits';

//   /**
//    * Get all deposits with pagination and filters
//    */
//   async getDeposits(filters?: DepositFilters): Promise<GetDepositsResponse> {
//     try {
//       const params = new URLSearchParams();
      
//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
      
//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.status) params.append('status', filters.status);
//       if (filters?.user_id) params.append('user_id', filters.user_id.toString());
//       if (filters?.admin_wallet_id) params.append('admin_wallet_id', filters.admin_wallet_id);
//       if (filters?.reviewed_by_admin_id) params.append('reviewed_by_admin_id', filters.reviewed_by_admin_id.toString());
//       if (filters?.amount_min) params.append('amount_min', filters.amount_min.toString());
//       if (filters?.amount_max) params.append('amount_max', filters.amount_max.toString());
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);
//       if (filters?.approved_from) params.append('approved_from', filters.approved_from);
//       if (filters?.approved_to) params.append('approved_to', filters.approved_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
      
//       const response = await apiMethods.get<GetDepositsResponse>(url);
      
//       return {
//         success: true,
//         message: 'Deposits retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposits error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get deposit by ID
//    */
//   async getDepositById(depositId: number): Promise<GetDepositResponse> {
//     try {
//       const response = await apiMethods.get<GetDepositResponse>(`${this.baseUrl}/${depositId}`);
      
//       return {
//         success: true,
//         message: 'Deposit retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit not found',
//           error: 'Deposit not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new deposit
//    */
//   async createDeposit(depositData: CreateDepositRequest): Promise<CreateDepositResponse> {
//     try {
//       const response = await apiMethods.post<CreateDepositResponse>(this.baseUrl, depositData);
      
//       return {
//         success: true,
//         message: 'Deposit created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create deposit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Approve deposit (Admin only)
//    */
//   async approveDeposit(depositId: number, approvalData?: ApproveDepositRequest): Promise<ApproveDepositResponse> {
//     try {
//       const response = await apiMethods.post<ApproveDepositResponse>(`${this.baseUrl}/${depositId}`, approvalData || {});
      
//       return {
//         success: true,
//         message: 'Deposit approved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Approve deposit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit not found',
//           error: 'Deposit not found'
//         };
//       }
      
//       if (errorInfo.status === 403) {
//         return {
//           success: false,
//           message: 'Insufficient permissions to approve deposits',
//           error: 'Access denied'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update deposit status
//    */
//   async updateDeposit(depositId: number, depositData: UpdateDepositRequest): Promise<UpdateDepositResponse> {
//     try {
//       const response = await apiMethods.put<UpdateDepositResponse>(`${this.baseUrl}/${depositId}`, depositData);
      
//       return {
//         success: true,
//         message: 'Deposit updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update deposit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit not found',
//           error: 'Deposit not found'
//         };
//       }
      
//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete deposit
//    */
//   async deleteDeposit(depositId: number): Promise<DeleteDepositResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteDepositResponse>(`${this.baseUrl}/${depositId}`);
      
//       return {
//         success: true,
//         message: 'Deposit deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete deposit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit not found',
//           error: 'Deposit not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get pending deposits
//    */
//   async getPendingDeposits(filters?: DepositFilters): Promise<GetDepositsResponse> {
//     return this.getDeposits({ ...filters, status: 'pending' });
//   }

//   /**
//    * Get approved deposits
//    */
//   async getApprovedDeposits(filters?: DepositFilters): Promise<GetDepositsResponse> {
//     return this.getDeposits({ ...filters, status: 'approved' });
//   }

//   /**
//    * Get rejected deposits
//    */
//   async getRejectedDeposits(filters?: DepositFilters): Promise<GetDepositsResponse> {
//     return this.getDeposits({ ...filters, status: 'rejected' });
//   }

//   /**
//    * Get paginated deposits (helper method)
//    */
//   async getPaginatedDeposits(page: number = 1, limit: number = 10, filters?: Omit<DepositFilters, 'skip' | 'limit'>): Promise<GetDepositsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getDeposits({ ...filters, skip, limit });
//   }

//   /**
//    * Search deposits
//    */
//   async searchDeposits(query: string, filters?: DepositFilters): Promise<GetDepositsResponse> {
//     return this.getDeposits({ ...filters, search: query });
//   }

//   /**
//    * Get deposit statistics (if endpoint exists)
//    */
//   async getDepositStatistics(): Promise<{ success: boolean; data?: DepositStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: DepositStatistics }>(`${this.baseUrl}/statistics`);
      
//       return {
//         success: true,
//         message: 'Statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get deposit summary by status
//    */
//   async getDepositSummary(): Promise<{ success: boolean; data?: DepositSummary[]; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: DepositSummary[] }>(`${this.baseUrl}/summary`);
      
//       return {
//         success: true,
//         message: 'Deposit summary retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit summary error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }
// }

// // Export singleton instance
// export const adminDepositsService = new AdminDepositsService();
// export default adminDepositsService;

export const getDepositList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string,
  date?: string
): Promise<depositLIst> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    if (date) {
      queryParams.append("date", encodeURIComponent(date));
    }

    const response = await api.get(`deposits/?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};