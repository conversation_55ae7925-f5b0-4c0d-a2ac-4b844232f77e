import React from 'react';
import NoDataMessage from './NoDataMessage';

interface Column {
  key: string;
  label: string;
  render?: (value: any, row: any) => React.ReactNode;
  className?: string;
  mobileLabel?: string;
  hideOnMobile?: boolean;
}

interface ResponsiveTableProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
  onRowClick?: (row: any, index: number) => void;
}

const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  data,
  loading = false,
  emptyMessage = "No data available",
  className = "",
  onRowClick
}) => {
  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="hidden md:block">
          <div className="bg-slate-700 h-12 rounded mb-2"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-slate-800 h-16 rounded mb-2"></div>
          ))}
        </div>
        <div className="md:hidden space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-slate-800 h-24 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <NoDataMessage
        type="general"
        message={emptyMessage}
        height="medium"
      />
    );
  }

  return (
    <div className={`responsive-table ${className}`}>
      {/* Desktop Table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-900/50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider ${column.className || ''}`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {data.map((row, index) => (
              <tr
                key={index}
                className={`hover:bg-slate-700/50 transition-colors duration-150 ${
                  onRowClick ? 'cursor-pointer' : ''
                }`}
                onClick={() => onRowClick?.(row, index)}
              >
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className={`px-6 py-4 whitespace-nowrap ${column.className || ''}`}
                  >
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-3">
        {data.map((row, index) => (
          <div
            key={index}
            className={`mobile-table-card ${onRowClick ? 'cursor-pointer' : ''}`}
            onClick={() => onRowClick?.(row, index)}
          >
            {columns
              .filter(column => !column.hideOnMobile)
              .map((column) => (
                <div key={column.key} className="flex justify-between items-start mb-2 last:mb-0">
                  <span className="text-sm font-medium text-slate-300 mr-2">
                    {column.mobileLabel || column.label}:
                  </span>
                  <span className="text-sm text-white text-right flex-1">
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </span>
                </div>
              ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ResponsiveTable;
