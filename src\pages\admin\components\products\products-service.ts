// // Admin Products Management Service

import api from "../../../../api/axiosInstance";
import { productList } from "../../../user/sub-pages/product/product.model";

// import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
// import {
//   Product,
//   CreateProductRequest,
//   UpdateProductRequest,
//   GetProductsResponse,
//   GetProductResponse,
//   CreateProductResponse,
//   UpdateProductResponse,
//   DeleteProductResponse,
//   ProductFilters,
//   ProductStatistics,
//   ProductImageUploadRequest,
//   ProductImageUploadResponse
// } from './products.model';

// class AdminProductsService {
//   private readonly baseUrl = '/products';

//   /**
//    * Get all products with pagination and filters
//    */
//   async getProducts(filters?: ProductFilters): Promise<GetProductsResponse> {
//     try {
//       const params = new URLSearchParams();
      
//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
      
//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.active !== undefined) params.append('active', filters.active.toString());
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
      
//       const response = await apiMethods.get<GetProductsResponse>(url);
      
//       return {
//         success: true,
//         message: 'Products retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get products error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get product by ID
//    */
//   async getProductById(productId: number): Promise<GetProductResponse> {
//     try {
//       const response = await apiMethods.get<GetProductResponse>(`${this.baseUrl}/${productId}`);
      
//       return {
//         success: true,
//         message: 'Product retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get product error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Product not found',
//           error: 'Product not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new product
//    */
//   async createProduct(productData: CreateProductRequest): Promise<CreateProductResponse> {
//     try {
//       const response = await apiMethods.post<CreateProductResponse>(this.baseUrl, productData);
      
//       return {
//         success: true,
//         message: 'Product created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create product error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update product
//    */
//   async updateProduct(productId: number, productData: UpdateProductRequest): Promise<UpdateProductResponse> {
//     try {
//       const response = await apiMethods.put<UpdateProductResponse>(`${this.baseUrl}/${productId}`, productData);
      
//       return {
//         success: true,
//         message: 'Product updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update product error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Product not found',
//           error: 'Product not found'
//         };
//       }
      
//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete product
//    */
//   async deleteProduct(productId: number): Promise<DeleteProductResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteProductResponse>(`${this.baseUrl}/${productId}`);
      
//       return {
//         success: true,
//         message: 'Product deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete product error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Product not found',
//           error: 'Product not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Upload product image
//    */
//   async uploadProductImage(request: ProductImageUploadRequest): Promise<ProductImageUploadResponse> {
//     try {
//       const response = await apiMethods.uploadFile<ProductImageUploadResponse>(
//         `/products/${request.product_id}/image`,
//         request.file,
//         {
//           allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
//           maxFileSize: 5 * 1024 * 1024 // 5MB
//         }
//       );
      
//       return {
//         success: true,
//         message: 'Product image uploaded successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Upload product image error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get paginated products (helper method)
//    */
//   async getPaginatedProducts(page: number = 1, limit: number = 10, filters?: Omit<ProductFilters, 'skip' | 'limit'>): Promise<GetProductsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getProducts({ ...filters, skip, limit });
//   }

//   /**
//    * Search products
//    */
//   async searchProducts(query: string, filters?: ProductFilters): Promise<GetProductsResponse> {
//     return this.getProducts({ ...filters, search: query });
//   }

//   /**
//    * Get active products only
//    */
//   async getActiveProducts(filters?: ProductFilters): Promise<GetProductsResponse> {
//     return this.getProducts({ ...filters, active: true });
//   }

//   /**
//    * Get product statistics (if endpoint exists)
//    */
//   async getProductStatistics(): Promise<{ success: boolean; data?: ProductStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: ProductStatistics }>(`${this.baseUrl}/statistics`);
      
//       return {
//         success: true,
//         message: 'Statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get product statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }
// }

// // Export singleton instance
// export const adminProductsService = new AdminProductsService();
// export default adminProductsService;


export const getProductList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<productList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(`products/?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const createProduct = async (body: FormData) => {
  try {
    const response = await api.post("formData/products/", body);
    return response.data;
  } catch (error) {
    throw error;
  }
};
