import { Navigate, useRoutes } from "react-router-dom";
import Login from "./pages/auth/Login";
import Signup from "./pages/auth/Signup";
import OTPVerification from "./pages/auth/OTPVerification";
import UserOverview from "./pages/user/sub-pages/dashboard/UserOverview";
// import Referral from "./pages/user/sub-pages/UserReferral";
import Earnings from "./pages/user/sub-pages/earnings/Earnings";
import Products from "./pages/user/sub-pages/product/Products";
import AdminLayout from "./pages/admin/AdminLayout";
import AdminDashboardContent from "./pages/admin/components/admin-dashboard/AdminDashboardContent";
import UserLayout from "./pages/user/UserLayout";
import UserDeposit from "./pages/user/sub-pages/deposit/UserDeposit";
import UserWithdraw from "./pages/user/sub-pages/withdraw/UserWithdraw";
import UserReferral from "./pages/user/sub-pages/referral/UserReferral";
import AdminUsers from "./pages/admin/components/admin-users/AdminUsers";
import AdminDeposits from "./pages/admin/components/deposits/AdminDeposits";
import AdminWithdrawals from "./pages/admin/components/withdrawals/AdminWithdrawals";
import AdminProfit from "./pages/admin/components/profit/AdminProfit";
import AdminCurrency from "./pages/admin/components/currency/AdminCurrency";
import AdminDepositSlabs from "./pages/admin/components/deposit-slabs/AdminDepositSlabs";
import AdminProducts from "./pages/admin/components/products/AdminProducts";
import AdminSettings from "./pages/admin/components/settings/AdminSettings";


const AppRoutes = () => {
  const routes = useRoutes([
    { path: "/", element: <Login /> },
    { path: "/signup", element: <Signup /> },
    { path: "/verify-otp", element: <OTPVerification /> },
    {
      path: "/user",
      element: <UserLayout />,
      children: [
        { index: true, element: <Navigate to="dashboard" replace /> },
        { path: "dashboard", element: <UserOverview /> },
        { path: "deposit", element: <UserDeposit /> },
        { path: "withdraw", element: <UserWithdraw /> },
        { path: "referrals", element: <UserReferral /> },
        { path: "earnings", element: <Earnings /> },
        { path: "products", element: <Products /> },
      ],
    },
    {
      path: "/admin",
      element: <AdminLayout />,
      children: [
        { index: true, element: <Navigate to="dashboard" replace /> },
        { path: "dashboard", element: <AdminDashboardContent /> },
        { path: "users", element: <AdminUsers /> },
        { path: "deposits", element: <AdminDeposits /> },
        { path: "withdrawals", element: <AdminWithdrawals /> },
        { path: "profit", element: <AdminProfit /> },
        { path: "currency", element: <AdminCurrency /> },
        { path: "deposit-slabs", element: <AdminDepositSlabs /> },
        { path: "products", element: <AdminProducts /> },
        { path: "settings", element: <AdminSettings /> },
      ],
    },
  ]);

  return routes;
};

export default AppRoutes;