# 🍎 Fruit-O-Cart - Investment Platform

A modern React-based investment platform with fruit-themed branding, providing comprehensive user and admin interfaces for managing investments, deposits, withdrawals, and referrals.

## 🚀 Features

### User Features
- **Dashboard**: Comprehensive overview of investments and earnings
- **Deposit Management**: Easy deposit functionality with multiple payment options
- **Withdrawal System**: Secure withdrawal processing
- **Referral Program**: Built-in referral system with tracking
- **Product Catalog**: Browse and invest in various products
- **Earnings Tracking**: Real-time earnings and performance monitoring

### Admin Features
- **Admin Dashboard**: Complete system overview with analytics
- **User Management**: Comprehensive user administration
- **Transaction Management**: Deposit and withdrawal oversight
- **Product Management**: Add, edit, and manage investment products
- **Currency Management**: Multi-currency support
- **Deposit Slabs**: Tiered deposit structure management
- **Profit Management**: System profit tracking and reporting
- **Settings**: System configuration and settings

## 🛠️ Technology Stack

- **Frontend**: React 19.1.0 with TypeScript
- **Styling**: Tailwind CSS + SCSS
- **Routing**: React Router DOM 7.5.3
- **HTTP Client**: Axios 1.9.0 with comprehensive error handling
- **Charts**: ECharts 5.6.0 for data visualization
- **Icons**: Lucide React 0.503.0
- **Build Tool**: React Scripts 5.0.1

## 📋 Prerequisites

- Node.js 16+ 
- npm or yarn package manager
- Modern web browser

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd fruit-o-cart

# Install dependencies
npm install

# Start development server
npm start
```

### Environment Setup

Create a `.env` file in the root directory:

```env
REACT_APP_API_BASE_URL=http://***********:8003
REACT_APP_AUTH_API_BASE_URL=http://***********:8002
REACT_APP_API_TIMEOUT=30000
REACT_APP_MAX_FILE_SIZE=********
REACT_APP_RETRY_ATTEMPTS=3
```

## 📁 Project Structure

```
src/
├── api/                    # API layer
│   ├── axiosInstance.ts   # HTTP client configuration
│   └── examples.ts        # API usage examples
├── components/            # Reusable components
│   ├── header/           # Header components
│   ├── loader/           # Loading components
│   ├── sidebar/          # Navigation components
│   └── ErrorBoundary.tsx # Error boundary component
├── pages/                # Page components
│   ├── admin/           # Admin-specific pages
│   ├── auth/            # Authentication pages
│   ├── terms-policy/    # Legal pages
│   └── user/            # User-specific pages
├── models/              # TypeScript interfaces
├── App.tsx              # Main application component
├── AppRoutes.tsx        # Route definitions
├── ContextGlobal.tsx    # Global state management
└── index.tsx            # Application entry point
```

## 🎨 Design System

The application follows a consistent dark theme design:

- **Primary Background**: `#0D1117`
- **Card Background**: `#1A1F2E`
- **Input Background**: `#131722`
- **Primary Blue**: `#3B82F6` to `#6C8FFF`
- **Text Colors**: White, gray variants for hierarchy

## 🔧 Available Scripts

### Development
```bash
npm start          # Start development server
npm test           # Run test suite
npm run build      # Build for production
```

### Code Quality
```bash
npm run lint       # Run ESLint (when configured)
npm run format     # Format code with Prettier (when configured)
```

## 🔐 Authentication

The app uses JWT-based authentication with:
- Access and refresh token pattern
- Automatic token refresh
- Secure token storage
- Role-based routing (user/admin)

## 🌐 API Integration

Comprehensive API client with:
- Automatic authentication handling
- Error handling and retry logic
- File upload support
- Type-safe responses
- Request/response interceptors

## 📱 Responsive Design

Fully responsive design supporting:
- Mobile devices (320px+)
- Tablets (768px+)
- Desktop (1024px+)
- Large screens (1440px+)

## 🧪 Testing

```bash
npm test                    # Run all tests
npm test -- --coverage     # Run tests with coverage
npm test -- --watch        # Run tests in watch mode
```

## 🚀 Deployment

### Production Build
```bash
npm run build
```

The build folder will contain optimized production files ready for deployment.

### Environment Variables for Production
Ensure all environment variables are properly set for production deployment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in `/ecogo_docs/`

## 🔄 Recent Updates

- ✅ Fixed TypeScript configuration issues
- ✅ Added comprehensive error boundary
- ✅ Restructured app initialization
- ✅ Enhanced documentation
- ✅ Improved code organization

## 🚧 Enhancement Roadmap

### Phase 1: Critical Fixes ✅
- [x] Fix TypeScript configuration
- [x] Add error boundary
- [x] Restructure app initialization
- [ ] Fix any remaining import issues

### Phase 2: Design Standardization
- [ ] Standardize all components to match Login.tsx design
- [ ] Add consistent loading states
- [ ] Implement proper error handling UI
- [ ] Ensure responsive design across all components

### Phase 3: Performance & Quality
- [ ] Add code splitting and lazy loading
- [ ] Implement React.memo optimizations
- [ ] Add comprehensive error handling
- [ ] Improve accessibility (ARIA labels, keyboard navigation)

### Phase 4: Testing & Documentation
- [ ] Add unit tests for all components
- [ ] Add integration tests for user flows
- [ ] Add API testing
- [ ] Complete code documentation

## 📊 Current Status

**Overall Progress**: 25% Complete

**What's Working**:
- ✅ Authentication system
- ✅ Routing structure
- ✅ API integration
- ✅ Basic component structure
- ✅ Error boundary implementation

**Next Steps**:
1. Fix TypeScript configuration file (manual replacement needed)
2. Test application startup
3. Standardize component designs
4. Add comprehensive error handling
5. Implement performance optimizations
