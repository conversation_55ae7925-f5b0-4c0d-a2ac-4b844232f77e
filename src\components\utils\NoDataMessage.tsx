import React from 'react';
import {
  FileText,
  Users,
  CreditCard,
  TrendingUp,
  Package,
  DollarSign,
  Database,
  Search,
  Filter
} from 'lucide-react';

interface NoDataMessageProps {
  type?: 'users' | 'deposits' | 'withdrawals' | 'profits' | 'products' | 'transactions' | 'currency' | 'general';
  message?: string;
  description?: string;
  showSearchHint?: boolean;
  showFilterHint?: boolean;
  className?: string;
  height?: 'small' | 'medium' | 'large';
}

const NoDataMessage: React.FC<NoDataMessageProps> = ({
  type = 'general',
  message,
  description,
  showSearchHint = false,
  showFilterHint = false,
  className = '',
  height = 'medium'
}) => {
  // Icon mapping based on data type
  const getIcon = () => {
    switch (type) {
      case 'users':
        return <Users className="w-12 h-12 text-slate-500" />;
      case 'deposits':
        return <CreditCard className="w-12 h-12 text-slate-500" />;
      case 'withdrawals':
        return <DollarSign className="w-12 h-12 text-slate-500" />;
      case 'profits':
        return <TrendingUp className="w-12 h-12 text-slate-500" />;
      case 'products':
        return <Package className="w-12 h-12 text-slate-500" />;
      case 'transactions':
        return <FileText className="w-12 h-12 text-slate-500" />;
      case 'currency':
        return <DollarSign className="w-12 h-12 text-slate-500" />;
      default:
        return <Database className="w-12 h-12 text-slate-500" />;
    }
  };

  // Default messages based on type
  const getDefaultMessage = () => {
    if (message) return message;

    switch (type) {
      case 'users':
        return 'No users found';
      case 'deposits':
        return 'No deposits found';
      case 'withdrawals':
        return 'No withdrawals found';
      case 'profits':
        return 'No profit entries found';
      case 'products':
        return 'No products found';
      case 'transactions':
        return 'No transactions found';
      case 'currency':
        return 'No currencies found';
      default:
        return 'No data available';
    }
  };

  // Default descriptions based on type
  const getDefaultDescription = () => {
    if (description) return description;

    switch (type) {
      case 'users':
        return 'No users match your current criteria';
      case 'deposits':
        return 'No deposit records match your current criteria';
      case 'withdrawals':
        return 'No withdrawal requests match your current criteria';
      case 'profits':
        return 'No profit distribution records found';
      case 'products':
        return 'No products match your current criteria';
      case 'transactions':
        return 'No transaction history available';
      case 'currency':
        return 'No currency conversion rates have been configured yet';
      default:
        return 'Try adjusting your search or filter criteria';
    }
  };

  // Height classes
  const getHeightClass = () => {
    switch (height) {
      case 'small':
        return 'py-8';
      case 'medium':
        return 'py-12';
      case 'large':
        return 'py-16';
      default:
        return 'py-12';
    }
  };

  return (
    <div className={`text-center ${getHeightClass()} ${className}`}>
      <div className="flex flex-col items-center justify-center space-y-4">
        {/* Icon */}
        <div className="p-4 bg-slate-800/50 rounded-full border border-slate-700/50">
          {getIcon()}
        </div>

        {/* Main Message */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-slate-300">
            {getDefaultMessage()}
          </h3>
          <p className="text-sm text-slate-400 max-w-md">
            {getDefaultDescription()}
          </p>
        </div>

        {/* Hints */}
        {(showSearchHint || showFilterHint) && (
          <div className="flex flex-col sm:flex-row items-center gap-3 text-xs text-slate-500 mt-4">
            {showSearchHint && (
              <div className="flex items-center gap-1">
                <Search className="w-3 h-3" />
                <span>Try different search terms</span>
              </div>
            )}
            {showFilterHint && (
              <div className="flex items-center gap-1">
                <Filter className="w-3 h-3" />
                <span>Adjust your filters</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default NoDataMessage;
