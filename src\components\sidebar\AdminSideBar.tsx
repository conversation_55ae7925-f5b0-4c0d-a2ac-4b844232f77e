import { DollarSign, HandCoins, Layers, LayoutDashboard, LineChart, Package, Settings, Users, Wallet, X } from 'lucide-react';
import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom';

interface AdminSideBarProps {
    isMobileMenuOpen?: boolean;
    setIsMobileMenuOpen?: (open: boolean) => void;
}

function AdminSideBar({ isMobileMenuOpen = false, setIsMobileMenuOpen }: AdminSideBarProps) {
    const navigate = useNavigate();
    const location = useLocation();
    const [activeTab, setActiveTab] = useState("dashboard");

    // Extract active tab from current route
    useEffect(() => {
        const path = location.pathname;
        if (path.startsWith('/admin/')) {
            const tabFromPath = path.replace('/admin/', '').split('/')[0];
            if (tabFromPath) {
                setActiveTab(tabFromPath);
            } else {
                setActiveTab('dashboard');
            }
        }
    }, [location.pathname]);

    const handleTabChange = (tabId: string) => {
        navigate(`/admin/${tabId}`);
        // Close mobile menu after navigation
        if (setIsMobileMenuOpen) {
            setIsMobileMenuOpen(false);
        }
    };

    // Close mobile menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const sidebar = document.getElementById('admin-mobile-sidebar');
            const menuButton = document.getElementById('mobile-menu-button');

            if (isMobileMenuOpen && sidebar && !sidebar.contains(event.target as Node) &&
                menuButton && !menuButton.contains(event.target as Node)) {
                if (setIsMobileMenuOpen) {
                    setIsMobileMenuOpen(false);
                }
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isMobileMenuOpen, setIsMobileMenuOpen]);

    const menuItems = [
        { id: "dashboard", label: "Dashboard", icon: LayoutDashboard },
        { id: "users", label: "Users", icon: Users },
        { id: "deposits", label: "Deposits", icon: Wallet },
        { id: "withdrawals", label: "Withdrawals", icon: HandCoins },
        { id: "profit", label: "Profit", icon: LineChart },
        { id: "currency", label: "Currency", icon: DollarSign },
        { id: "deposit-slabs", label: "Deposit Slabs", icon: Layers },
        { id: "products", label: "Products", icon: Package },
        { id: "settings", label: "Settings", icon: Settings },
    ];

    return (
        <>
            {/* Desktop Sidebar */}
            <aside className="w-64 bg-[#1A1F2E] border-r border-gray-700 fixed left-0 top-16 bottom-0 hidden md:block overflow-y-auto z-10">
                <nav className="mt-6 px-4">
                    <div className="space-y-2">
                        {menuItems.map((item) => {
                            const IconComponent = item.icon;
                            return (
                                <button
                                    key={item.id}
                                    onClick={() => handleTabChange(item.id)}
                                    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out group desktop-hover ${
                                        activeTab === item.id
                                            ? "bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white shadow-lg shadow-blue-500/20"
                                            : "text-gray-300 hover:bg-[#131722] hover:text-white hover:shadow-md"
                                    }`}
                                >
                                    <IconComponent
                                        className={`w-5 h-5 mr-3 transition-all duration-200 ${
                                            activeTab === item.id
                                                ? "text-white"
                                                : "text-gray-400 group-hover:text-blue-400"
                                        }`}
                                    />
                                    <span className="transition-all duration-200">{item.label}</span>
                                </button>
                            );
                        })}
                    </div>
                </nav>
            </aside>

            {/* Mobile Sidebar Overlay */}
            {isMobileMenuOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                    onClick={() => setIsMobileMenuOpen && setIsMobileMenuOpen(false)}
                />
            )}

            {/* Mobile Sidebar */}
            <aside
                id="admin-mobile-sidebar"
                className={`fixed left-0 top-0 bottom-0 w-72 sm:w-80 bg-[#1A1F2E] border-r border-gray-700 transform transition-transform duration-300 ease-in-out z-50 md:hidden ${
                    isMobileMenuOpen ? 'translate-x-0 animate-slide-in-right' : '-translate-x-full animate-slide-out-right'
                } overflow-y-auto mobile-scroll`}
            >
                {/* Mobile Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-700 min-h-[64px]">
                    <div className="text-white font-bold text-lg sm:text-xl flex items-center min-w-0">
                        <div className="h-7 w-7 sm:h-8 sm:w-8 bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                            <Settings className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                        </div>
                        <span className="truncate">Admin Panel</span>
                    </div>
                    <button
                        onClick={() => setIsMobileMenuOpen && setIsMobileMenuOpen(false)}
                        className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg min-h-touch-target"
                        aria-label="Close menu"
                    >
                        <X className="w-5 h-5 sm:w-6 sm:h-6" />
                    </button>
                </div>

                {/* Mobile Navigation */}
                <nav className="mt-6 px-4 pb-6">
                    <div className="space-y-2">
                        {menuItems.map((item) => {
                            const IconComponent = item.icon;
                            return (
                                <button
                                    key={item.id}
                                    onClick={() => handleTabChange(item.id)}
                                    className={`mobile-nav-item w-full flex items-center px-4 py-4 text-base font-medium rounded-lg transition-all duration-200 ease-in-out group min-h-touch-target ${
                                        activeTab === item.id
                                            ? "bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white shadow-lg shadow-blue-500/20"
                                            : "text-gray-300 hover:bg-[#131722] hover:text-white hover:shadow-md"
                                    }`}
                                >
                                    <IconComponent
                                        className={`w-5 h-5 mr-4 transition-all duration-200 flex-shrink-0 ${
                                            activeTab === item.id
                                                ? "text-white"
                                                : "text-gray-400 group-hover:text-blue-400"
                                        }`}
                                    />
                                    <span className="transition-all duration-200 text-left">{item.label}</span>
                                </button>
                            );
                        })}
                    </div>
                </nav>
            </aside>
        </>
    );
}

export default AdminSideBar;