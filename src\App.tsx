import React from "react";
import './App.scss';
import AppRoutes from './AppRoutes';
import { BrowserRouter } from 'react-router-dom';
import { GlobalProvider } from './ContextGlobal';
import ErrorBoundary from './components/ErrorBoundary';
import { ToastProvider } from "./components/utils/HotToast";

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <GlobalProvider>
        <BrowserRouter>
          <ToastProvider>
            <div className="App">
              <AppRoutes />
            </div>
          </ToastProvider>
        </BrowserRouter>
      </GlobalProvider>
    </ErrorBoundary>
  );
};

export default App;
