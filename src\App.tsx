import React from "react";
import './App.scss';
import AppRoutes from './AppRoutes';
import { BrowserRouter } from 'react-router-dom';
import { GlobalProvider } from './ContextGlobal';
import ErrorBoundary from './components/ErrorBoundary';
import { ToastProvider } from "./components/utils/HotToast";
import { AlertProvider } from "./components/utils/Alert";

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <GlobalProvider>
        <BrowserRouter>
          <ToastProvider>
            <AlertProvider>
              <div className="App">
                <AppRoutes />
              </div>
            </AlertProvider>
          </ToastProvider>
        </BrowserRouter>
      </GlobalProvider>
    </ErrorBoundary>
  );
};

export default App;
