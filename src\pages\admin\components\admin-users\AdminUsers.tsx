import {
  Edit,
  Eye,
  Key,
  MoreVertical,
  Trash2,
  UserCheck,
  Search,
  Filter,
  X,
  Check,
  Calendar,
  Mail,
  Phone,
  MapPin,
  User as UserIcon,
  CreditCard,
  Flag,
  Shield,
  FileCheck,
} from "lucide-react";
import React, { JSX, useState, useEffect, useCallback } from "react";
import { getUserList, confirmKyc, rejectKyc, deleteUser } from "./admin-users-service";
import { user, StatusColor } from "./admin-users.model";
import TableShimmer from "../../../../components/shimmers/TableShimmer";
import TableNoDataRow from "../../../../components/utils/TableNoDataRow";
import NoDataMessage from "../../../../components/utils/NoDataMessage";
import KycVerificationModal from "./components/KycVerificationModal";
import { useAlert } from "../../../../components/utils/Alert";

function AdminUsers(): JSX.Element {
  const { fire } = useAlert();
  const [showDropdown, setShowDropdown] = useState<number | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{top: number, right: number} | null>(null);
  const [users, setUsers] = useState<user[]>([]);
  const [isShimmerLoading, setIsShimmerLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [status, setStatus] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(0);

  // View popup states
  const [showViewPopup, setShowViewPopup] = useState<boolean>(false);
  const [viewUser, setViewUser] = useState<user | null>(null);

  // Rejection modal states
  const [showRejectionModal, setShowRejectionModal] = useState<boolean>(false);
  const [rejectionReason, setRejectionReason] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  // KYC verification modal states
  const [showKycModal, setShowKycModal] = useState<boolean>(false);
  const [kycUser, setKycUser] = useState<user | null>(null);

  // Helper function to get status color based on KYC status
  const getStatusColor = (kycStatus: string): StatusColor => {
    switch (kycStatus.toLowerCase()) {
      case 'verified':
        return 'green';
      case 'rejected':
        return 'red';
      case 'pending':
      default:
        return 'yellow';
    }
  };

  const getUsers = useCallback(async (
    page: number,
    itemsPerPage: number,
    search: string,
    status?: string
  ) => {
    try {
      setIsShimmerLoading(true);
      // Convert page to skip (offset)
      const skip = (page - 1) * itemsPerPage;
      const response = await getUserList(skip, itemsPerPage, search, status);

      // Map the response to include status colors and formatted data
      const mappedUsers = response.items.map(user => ({
        ...user,
        status: user.kyc_status,
        statusColor: getStatusColor(user.kyc_status),
        date: new Date(user.created_at).toLocaleDateString()
      }));

      setUsers(mappedUsers);
      setTotalPages(response.total_pages);
    } catch (error) {
      console.log(error);
    } finally {
      setIsShimmerLoading(false);
    }
  }, []);

  // Load users when component mounts
  useEffect(() => {
    getUsers(currentPage, itemsPerPage, searchTerm, status);
  }, [getUsers, currentPage, itemsPerPage, searchTerm, status]);

  const getStatusStyles = (statusColor: StatusColor): string => {
    switch (statusColor) {
      case "green":
        return "bg-green-900/20 text-green-400 border border-green-900/30";
      case "yellow":
        return "bg-yellow-900/20 text-yellow-400 border border-yellow-900/30";
      case "red":
        return "bg-red-900/20 text-red-400 border border-red-900/30";
      default:
        return "bg-gray-700 text-gray-300 border border-gray-600";
    }
  };

  const handleDeleteClick = (userData: user): void => {
    setShowDropdown(null); // Close dropdown first

    // Show custom confirmation alert
    fire({
      icon: 'error',
      title: 'Delete User',
      text: `Are you sure you want to delete user "${userData.name}"? This action cannot be undone and will permanently remove their account and all associated data.`,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      onConfirm: () => handleDeleteConfirm(userData.id, userData.name),
      onCancel: () => console.log('User deletion cancelled')
    });
  };

  const handleDropdownToggle = (index: number, event?: React.MouseEvent): void => {
    if (showDropdown === index) {
      setShowDropdown(null);
      setDropdownPosition(null);
    } else {
      setShowDropdown(index);

      // Calculate position for fixed dropdown
      if (event) {
        const button = event.currentTarget as HTMLElement;
        const rect = button.getBoundingClientRect();

        // Position dropdown to the left of the button, almost touching
        setDropdownPosition({
          top: rect.bottom + window.scrollY + 2,
          right: window.innerWidth - rect.left + window.scrollX + 2  // 2px gap from button's left edge
        });
      }
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchTerm(e.target.value);
    getUsers(1, itemsPerPage, e.target.value, status);
  };

  const handleDeleteConfirm = async (userId: number, userName: string): Promise<void> => {
    setIsProcessing(true);
    try {
      const response = await deleteUser(userId);
      if (response.success) {
        // Remove user from the list
        setUsers(prevUsers => prevUsers.filter(u => u.id !== userId));
        console.log('User deleted successfully:', userName);

        // Show success alert
        fire({
          icon: 'success',
          title: 'User Deleted',
          text: `User "${userName}" has been successfully deleted.`,
          confirmButtonText: 'OK'
        });
      } else {
        console.error('Failed to delete user:', response.message);

        // Show error alert
        fire({
          icon: 'error',
          title: 'Delete Failed',
          text: response.message || 'Failed to delete user. Please try again.',
          confirmButtonText: 'OK'
        });
      }
    } catch (error) {
      console.error('Error deleting user:', error);

      // Show error alert
      fire({
        icon: 'error',
        title: 'Error',
        text: 'An unexpected error occurred while deleting the user. Please try again.',
        confirmButtonText: 'OK'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // View popup handlers
  const handleViewClick = (user: user): void => {
    setViewUser(user);
    setShowViewPopup(true);
    setShowDropdown(null);
  };

  const handleCloseViewPopup = (): void => {
    setShowViewPopup(false);
    setViewUser(null);
  };

  // KYC Status handlers
  const handleConfirmKyc = async (): Promise<void> => {
    if (!viewUser) return;

    setIsProcessing(true);
    try {
      const response = await confirmKyc(viewUser.id);
      if (response.success) {
        // Update the user in the list
        setUsers(prevUsers =>
          prevUsers.map(u =>
            u.id === viewUser.id
              ? { ...u, kyc_status: 'verified', status: 'Verified', statusColor: 'green' as const }
              : u
          )
        );
        handleCloseViewPopup();
        console.log('KYC confirmed successfully');
      } else {
        console.error('Failed to confirm KYC:', response.message);
      }
    } catch (error) {
      console.error('Error confirming KYC:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRejectClick = (): void => {
    setShowRejectionModal(true);
  };

  const handleRejectKyc = async (): Promise<void> => {
    if (!viewUser || !rejectionReason.trim()) return;

    setIsProcessing(true);
    try {
      const response = await rejectKyc(viewUser.id, rejectionReason);
      if (response.success) {
        // Update the user in the list
        setUsers(prevUsers =>
          prevUsers.map(u =>
            u.id === viewUser.id
              ? { ...u, kyc_status: 'rejected', status: 'Rejected', statusColor: 'red' as const }
              : u
          )
        );
        setShowRejectionModal(false);
        setRejectionReason("");
        handleCloseViewPopup();
        console.log('KYC rejected successfully');
      } else {
        console.error('Failed to reject KYC:', response.message);
      }
    } catch (error) {
      console.error('Error rejecting KYC:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelRejection = (): void => {
    setShowRejectionModal(false);
    setRejectionReason("");
  };

  // KYC Modal handlers
  const handleKycClick = (userData: user): void => {
    setKycUser(userData);
    setShowKycModal(true);
    setShowDropdown(null); // Close dropdown
  };

  const handleKycModalClose = (): void => {
    setShowKycModal(false);
    setKycUser(null);
  };

  const handleKycConfirm = async (userId: number): Promise<void> => {
    setIsProcessing(true);
    try {
      const response = await confirmKyc(userId);
      if (response.success) {
        // Update the user in the list
        setUsers(prevUsers =>
          prevUsers.map(u =>
            u.id === userId
              ? { ...u, kyc_status: 'verified', status: 'Verified', statusColor: 'green' as const }
              : u
          )
        );
        console.log('KYC confirmed successfully');
      } else {
        console.error('Failed to confirm KYC:', response.message);
        alert('Failed to confirm KYC: ' + response.message);
      }
    } catch (error) {
      console.error('Error confirming KYC:', error);
      alert('Error confirming KYC. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKycReject = async (userId: number, reason: string): Promise<void> => {
    setIsProcessing(true);
    try {
      const response = await rejectKyc(userId, reason);
      if (response.success) {
        // Update the user in the list
        setUsers(prevUsers =>
          prevUsers.map(u =>
            u.id === userId
              ? { ...u, kyc_status: 'rejected', status: 'Rejected', statusColor: 'red' as const }
              : u
          )
        );
        console.log('KYC rejected successfully');
      } else {
        console.error('Failed to reject KYC:', response.message);
        alert('Failed to reject KYC: ' + response.message);
      }
    } catch (error) {
      console.error('Error rejecting KYC:', error);
      alert('Error rejecting KYC. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-slate-800 rounded-lg border border-slate-700 responsive-container">
      {/* Header */}
      <div className="px-3 sm:px-4 lg:px-6 py-4 border-b border-slate-700">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
          <h3 className="text-lg font-semibold text-white">
            User KYC Verification
          </h3>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Search users..."
                onChange={handleSearchChange}
                className="mobile-input w-full sm:w-auto pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white placeholder-slate-400"
              />
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
            </div>
            <button
              type="button"
              className="mobile-button px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <Filter className="w-4 h-4" />
              <span className="hidden sm:inline">Filter</span>
              <span className="sm:hidden">Filter</span>
            </button>
          </div>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block overflow-x-auto" style={{ overflowY: 'visible' }}>
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-900/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                Registration Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                KYC Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {isShimmerLoading ? (
              <TableShimmer />
            ) : users && users.length > 0 ? (
              users.map((user, index) => (
                <tr
                  key={index}
                  className="hover:bg-slate-700/50 transition-colors duration-150"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-slate-600 flex items-center justify-center">
                        <span className="text-slate-200 font-medium text-sm">
                          {user.name.charAt(0)}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-white">
                          {user.name}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{user.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{user.date}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusStyles(
                        user.statusColor || 'yellow'
                      )}`}
                    >
                      {user.status || user.kyc_status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium relative">
                    <div className="relative">
                      <button
                        type="button"
                        onClick={(e) => handleDropdownToggle(index, e)}
                        className="text-slate-400 hover:text-white transition-colors duration-150 p-1 rounded-md hover:bg-slate-700"
                      >
                        <MoreVertical className="w-5 h-5" />
                      </button>

                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <TableNoDataRow
                colSpan={5}
                type="users"
                showSearchHint={!!searchTerm}
                showFilterHint={!!status}
              />
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden p-3 sm:p-4 space-y-3">
        {isShimmerLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="mobile-table-card animate-pulse">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="h-10 w-10 bg-slate-600 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-slate-600 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-slate-700 rounded w-full"></div>
                  <div className="h-3 bg-slate-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : users && users.length > 0 ? (
          users.map((user, index) => (
            <div key={index} className="mobile-table-card">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <div className="h-10 w-10 rounded-full bg-slate-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-slate-200 font-medium text-sm">
                      {user.name.charAt(0)}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-sm font-medium text-white truncate">
                      {user.name}
                    </div>
                    <div className="text-xs text-slate-400 truncate">
                      {user.email}
                    </div>
                  </div>
                </div>
                <div className="relative flex-shrink-0">
                  <button
                    type="button"
                    onClick={(e) => handleDropdownToggle(index, e)}
                    className="text-slate-400 hover:text-white transition-colors duration-150 p-2 rounded-md hover:bg-slate-700 min-h-touch-target"
                  >
                    <MoreVertical className="w-5 h-5" />
                  </button>

                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Registration:</span>
                  <span className="text-xs text-slate-300">{user.date}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">KYC Status:</span>
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${getStatusStyles(
                      user.statusColor || 'yellow'
                    )}`}
                  >
                    {user.status || user.kyc_status}
                  </span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <NoDataMessage
            type="users"
            showSearchHint={!!searchTerm}
            showFilterHint={!!status}
            height="medium"
          />
        )}
      </div>

      {/* Delete Confirmation Dialog
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-lg border border-slate-700 max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-red-900/20 flex items-center justify-center">
                  <Trash2 className="w-6 h-6 text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Delete User</h3>
                  <p className="text-sm text-slate-400">This action cannot be undone</p>
                </div>
              </div>
              <p className="text-slate-300 mb-6">
                Are you sure you want to delete <strong className="text-white">{selectedUser?.name}</strong>?
                This will permanently remove their account and all associated data.
              </p>
              <div className="flex gap-3 justify-end">
                <button
                  type="button"
                  onClick={handleDeleteCancel}
                  className="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white border border-slate-600 rounded-lg hover:bg-slate-700 transition-colors duration-150"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDeleteConfirm}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-150"
                >
                  Delete User
                </button>
              </div>
            </div>
          </div>
        </div>
      )} */}

      {/* User View Popup */}
      {showViewPopup && viewUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-lg border border-slate-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="p-6 border-b border-slate-700 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-full bg-slate-600 flex items-center justify-center">
                  <span className="text-slate-200 font-medium text-lg">
                    {viewUser.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white">{viewUser.name}</h3>
                  <p className="text-sm text-slate-400">User Details</p>
                </div>
              </div>
              <button
                type="button"
                onClick={handleCloseViewPopup}
                className="text-slate-400 hover:text-white transition-colors duration-150 p-2 rounded-md hover:bg-slate-700"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Personal Information */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <UserIcon className="w-5 h-5 text-blue-400" />
                  Personal Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Full Name</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.name}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Date of Birth</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.dob}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Nationality</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.nationality}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Country of Residence</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.country_of_residence}</p>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Mail className="w-5 h-5 text-green-400" />
                  Contact Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Email</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.email}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Phone</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">
                      {viewUser.country_code} {viewUser.phone}
                    </p>
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <label className="text-sm font-medium text-slate-300">Address</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.address}</p>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Shield className="w-5 h-5 text-purple-400" />
                  Account Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">User Rank</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg capitalize">{viewUser.rank}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Preferred Currency</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.preferred_currency}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Referral Code</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.referral_code}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Registration Date</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">
                      {new Date(viewUser.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              {/* KYC Information */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5 text-yellow-400" />
                  KYC Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">National ID</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.national_id}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Passport</label>
                    <p className="text-white bg-slate-700/50 px-3 py-2 rounded-lg">{viewUser.passport}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">KYC Status</label>
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          viewUser.kyc_status === 'verified'
                            ? 'bg-green-900/20 text-green-400 border border-green-900/30'
                            : viewUser.kyc_status === 'pending'
                            ? 'bg-yellow-900/20 text-yellow-400 border border-yellow-900/30'
                            : 'bg-red-900/20 text-red-400 border border-red-900/30'
                        }`}
                      >
                        {viewUser.kyc_status}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-300">Account Status</label>
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          viewUser.is_active
                            ? 'bg-green-900/20 text-green-400 border border-green-900/30'
                            : 'bg-red-900/20 text-red-400 border border-red-900/30'
                        }`}
                      >
                        {viewUser.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="p-6 border-t border-slate-700 flex justify-end gap-3">
              {viewUser.kyc_status === 'verified' ? (
                <button
                  type="button"
                  onClick={handleCloseViewPopup}
                  className="px-6 py-2 text-sm font-medium text-slate-300 hover:text-white border border-slate-600 rounded-lg hover:bg-slate-700 transition-colors duration-150"
                >
                  Close
                </button>
              ) : viewUser.kyc_status === 'pending' ? (
                <>
                  <button
                    type="button"
                    onClick={handleRejectClick}
                    disabled={isProcessing}
                    className="px-6 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    <X className="w-4 h-4" />
                    {isProcessing ? 'Processing...' : 'Reject'}
                  </button>
                  <button
                    type="button"
                    onClick={handleConfirmKyc}
                    disabled={isProcessing}
                    className="px-6 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    <Check className="w-4 h-4" />
                    {isProcessing ? 'Processing...' : 'Confirm'}
                  </button>
                </>
              ) : (
                <button
                  type="button"
                  onClick={handleCloseViewPopup}
                  className="px-6 py-2 text-sm font-medium text-slate-300 hover:text-white border border-slate-600 rounded-lg hover:bg-slate-700 transition-colors duration-150"
                >
                  Close
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Rejection Reason Modal */}
      {showRejectionModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-lg border border-slate-700 max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-red-900/20 flex items-center justify-center">
                  <X className="w-6 h-6 text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Reject KYC</h3>
                  <p className="text-sm text-slate-400">Please provide a reason for rejection</p>
                </div>
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Rejection Reason
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Enter the reason for rejecting this KYC application..."
                  className="w-full h-32 px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm resize-vertical"
                />
              </div>
              <div className="flex gap-3 justify-end">
                <button
                  type="button"
                  onClick={handleCancelRejection}
                  disabled={isProcessing}
                  className="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white border border-slate-600 rounded-lg hover:bg-slate-700 transition-colors duration-150 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleRejectKyc}
                  disabled={isProcessing || !rejectionReason.trim()}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? 'Rejecting...' : 'Reject KYC'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* KYC Verification Modal */}
      <KycVerificationModal
        isOpen={showKycModal}
        onClose={handleKycModalClose}
        user={kycUser}
        onConfirm={handleKycConfirm}
        onReject={handleKycReject}
        isProcessing={isProcessing}
      />

      {/* Fixed Position Dropdown - Renders outside table to avoid clipping */}
      {showDropdown !== null && dropdownPosition && users[showDropdown] && (
        <div
          className="fixed w-48 bg-slate-700 rounded-lg shadow-xl border border-slate-600 z-[9999]"
          style={{
            top: `${dropdownPosition.top}px`,
            right: `${dropdownPosition.right}px`,
          }}
        >
          <div className="py-1">
            <button
              type="button"
              onClick={() => handleViewClick(users[showDropdown])}
              className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
            >
              <Eye className="w-4 h-4" />
              <span>View Details</span>
            </button>
            <button
              type="button"
              className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
            >
              <Edit className="w-4 h-4" />
              <span>Edit Profile</span>
            </button>
            <button
              type="button"
              className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
            >
              <Key className="w-4 h-4" />
              <span>Reset Password</span>
            </button>
            <button
              type="button"
              onClick={() => handleKycClick(users[showDropdown])}
              className="w-full px-4 py-2 text-left text-sm text-slate-200 hover:bg-slate-600 flex items-center gap-3 transition-colors duration-150"
            >
              <UserCheck className="w-4 h-4" />
              <span>Change KYC</span>
            </button>
            <div className="border-t border-slate-600 my-1"></div>
            <button
              type="button"
              onClick={() => handleDeleteClick(users[showDropdown])}
              className="w-full px-4 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-3 transition-colors duration-150"
            >
              <Trash2 className="w-4 h-4" />
              <span>Delete User</span>
            </button>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown !== null && (
        <div
          className="fixed inset-0 z-[9998]"
          onClick={() => {
            setShowDropdown(null);
            setDropdownPosition(null);
          }}
        ></div>
      )}
    </div>
  );
}

export default AdminUsers;
