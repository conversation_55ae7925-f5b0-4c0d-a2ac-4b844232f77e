// import React, { useState } from "react";

// function UserDeposit() {
//   const [copySuccess, setCopySuccess] = useState(false);
//   const [selectedAmount, setSelectedAmount] = useState("");
//   const [transactionId, setTransactionId] = useState("");
//   const [isCustomAmount, setIsCustomAmount] = useState(false);
//   const [isDropdownOpen, setIsDropdownOpen] = useState(false);
//   const [searchTerm, setSearchTerm] = useState("");
//   const [sortBy, setSortBy] = useState("date");
//   const [sortDirection, setSortDirection] = useState("desc");
//   const [currentPage, setCurrentPage] = useState(1);

//   const depositHistory = [
//     {
//       id: 1,
//       date: "Apr 29, 2025 14:32",
//       amount: 100,
//       txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
//       status: "Approved",
//     },
//     {
//       id: 2,
//       date: "Apr 27, 2025 09:15",
//       amount: 250,
//       txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
//       status: "Approved",
//     },
//     {
//       id: 3,
//       date: "Apr 25, 2025 18:45",
//       amount: 500,
//       txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
//       status: "Pending",
//     },
//     {
//       id: 4,
//       date: "Apr 20, 2025 11:20",
//       amount: 50,
//       txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
//       status: "Rejected",
//     },
//     {
//       id: 5,
//       date: "Apr 15, 2025 16:05",
//       amount: 100,
//       txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
//       status: "Approved",
//     },
//   ];

//   const depositPackages = [
//     {
//       name: "Starter Pack",
//       value: "25",
//       image:
//         "https://readdy.ai/api/search-image?query=A%20minimalist%20illustration%20of%20a%20seedling%20or%20small%20plant%20emerging%20from%20soil%2C%20symbolizing%20growth%20and%20beginnings%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=1&orientation=squarish",
//     },
//     {
//       name: "Growth Pack",
//       value: "50",
//       image:
//         "https://readdy.ai/api/search-image?query=A%20modern%20minimalist%20illustration%20of%20ascending%20steps%20or%20staircase%2C%20representing%20progress%20and%20advancement%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=2&orientation=squarish",
//     },
//     {
//       name: "Professional Pack",
//       value: "100",
//       image:
//         "https://readdy.ai/api/search-image?query=A%20sleek%20briefcase%20or%20professional%20tool%20illustration%2C%20symbolizing%20business%20and%20expertise%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=3&orientation=squarish",
//     },
//     {
//       name: "Enterprise Pack",
//       value: "200",
//       image:
//         "https://readdy.ai/api/search-image?query=A%20modern%20building%20or%20skyscraper%20illustration%2C%20representing%20enterprise%20and%20large%20scale%20operations%2C%20with%20a%20clean%20white%20background%20and%20professional%20lighting&width=100&height=100&seq=4&orientation=squarish",
//     },
//   ];

//   const usdtRate = 1.0;
//   const adminWalletAddress = "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F";
//   const totalDeposited = depositHistory
//     .filter((item) => item.status === "Approved")
//     .reduce((sum, item) => sum + item.amount, 0);
//   const currentBalance = 5280.42;
//   const filteredHistory = depositHistory;
//   const itemsPerPage = 5;
//   const totalPages = Math.ceil(filteredHistory.length / itemsPerPage);

//   const paginatedHistory = filteredHistory.slice(
//     (currentPage - 1) * itemsPerPage,
//     currentPage * itemsPerPage
//   );

//   const copyToClipboard = (text: string) => {
//     navigator.clipboard.writeText(text);
//     setCopySuccess(true);
//     setTimeout(() => setCopySuccess(false), 2000);
//   };

//   const handleSort = (column: string) => {
//     if (sortBy === column) {
//       setSortDirection(sortDirection === "asc" ? "desc" : "asc");
//     } else {
//       setSortBy(column);
//       setSortDirection("asc");
//     }
//   };

//   const handleSubmitDeposit = (e: React.FormEvent) => {
//     e.preventDefault();
//     if (Number(selectedAmount) < 25) {
//       return;
//     }
//     // Handle deposit submission logic here
//     alert(
//       `Deposit request submitted: ${selectedAmount} USDT with Transaction ID: ${transactionId}`
//     );
//     setSelectedAmount("");
//     setTransactionId("");
//     setIsCustomAmount(false);
//   };

//   return (
//     <div className="min-h-screen bg-[#0B1221] p-6 text-white">
//       <div className="flex items-center mb-4">
//         <h1 className="text-2xl font-bold text-white">Deposit USDT</h1>
//       </div>
//       <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
//         {/* Admin Wallet Section */}
//         <div className="lg:col-span-2 bg-[#151C2D] rounded-lg shadow-md p-6 border border-[#1E293B]">
//           <h2 className="text-lg font-semibold text-white mb-3">
//             Admin USDT Wallet (TRC20)
//           </h2>
//           <div className="flex flex-col md:flex-row gap-4">
//             <div className="flex-1">
//               <div className="border border-[#1E293B] rounded-lg p-4 bg-[#0B1221] mb-4 relative">
//                 <p className="text-sm font-medium text-white break-all">
//                   {adminWalletAddress}
//                 </p>
//                 <button
//                   onClick={() => copyToClipboard(adminWalletAddress)}
//                   className="absolute right-2 top-2 text-[#4A6FFF] hover:text-[#6C8FFF] cursor-pointer"
//                 >
//                   <i className="fas fa-copy"></i>
//                 </button>
//                 {copySuccess && (
//                   <div className="absolute -top-10 right-0 bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
//                     Copied to clipboard!
//                   </div>
//                 )}
//               </div>
//               <div className="bg-[#1E293B] border-l-4 border-yellow-400 p-4 mb-4">
//                 <div className="flex">
//                   <div className="flex-shrink-0">
//                     <i className="fas fa-exclamation-triangle text-yellow-400"></i>
//                   </div>
//                   <div className="ml-3">
//                     <p className="text-sm text-yellow-300">
//                       <strong>Important:</strong> Only send USDT via the TRC20
//                       network. Other networks are not supported and may result
//                       in loss of funds.
//                     </p>
//                   </div>
//                 </div>
//               </div>
//               <div className="text-sm text-[#94A3B8] mb-4">
//                 <p className="mb-2">
//                   <strong className="text-white">Current Exchange Rate:</strong>{" "}
//                   1 USDT = ${usdtRate.toFixed(2)} USD
//                 </p>
//                 <p>
//                   After sending USDT, please submit your transaction ID in the
//                   form below to complete your deposit request.
//                 </p>
//               </div>
//             </div>
//             <div className="w-full md:w-48 flex flex-col items-center">
//               <div className="bg-white p-2 border border-gray-200 rounded-lg mb-2">
//                 <div className="bg-gray-100 p-4 rounded">
//                   <img
//                     src="https://readdy.ai/api/search-image?query=QR%20code%20for%20cryptocurrency%20wallet%20address%2C%20clean%20minimalist%20design%2C%20high%20contrast%20black%20pattern%20on%20white%20background%2C%20clear%20and%20scannable%2C%20professional%20fintech%20style%2C%20digital%20payment%20technology&width=200&height=200&seq=1&orientation=squarish"
//                     alt="USDT Wallet QR Code"
//                     className="w-full h-auto"
//                   />
//                 </div>
//               </div>
//               <p className="text-xs text-center text-[#94A3B8]">
//                 Scan to deposit USDT
//               </p>
//             </div>
//           </div>
//         </div>
//         {/* Summary Section */}
//         <div className="bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
//           <h2 className="text-lg font-semibold text-white mb-3">Summary</h2>
//           <div className="space-y-3">
//             <div>
//               <p className="text-sm text-[#94A3B8] mb-1">
//                 Total Deposited Amount
//               </p>
//               <div className="flex items-baseline">
//                 <span className="text-2xl font-bold text-white">
//                   {totalDeposited}
//                 </span>
//                 <span className="ml-1 text-sm text-[#94A3B8]">USDT</span>
//               </div>
//               <p className="text-xs text-[#94A3B8]">
//                 (≈ ${(totalDeposited * usdtRate).toFixed(2)} USD)
//               </p>
//             </div>
//             <div className="border-t border-[#1E293B] pt-4">
//               <p className="text-sm text-[#94A3B8] mb-1">Current Balance</p>
//               <div className="flex items-baseline">
//                 <span className="text-2xl font-bold text-white">
//                   {currentBalance.toFixed(2)}
//                 </span>
//                 <span className="ml-1 text-sm text-[#94A3B8]">USDT</span>
//               </div>
//               <p className="text-xs text-[#94A3B8]">
//                 (≈ ${(currentBalance * usdtRate).toFixed(2)} USD)
//               </p>
//               <div className="flex items-center mt-1 text-green-400 text-xs">
//                 <i className="fas fa-arrow-up mr-1"></i>
//                 <span>+2.7% from yesterday</span>
//               </div>
//             </div>
//             <div className="border-t border-[#1E293B] pt-4 text-xs text-[#94A3B8]">
//               <p>Last updated: Apr 29, 2025, 14:30</p>
//             </div>
//           </div>
//         </div>
//       </div>
//       {/* Deposit Request Form */}
//       <div className="mt-6 bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
//         <h2 className="text-lg font-semibold text-white mb-3">
//           Deposit Request Form
//         </h2>
//         <form onSubmit={handleSubmitDeposit} className="max-w-2xl">
//           <div className="mb-3">
//             <label
//               htmlFor="network"
//               className="block text-sm font-medium text-white mb-1"
//             >
//               Select Network
//             </label>
//             <select
//               id="network"
//               className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] mb-4 bg-[#0B1221] text-white"
//               defaultValue="trc20"
//             >
//               <option value="trc20">TRC20 (Recommended)</option>
//               <option value="erc20" disabled>
//                 ERC20 (Not Supported)
//               </option>
//               <option value="bep20" disabled>
//                 BEP20 (Not Supported)
//               </option>
//             </select>
//             <label
//               htmlFor="amount"
//               className="block text-sm font-medium text-white mb-1"
//             >
//               Amount (USDT)
//             </label>
//             <div className="space-y-2">
//               <div className="relative">
//                 <button
//                   type="button"
//                   onClick={() => setIsDropdownOpen(!isDropdownOpen)}
//                   className="w-full px-4 py-3 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] mb-2 bg-[#0B1221] text-white flex items-center justify-between"
//                 >
//                   <div className="flex items-center">
//                     {selectedAmount ? (
//                       <>
//                         <img
//                           src={
//                             depositPackages.find(
//                               (pkg) => pkg.value === selectedAmount,
//                             )?.image || ""
//                           }
//                           alt=""
//                           className="w-8 h-8 rounded-full object-cover mr-3"
//                         />
//                         <div className="flex flex-col items-start">
//                           <span className="text-[#94A3B8] text-sm">
//                             {depositPackages.find(
//                               (pkg) => pkg.value === selectedAmount,
//                             )?.name || "Custom Amount"}
//                           </span>
//                           <span className="text-white font-semibold">
//                             {selectedAmount} USDT
//                           </span>
//                         </div>
//                       </>
//                     ) : (
//                       <span className="text-[#5D6B98]">
//                         Select a deposit package
//                       </span>
//                     )}
//                   </div>
//                   <i
//                     className={`fas fa-chevron-${isDropdownOpen ? "up" : "down"} text-[#5D6B98]`}
//                   ></i>
//                 </button>
//                 {isDropdownOpen && (
//                   <div className="absolute z-10 w-full mt-1 bg-[#0B1221] border border-[#1E293B] rounded-lg shadow-lg">
//                     <div className="py-2 max-h-60 overflow-y-auto">
//                       {depositPackages.map((pkg) => (
//                         <button
//                           key={pkg.value}
//                           type="button"
//                           onClick={() => {
//                             setSelectedAmount(pkg.value);
//                             setIsDropdownOpen(false);
//                           }}
//                           className="w-full px-4 py-2 flex items-center hover:bg-[#1E293B] transition-colors"
//                         >
//                           <img
//                             src={pkg.image}
//                             alt={pkg.name}
//                             className="w-8 h-8 rounded-full object-cover mr-3"
//                           />
//                           <div className="flex flex-col items-start">
//                             <span className="text-white">{pkg.name}</span>
//                             <span className="text-sm text-[#5D6B98]">
//                               {pkg.value} USDT
//                             </span>
//                           </div>
//                         </button>
//                       ))}
//                       <button
//                         type="button"
//                         onClick={() => {
//                           setSelectedAmount("");
//                           setIsDropdownOpen(false);
//                         }}
//                         className="w-full px-4 py-2 flex items-center hover:bg-[#1E293B] transition-colors"
//                       >
//                         <div className="w-8 h-8 rounded-full bg-[#1E293B] flex items-center justify-center mr-3">
//                           <i className="fas fa-pen-to-square text-[#5D6B98]"></i>
//                         </div>
//                         <span className="text-white">Manual Entry</span>
//                       </button>
//                     </div>
//                   </div>
//                 )}
//               </div>
//               {selectedAmount === "" && (
//                 <div className="relative">
//                   <div className="flex items-center">
//                     <input
//                       type="number"
//                       min="25"
//                       step="1"
//                       value={selectedAmount}
//                       onChange={(e) => {
//                         const value = e.target.value;
//                         setSelectedAmount(value);
//                       }}
//                       placeholder="Enter amount (min. 25 USDT)"
//                       className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98]"
//                       required
//                     />
//                     <span className="absolute right-3 text-[#5D6B98] text-sm">
//                       USDT
//                     </span>
//                   </div>
//                 </div>
//               )}
//               {selectedAmount !== "" && !isNaN(Number(selectedAmount)) && (
//                 <div className="text-sm text-[#94A3B8]">
//                   Approximate value: $
//                   {(Number(selectedAmount) * usdtRate).toFixed(2)} USD
//                 </div>
//               )}
//               {selectedAmount !== "" && Number(selectedAmount) < 25 && (
//                 <div className="text-sm text-[#DC2626]">
//                   <i className="fas fa-exclamation-circle mr-1"></i>
//                   Minimum deposit amount is 25 USDT
//                 </div>
//               )}
//             </div>
//           </div>
//           <div className="mb-4">
//             <label
//               htmlFor="transactionId"
//               className="block text-sm font-medium text-white mb-1"
//             >
//               Enter Transaction ID
//             </label>
//             <input
//               type="text"
//               id="transactionId"
//               value={transactionId}
//               onChange={(e) => setTransactionId(e.target.value)}
//               placeholder="Enter the transaction ID from your wallet"
//               className="w-full px-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98]"
//               required
//             />
//             <p className="mt-1 text-xs text-[#94A3B8]">
//               You can find the transaction ID in your wallet after sending USDT
//               to the admin wallet address.
//             </p>
//           </div>
//           <div className="bg-[#1E293B] border-l-4 border-yellow-400 p-4 mb-4">
//             <div className="flex">
//               <div className="flex-shrink-0">
//                 <i className="fas fa-exclamation-triangle text-yellow-400"></i>
//               </div>
//               <div className="ml-3">
//                 <p className="text-sm text-yellow-300">
//                   Please ensure you enter the correct transaction ID. Incorrect
//                   IDs may delay the processing of your deposit.
//                 </p>
//               </div>
//             </div>
//           </div>
//           <button
//             type="submit"
//             className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white px-6 py-3 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity !rounded-button whitespace-nowrap cursor-pointer shadow-lg"
//           >
//             Submit Deposit Request
//           </button>
//         </form>
//       </div>
//       {/* Deposit Logs Table */}
//       <div className="mt-6 bg-[#151C2D] rounded-lg shadow-md p-4 border border-[#1E293B]">
//         <h2 className="text-lg font-semibold text-white mb-3">
//           Deposit History
//         </h2>
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-3">
//           <div className="relative">
//             <input
//               type="text"
//               placeholder="Search deposits..."
//               value={searchTerm}
//               onChange={(e) => setSearchTerm(e.target.value)}
//               className="pl-10 pr-4 py-2 border border-[#1E293B] rounded-lg focus:ring-[#3B82F6] focus:border-[#3B82F6] bg-[#0B1221] text-white placeholder-[#5D6B98] text-sm"
//             />
//             <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//               <i className="fas fa-search text-[#94A3B8]"></i>
//             </div>
//           </div>
//         </div>
//         <div className="overflow-x-auto">
//           <table className="min-w-full divide-y divide-[#1E293B]">
//             <thead className="bg-[#0B1221]">
//               <tr>
//                 <th
//                   scope="col"
//                   className="px-6 py-3 text-left text-xs font-medium text-[#94A3B8] uppercase tracking-wider cursor-pointer"
//                   onClick={() => handleSort("date")}
//                 >
//                   <div className="flex items-center">
//                     Date & Time
//                     {sortBy === "date" && (
//                       <i
//                         className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
//                       ></i>
//                     )}
//                   </div>
//                 </th>
//                 <th
//                   scope="col"
//                   className="px-6 py-3 text-left text-xs font-medium text-[#94A3B8] uppercase tracking-wider cursor-pointer"
//                   onClick={() => handleSort("amount")}
//                 >
//                   <div className="flex items-center">
//                     Amount (USDT)
//                     {sortBy === "amount" && (
//                       <i
//                         className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
//                       ></i>
//                     )}
//                   </div>
//                 </th>
//                 <th
//                   scope="col"
//                   className="px-6 py-3 text-left text-xs font-medium text-[#94A3B8] uppercase tracking-wider"
//                 >
//                   Transaction ID
//                 </th>
//                 <th
//                   scope="col"
//                   className="px-6 py-3 text-left text-xs font-medium text-[#94A3B8] uppercase tracking-wider"
//                 >
//                   Status
//                 </th>
//               </tr>
//             </thead>
//             <tbody className="bg-[#151C2D] divide-y divide-[#1E293B]">
//               {paginatedHistory.length > 0 ? (
//                 paginatedHistory.map((item) => (
//                   <tr key={item.id}>
//                     <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
//                       {item.date}
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
//                       {item.amount} USDT
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap text-sm text-[#94A3B8]">
//                       <div className="flex items-center">
//                         <span className="truncate max-w-xs">
//                           {item.txId.substring(0, 10)}...
//                         </span>
//                         <button
//                           onClick={() => copyToClipboard(item.txId)}
//                           className="ml-2 text-[#4A6FFF] hover:text-[#6C8FFF] cursor-pointer"
//                         >
//                           <i className="fas fa-copy"></i>
//                         </button>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <span
//                         className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                           item.status === "Approved"
//                             ? "bg-[#065F46] text-[#10B981]"
//                             : item.status === "Pending"
//                               ? "bg-[#854D0E] text-[#F59E0B]"
//                               : "bg-[#991B1B] text-[#F87171]"
//                         }`}
//                       >
//                         {item.status}
//                       </span>
//                     </td>
//                   </tr>
//                 ))
//               ) : (
//                 <tr>
//                   <td
//                     colSpan={4}
//                     className="px-6 py-4 text-center text-sm text-[#94A3B8]"
//                   >
//                     No deposit history found
//                   </td>
//                 </tr>
//               )}
//             </tbody>
//           </table>
//         </div>
//         {totalPages > 1 && (
//           <div className="flex items-center justify-between mt-4">
//             <div className="text-sm text-[#94A3B8]">
//               Showing{" "}
//               <span className="font-medium">
//                 {(currentPage - 1) * itemsPerPage + 1}
//               </span>{" "}
//               to{" "}
//               <span className="font-medium">
//                 {Math.min(currentPage * itemsPerPage, filteredHistory.length)}
//               </span>{" "}
//               of <span className="font-medium">{filteredHistory.length}</span>{" "}
//               results
//             </div>
//             <div className="flex space-x-2">
//               <button
//                 onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
//                 disabled={currentPage === 1}
//                 className={`px-3 py-1 rounded-md ${currentPage === 1 ? "bg-[#1E293B] text-[#64748B] cursor-not-allowed" : "bg-[#151C2D] text-[#4A6FFF] hover:bg-[#1E293B] cursor-pointer"} border border-[#1E293B]`}
//               >
//                 Previous
//               </button>
//               <button
//                 onClick={() =>
//                   setCurrentPage((prev) => Math.min(prev + 1, totalPages))
//                 }
//                 disabled={currentPage === totalPages}
//                 className={`px-3 py-1 rounded-md ${currentPage === totalPages ? "bg-[#1E293B] text-[#64748B] cursor-not-allowed" : "bg-[#151C2D] text-[#4A6FFF] hover:bg-[#1E293B] cursor-pointer"} border border-[#1E293B]`}
//               >
//                 Next
//               </button>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }

// export default UserDeposit;

import React, { ChangeEvent, FormEvent, useEffect, useState } from "react";
import { create_deposit, deposit } from "./deposit.model";
import Pagination from "../../../../components/Pagination/Pagination";
import { createDeposite, getUserDepositList } from "./deposit-service";
import TableShimmer from "../../../../components/shimmers/TableShimmer";
import { useToast } from "../../../../components/utils/HotToast";
import { useGlobalContext } from "../../../../ContextGlobal";

function UserDeposit() {
  const { success, error, warning, info } = useToast();
  const [copySuccess, setCopySuccess] = useState(false);
  const [selectedAmount, setSelectedAmount] = useState("");
  const [transactionId, setTransactionId] = useState("");
  const [isCustomAmount, setIsCustomAmount] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [depositHistory, setDepositHistory] = useState<deposit[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPage, setTotalPage] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(true);
  const [statusFilter, setStatusFilter] = useState("");
  const { setIsLoading } = useGlobalContext();

  const depositPackages = [
    {
      name: "Starter Pack",
      value: "25",
      icon: "🌱",
    },
    {
      name: "Growth Pack",
      value: "50",
      icon: "📈",
    },
    {
      name: "Professional Pack",
      value: "100",
      icon: "💼",
    },
    {
      name: "Enterprise Pack",
      value: "200",
      icon: "🏢",
    },
  ];

  const usdtRate = 1.0;
  const adminWalletAddress = "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F";
  const totalDeposited = depositHistory
    .filter((item) => item.status === "Approved")
    .reduce((sum, item) => sum + item.amount, 0);
  const currentBalance = 5280.42;

  const getDepositList = async (
    page: number,
    itemsPerPageCount: number,
    searchValue: string,
    status:string
  ) => {
    try {
      setIsTableLoading(true);
      const response = await getUserDepositList(
        (page - 1) * itemsPerPageCount,
        itemsPerPageCount,
        searchValue,
        status
      );
      setIsTableLoading(false);
      setDepositHistory(response.items);
      setTotalPage(response.total_pages);
      setCurrentPage(response.current_page);
    } catch (error) {
      console.log(error);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    success("Copied to clipboard!");
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };

  const handleAmountSelection = (value: string) => {
    setSelectedAmount(value);
    setIsCustomAmount(value === "custom");
    setIsDropdownOpen(false);
  };

  const handleCustomAmountChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSelectedAmount(value);
  };

  const handleSubmitDeposit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (Number(selectedAmount) < 25) {
      alert("Minimum deposit amount is 25 USDT");
      return;
    }
    if (!transactionId.trim()) {
      alert("Please enter a valid transaction ID");
      return;
    }
    // Handle deposit submission logic here
    handleDepositSubmitAPi({
      admin_wallet_id: adminWalletAddress,
      amount: Number(selectedAmount),
      transaction_id: transactionId,
      status: "Pending",
    });
    setSelectedAmount("");
    setTransactionId("");
    setIsCustomAmount(false);
  };

  const handleItemsPerPage = (value: number) => {
    setItemsPerPage(value);
    getDepositList(1, value, searchTerm , statusFilter);
  };

  const handlePage = (page: number) => {
    console.log("page changed", page);

    setCurrentPage(page);
    getDepositList(page, itemsPerPage, searchTerm , statusFilter);
  };

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    getDepositList(1, itemsPerPage, value , statusFilter);
  };

  const handleFilterChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setStatusFilter(value);
    getDepositList(1, itemsPerPage, searchTerm, value);
  };
  
  const handleDepositSubmitAPi = async (body: create_deposit) => {
    setIsLoading(true);
    try {
      const response = await createDeposite(body);
      if (response) {
        success("Deposit request submitted successfully!");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getDepositList(1, 5, "" , "");
  }, []);

  // Generate QR Code using a more reliable service
  const generateQRCode = (text: string) => {
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(
      text
    )}&bgcolor=FFFFFF&color=000000&margin=10`;
  };

  return (
    <div className="min-h-screen bg-[#0D1117] p-6 text-white text-left">
      <div className="flex items-center mb-6">
        <h1 className="text-3xl font-bold text-white">Deposit USDT</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Admin Wallet Section */}
        <div className="lg:col-span-2 bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold text-white mb-4">
            Admin USDT Wallet (TRC20)
          </h2>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <div className="border border-gray-600 rounded-lg p-4 bg-[#131722] mb-4 relative">
                <p className="text-sm font-medium text-white break-all pr-8">
                  {adminWalletAddress}
                </p>
                <button
                  onClick={() => copyToClipboard(adminWalletAddress)}
                  className="absolute right-3 top-3 text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                </button>
                {copySuccess && (
                  <div className="absolute -top-12 right-0 bg-green-500 text-white text-xs px-3 py-1 rounded shadow-lg">
                    Copied to clipboard!
                  </div>
                )}
              </div>

              <div className="bg-amber-900/20 border-l-4 border-amber-400 p-4 mb-4 rounded">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="w-5 h-5 text-amber-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-amber-200">
                      <strong>Important:</strong> Only send USDT via the TRC20
                      network. Other networks are not supported and may result
                      in loss of funds.
                    </p>
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-300 mb-4 space-y-2">
                <p>
                  <strong className="text-white">Current Exchange Rate:</strong>{" "}
                  1 USDT = ${usdtRate.toFixed(2)} USD
                </p>
                <p>
                  After sending USDT, please submit your transaction ID in the
                  form below to complete your deposit request.
                </p>
              </div>
            </div>

            <div className="w-full md:w-48 flex flex-col items-center">
              <div className="bg-white p-3 border border-gray-300 rounded-lg mb-3 shadow-sm">
                <img
                  src={generateQRCode(adminWalletAddress)}
                  alt="USDT Wallet QR Code"
                  className="w-full h-auto rounded"
                />
              </div>
              <p className="text-xs text-center text-slate-400">
                Scan to deposit USDT
              </p>
            </div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold text-white mb-4">Summary</h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-300 mb-2">
                Total Deposited Amount
              </p>
              <div className="flex items-baseline">
                <span className="text-2xl font-bold text-white">
                  {totalDeposited}
                </span>
                <span className="ml-2 text-sm text-gray-400">USDT</span>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                (≈ ${(totalDeposited * usdtRate).toFixed(2)} USD)
              </p>
            </div>

            <div className="border-t border-gray-700 pt-4">
              <p className="text-sm text-gray-300 mb-2">Current Balance</p>
              <div className="flex items-baseline">
                <span className="text-2xl font-bold text-white">
                  {currentBalance.toFixed(2)}
                </span>
                <span className="ml-2 text-sm text-gray-400">USDT</span>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                (≈ ${(currentBalance * usdtRate).toFixed(2)} USD)
              </p>
              <div className="flex items-center mt-2 text-green-400 text-xs">
                <svg
                  className="w-3 h-3 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>+2.7% from yesterday</span>
              </div>
            </div>

            <div className="border-t border-gray-700 pt-4 text-xs text-gray-400">
              <p>Last updated: Apr 29, 2025, 14:30</p>
            </div>
          </div>
        </div>
      </div>

      {/* Deposit Request Form */}
      <div className="mt-8 bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
        <h2 className="text-xl font-semibold text-white mb-4">
          Deposit Request Form
        </h2>
        <form onSubmit={handleSubmitDeposit} className="max-w-2xl space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Select Network
            </label>
            <select
              className="w-full px-4 py-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-[#131722] text-white"
              defaultValue="trc20"
            >
              <option value="trc20">TRC20 (Recommended)</option>
              <option value="erc20" disabled>
                ERC20 (Not Supported)
              </option>
              <option value="bep20" disabled>
                BEP20 (Not Supported)
              </option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Amount (USDT)
            </label>
            <div className="space-y-3">
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full px-4 py-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-[#131722] text-white flex items-center justify-between"
                >
                  <div className="flex items-center">
                    {selectedAmount && !isCustomAmount ? (
                      <>
                        <span className="text-2xl mr-3">
                          {depositPackages.find(
                            (pkg) => pkg.value === selectedAmount
                          )?.icon || "💰"}
                        </span>
                        <div className="flex flex-col items-start">
                          <span className="text-gray-400 text-sm">
                            {depositPackages.find(
                              (pkg) => pkg.value === selectedAmount
                            )?.name || "Package"}
                          </span>
                          <span className="text-white font-semibold">
                            {selectedAmount} USDT
                          </span>
                        </div>
                      </>
                    ) : isCustomAmount ? (
                      <span className="text-white">Custom Amount</span>
                    ) : (
                      <span className="text-gray-400">
                        Select a deposit package
                      </span>
                    )}
                  </div>
                  <svg
                    className={`w-5 h-5 text-gray-400 transition-transform ${
                      isDropdownOpen ? "rotate-180" : ""
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>

                {isDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-[#131722] border border-gray-600 rounded-lg shadow-xl">
                    <div className="py-2 max-h-60 overflow-y-auto">
                      {depositPackages.map((pkg) => (
                        <button
                          key={pkg.value}
                          type="button"
                          onClick={() => handleAmountSelection(pkg.value)}
                          className="w-full px-4 py-3 flex items-center hover:bg-[#1A1F2E] transition-colors"
                        >
                          <span className="text-2xl mr-3">{pkg.icon}</span>
                          <div className="flex flex-col items-start">
                            <span className="text-white">{pkg.name}</span>
                            <span className="text-sm text-gray-400">
                              {pkg.value} USDT
                            </span>
                          </div>
                        </button>
                      ))}
                      <button
                        type="button"
                        onClick={() => handleAmountSelection("custom")}
                        className="w-full px-4 py-3 flex items-center hover:bg-[#1A1F2E] transition-colors"
                      >
                        <div className="w-8 h-8 rounded-full bg-[#1A1F2E] flex items-center justify-center mr-3">
                          <svg
                            className="w-4 h-4 text-gray-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        </div>
                        <span className="text-white">Custom Amount</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {isCustomAmount && (
                <div className="relative">
                  <input
                    type="number"
                    min="25"
                    step="1"
                    value={selectedAmount === "custom" ? "" : selectedAmount}
                    onChange={handleCustomAmountChange}
                    placeholder="Enter amount (min. 25 USDT)"
                    className="w-full px-4 py-3 pr-16 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-slate-900 text-white placeholder-slate-500"
                    required
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 text-sm">
                    USDT
                  </span>
                </div>
              )}

              {selectedAmount &&
                selectedAmount !== "custom" &&
                !isNaN(Number(selectedAmount)) && (
                  <div className="text-sm text-slate-400">
                    Approximate value: $
                    {(Number(selectedAmount) * usdtRate).toFixed(2)} USD
                  </div>
                )}

              {selectedAmount &&
                selectedAmount !== "custom" &&
                Number(selectedAmount) < 25 && (
                  <div className="text-sm text-red-400 flex items-center">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Minimum deposit amount is 25 USDT
                  </div>
                )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Enter Transaction ID
            </label>
            <input
              type="text"
              value={transactionId}
              onChange={(e) => setTransactionId(e.target.value)}
              placeholder="Enter the transaction ID from your wallet"
              className="w-full px-4 py-3 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-slate-900 text-white placeholder-slate-500"
              required
            />
            <p className="mt-1 text-xs text-slate-400">
              You can find the transaction ID in your wallet after sending USDT
              to the admin wallet address.
            </p>
          </div>

          <div className="bg-amber-900/20 border-l-4 border-amber-400 p-4 rounded">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="w-5 h-5 text-amber-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-amber-200">
                  Please ensure you enter the correct transaction ID. Incorrect
                  IDs may delay the processing of your deposit.
                </p>
              </div>
            </div>
          </div>

          <button
            type="submit"
            disabled={
              !selectedAmount ||
              (selectedAmount !== "custom" && Number(selectedAmount) < 25) ||
              !transactionId.trim()
            }
            className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] disabled:from-gray-600 disabled:to-gray-700 text-white px-8 py-3 rounded-lg font-medium hover:from-[#3B5FEF] hover:to-[#5C7FEF] disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-200 shadow-lg disabled:cursor-not-allowed"
          >
            Submit Deposit Request
          </button>
        </form>
      </div>

      {/* Deposit History Table */}
      <div className="mt-8 bg-[#1A1F2E] rounded-xl shadow-lg p-6 border border-gray-700">
        <h2 className="text-xl font-semibold text-white mb-4">
          Deposit History
        </h2>

        <div className="flex flex-col sm:flex-row items-start sm:items-center mb-4 gap-4">
          <div className="relative flex-1 sm:flex-initial">
            <input
              type="text"
              placeholder="Search deposits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-[#131722] text-white placeholder-gray-400 text-sm"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="w-4 h-4 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>

          {/* Status Filter Dropdown */}
          <div className="relative flex-1 sm:flex-initial">
            <select
              value={statusFilter}
              onChange={(e) => handleFilterChange(e)}
              className="w-full sm:w-48 appearance-none pl-4 pr-10 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-[#131722] text-white text-sm"
            >
              <option value="">All Status</option>
              <option value="credited_to_wallet">Credited to Wallet</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="reject">Rejected</option>
            </select>
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg
                className="w-4 h-4 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-[#131722]">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white transition-colors"
                  onClick={() => handleSort("date")}
                >
                  <div className="flex items-center">
                    Date & Time
                    {sortBy === "date" && (
                      <svg
                        className={`w-4 h-4 ml-1 ${
                          sortDirection === "asc" ? "rotate-180" : ""
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white transition-colors"
                  onClick={() => handleSort("amount")}
                >
                  <div className="flex items-center">
                    Amount (USDT)
                    {sortBy === "amount" && (
                      <svg
                        className={`w-4 h-4 ml-1 ${
                          sortDirection === "asc" ? "rotate-180" : ""
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Transaction ID
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-slate-800 divide-y divide-slate-700">
              {isTableLoading ? (
                <TableShimmer rows={itemsPerPage} columns={4} />
              ) : depositHistory.length > 0 ? (
                depositHistory.map((item) => (
                  <tr
                    key={item.id}
                    className="hover:bg-slate-750 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {item.created_at}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white font-medium">
                      {item.amount} USDT
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-400">
                      <div className="flex items-center">
                        <span className="truncate max-w-xs">
                          {item.transaction_id.substring(0, 10)}...
                        </span>
                        <button
                          onClick={() => copyToClipboard(item.transaction_id)}
                          className="ml-2 text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                          </svg>
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-3 py-1 inline-block text-xs leading-5 font-semibold rounded-full first-letter:uppercase ${
                          item.status === "credited_to_wallet"
                            ? "bg-emerald-900/50 text-emerald-300 border border-emerald-700"
                            : item.status === "approved"
                            ? "bg-green-900/50 text-green-300 border border-green-800"
                            : item.status === "pending"
                            ? "bg-yellow-900/50 text-yellow-300 border border-yellow-800"
                            : item.status === "reject"
                            ? "bg-red-900/50 text-red-300 border border-red-800"
                            : "bg-gray-900/50 text-gray-300 border border-gray-800"
                        }`}
                      >
                        {item.status === "credited_to_wallet"
                          ? "Credited to Wallet"
                          : item.status === "reject"
                          ? "Rejected"
                          : item.status}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={4}
                    className="px-6 py-8 text-center text-sm text-slate-400"
                  >
                    <div className="flex flex-col items-center">
                      <svg
                        className="w-12 h-12 text-slate-600 mb-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <p>No deposit history found</p>
                      {statusFilter && (
                        <p className="text-xs mt-1">
                          Try changing the status filter
                        </p>
                      )}
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {depositHistory && depositHistory.length > 0 && (
          <Pagination
            handleItemsPerPageChange={handleItemsPerPage}
            handlePage={handlePage}
            page={currentPage}
            itemsPerPage={itemsPerPage}
            totalPages={totalPage}
          />
        )}
      </div>
    </div>
  );
}

export default UserDeposit;
