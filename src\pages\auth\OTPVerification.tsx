import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Mail, 
  ArrowLeft, 
  Loader, 
  CheckCircle, 
  RefreshCw,
  AlertCircle 
} from 'lucide-react';
import { sendOT<PERSON>pi, verifyOTPApi } from './login-signup-serivce';

interface LocationState {
  email?: string;
  fromSignup?: boolean;
}

const OTPVerification: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const state = location.state as LocationState;
  
  // Get email from location state or redirect if not available
  const email = state?.email;
  const fromSignup = state?.fromSignup || false;

  // Redirect if no email provided
  useEffect(() => {
    if (!email) {
      navigate('/signup');
    }
  }, [email, navigate]);

  // State management
  const [otpValue, setOtpValue] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [timer]);

  // Auto-send OTP when component mounts
  useEffect(() => {
    if (email && fromSignup) {
      handleSendOTP();
    }
  }, [email, fromSignup]);

  // Handle OTP input change
  const handleOTPChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtpValue(value);
    setError('');
  };

  // Handle send OTP
  const handleSendOTP = async () => {
    if (!email) return;

    setIsResending(true);
    setError('');

    try {
      const response = await sendOTPApi(email);
      if (response.success) {
        setTimer(60);
        setCanResend(false);
        setError('');
      } else {
        setError(response.message || 'Failed to send OTP');
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to send OTP. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  // Handle verify OTP
  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || otpValue.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const response = await verifyOTPApi(email, otpValue);
      if (response.success && response.data?.verified) {
        setSuccess(true);
        setError('');
        
        // Redirect after successful verification
        setTimeout(() => {
          navigate('/login', { 
            state: { 
              message: 'Email verified successfully! You can now log in.',
              email: email 
            }
          });
        }, 2000);
      } else {
        setError(response.message || 'Invalid OTP. Please try again.');
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to verify OTP. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (fromSignup) {
      navigate('/signup');
    } else {
      navigate(-1);
    }
  };

  if (!email) {
    return null; // Will redirect in useEffect
  }

  if (success) {
    return (
      <div className="min-h-screen bg-[#0D1117] flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-[#1A1F2E] rounded-lg shadow-lg p-8 text-center">
          <div className="mb-6">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">Email Verified!</h1>
            <p className="text-gray-400">
              Your email has been successfully verified. Redirecting to login...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0D1117] flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-[#1A1F2E] rounded-lg shadow-lg p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mb-4">
            <Mail className="w-16 h-16 text-blue-500 mx-auto" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Verify Your Email</h1>
          <p className="text-gray-400 text-sm">
            We've sent a 6-digit verification code to
          </p>
          <p className="text-white font-medium mt-1">{email}</p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-md text-sm flex items-center">
            <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
            {error}
          </div>
        )}

        {/* OTP Form */}
        <form onSubmit={handleVerifyOTP} className="space-y-6">
          <div>
            <label htmlFor="otp" className="block text-sm font-medium text-gray-300 mb-2">
              Enter Verification Code
            </label>
            <input
              type="text"
              id="otp"
              value={otpValue}
              onChange={handleOTPChange}
              placeholder="000000"
              className="w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-[#0D1117] text-white"
              maxLength={6}
              required
            />
            <p className="text-xs text-gray-400 mt-2 text-center">
              Enter the 6-digit code sent to your email
            </p>
          </div>

          <button
            type="submit"
            disabled={otpValue.length !== 6 || isVerifying}
            className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
              otpValue.length !== 6 || isVerifying
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isVerifying ? (
              <span className="flex items-center justify-center">
                <Loader className="w-5 h-5 mr-2 animate-spin" />
                Verifying...
              </span>
            ) : (
              'Verify Email'
            )}
          </button>
        </form>

        {/* Resend OTP */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-400 mb-3">
            Didn't receive the code?
          </p>
          
          {canResend ? (
            <button
              onClick={handleSendOTP}
              disabled={isResending}
              className="text-blue-400 hover:text-blue-300 font-medium text-sm flex items-center justify-center mx-auto transition-colors"
            >
              {isResending ? (
                <>
                  <Loader className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Resend Code
                </>
              )}
            </button>
          ) : (
            <p className="text-sm text-gray-500">
              Resend code in {timer}s
            </p>
          )}
        </div>

        {/* Back Button */}
        <div className="mt-8 text-center">
          <button
            onClick={handleBack}
            className="text-gray-400 hover:text-white font-medium text-sm flex items-center justify-center mx-auto transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to {fromSignup ? 'Signup' : 'Previous Page'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OTPVerification;
