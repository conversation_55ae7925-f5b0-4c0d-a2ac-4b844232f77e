import React, { JSX, useEffect, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationProps {
  handlePage: (page: number) => void;
  page: number;
  itemsPerPage: number;
  handleItemsPerPageChange: (itemsPerPage: number) => void;
  totalPages: number;
}

// Toast types
type ToastType = 'success' | 'error' | 'info';

interface Toast {
  id: number;
  message: string;
  type: ToastType;
}

// Toast Hook
const useToast = () => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const showToast = (message: string, type: ToastType = 'info') => {
    const id = Date.now();
    setToasts(prev => [...prev, { id, message, type }]);
    
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, 3000);
  };

  const removeToast = (id: number) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return { toasts, showToast, removeToast };
};

// Toast Component
const ToastComponent = ({ toast, onRemove }: { toast: Toast, onRemove: (id: number) => void }) => {
  const bgColor: Record<ToastType, string> = {
    success: 'bg-green-600',
    error: 'bg-red-600',
    info: 'bg-blue-600'
  };

  const backgroundColor = bgColor[toast.type];

  return (
    <div className={`${backgroundColor} text-white px-4 py-3 rounded-lg shadow-lg z-50 min-w-64 animate-slide-in mb-2`}>
      <div className="flex justify-between items-center">
        <div>
          <div className="font-medium">
            {toast.type === 'info' && 'Invalid Page Size!'}
            {toast.type === 'success' && 'Success'}
            {toast.type === 'error' && 'Error'}
          </div>
          <div className="text-sm opacity-90">{toast.message}</div>
        </div>
        <button 
          onClick={() => onRemove(toast.id)}
          className="ml-4 text-white hover:text-gray-200 text-lg font-bold"
        >
          ×
        </button>
      </div>
    </div>
  );
};

function Pagination({
  handlePage,
  page,
  itemsPerPage,
  handleItemsPerPageChange,
  totalPages,
}: PaginationProps) {
  const [inputValue, setInputValue] = useState<string>(String(itemsPerPage));
  const { toasts, showToast, removeToast } = useToast();

  const handleChangePage = (pageNo: number) => {
    if (pageNo !== page) {
      handlePage(pageNo);
    }
  };

  const handlePrevious = () => {
    if (page > 1) {
      handleChangePage(page - 1);
    }
  };

  const handleNext = () => {
    if (page < totalPages) {
      handleChangePage(page + 1);
    }
  };

  const handleResultsPerPage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (/^\d*$/.test(value)) {
      setInputValue(value);
    }
  };

  useEffect(() => {
    setInputValue(String(itemsPerPage));
  }, [itemsPerPage]);

  const renderPageNumbers = () => {
    const pages: JSX.Element[] = [];
    const startPage = Math.max(1, page - 1);
    const endPage = Math.min(totalPages, page + 1);

    // Always show the first page
    if (startPage > 1) {
      pages.push(
        <button
          key="1"
          className={`px-3 py-2 rounded text-sm font-medium transition-colors hidden md:flex items-center justify-center min-w-[36px] ${
            page === 1
              ? "bg-blue-600 text-white"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
          }`}
          onClick={() => handleChangePage(1)}
        >
          1
        </button>
      );
    }

    // Show ellipsis if there's a gap
    if (startPage > 2) {
      pages.push(
        <span
          key="ellipsis-start"
          className="px-2 py-2 text-gray-400 text-sm hidden md:block"
        >
          ...
        </span>
      );
    }

    // Add pages from startPage to endPage
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          className={`px-3 py-2 rounded text-sm font-medium transition-colors hidden md:flex items-center justify-center min-w-[36px] ${
            i === page
              ? "bg-blue-600 text-white"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
          }`}
          onClick={() => handleChangePage(i)}
        >
          {i}
        </button>
      );
    }

    // Show ellipsis if there's a gap
    if (endPage < totalPages - 1) {
      pages.push(
        <span
          key="ellipsis-end"
          className="px-2 py-2 text-gray-400 text-sm hidden md:block"
        >
          ...
        </span>
      );
    }

    // Always show the last page
    if (endPage < totalPages) {
      pages.push(
        <button
          key={totalPages}
          className={`px-3 py-2 rounded text-sm font-medium transition-colors hidden md:flex items-center justify-center min-w-[36px] ${
            page === totalPages
              ? "bg-blue-600 text-white"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
          }`}
          onClick={() => handleChangePage(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    return pages;
  };

  const handleApply = () => {
    const parsedValue = parseInt(inputValue, 10);

    if (isNaN(parsedValue) || parsedValue <= 0 || parsedValue > 100) {
      showToast("Please enter a number between 1 and 100", "info");
      setInputValue(String(itemsPerPage));
    } else {
      handleItemsPerPageChange(parsedValue);
      handleChangePage(1);
    }
  };

  return (
    <>
      {/* Toast Container - Fixed positioning */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map((toast) => (
          <ToastComponent key={toast.id} toast={toast} onRemove={removeToast} />
        ))}
      </div>

      {/* Pagination Component */}
      <div className="bg-gray-800 border-t border-gray-700 px-6 py-3 flex flex-col sm:flex-row justify-between items-center gap-4">
        {/* Results per page */}
        <div className="flex items-center gap-3">
          <label htmlFor="resultPerPage" className="text-gray-400 text-xs font-medium">
            Result per page:
          </label>
          <div className="flex items-center gap-2">
            <input
              id="resultPerPage"
              type="text"
              value={inputValue}
              onChange={handleResultsPerPage}
              className="w-12 h-8 bg-gray-700 border border-gray-600 rounded text-center text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={handleApply}
              className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors"
            >
              Apply
            </button>
          </div>
        </div>

        {/* Pagination controls */}
        <div className="flex items-center gap-2">
          {/* Previous button */}
          <button
            onClick={handlePrevious}
            disabled={page === 1}
            className={`flex items-center gap-1 px-3 py-2 rounded text-sm font-medium transition-colors ${
              page === 1
                ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                : "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
            }`}
          >
            <ChevronLeft size={16} />
            <span className="hidden sm:inline">Previous</span>
          </button>

          {/* Page info for mobile */}
          <div className="flex md:hidden items-center px-3 py-2 bg-gray-700 text-gray-300 text-sm rounded">
            {page} of {totalPages}
          </div>

          {/* Page numbers for desktop */}
          {renderPageNumbers()}

          {/* Next button */}
          <button
            onClick={handleNext}
            disabled={page === totalPages}
            className={`flex items-center gap-1 px-3 py-2 rounded text-sm font-medium transition-colors ${
              page === totalPages
                ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                : "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
            }`}
          >
            <span className="hidden sm:inline">Next</span>
            <ChevronRight size={16} />
          </button>
        </div>
      </div>

      <style>{`
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        .animate-slide-in {
          animation: slide-in 0.3s ease-out;
        }
      `}</style>
    </>
  );
}

export default Pagination;