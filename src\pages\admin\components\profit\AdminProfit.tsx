import { Banknote, Edit, Eye, HandCoins, LineChart, Users, Search } from 'lucide-react'
import React, { useState, useEffect } from 'react'
import { profit, profitHeader } from './profit.model'
import { getProfitList, getProfitHeader } from './profit-service'
import Pagination from '../../../../components/Pagination/Pagination'
import TableShimmer from '../../../../components/shimmers/TableShimmer'
import TableNoDataRow from '../../../../components/utils/TableNoDataRow'

const AdminProfit: React.FC = () => {
  // State management
  const [profits, setProfits] = useState<profit[]>([])
  const [headerData, setHeaderData] = useState<profitHeader | null>(null)
  const [profitRate, setProfitRate] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [itemsPerPage, setItemsPerPage] = useState<number>(10)
  const [totalPages, setTotalPages] = useState<number>(0)

  // Loading states
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false) // Shimmer loading for table
  const [isOverlayLoading, setIsOverlayLoading] = useState<boolean>(false) // Overlay loading for actions
  const [isHeaderLoading, setIsHeaderLoading] = useState<boolean>(true) // Loading for header tiles

  // API Functions
  const fetchProfits = async (
    page: number,
    itemsPerPageCount: number,
    searchValue: string,
    status: string = ""
  ) => {
    try {
      setIsTableLoading(true)
      const response = await getProfitList(
        (page - 1) * itemsPerPageCount,
        itemsPerPageCount,
        searchValue,
        status
      )
      setProfits(response.items)
      setCurrentPage(response.current_page)
      setTotalPages(response.total_pages)
    } catch (error) {
      console.error("Error fetching profits:", error)
    } finally {
      setIsTableLoading(false)
    }
  }

  const fetchHeaderData = async () => {
    try {
      setIsHeaderLoading(true)
      const response = await getProfitHeader()
      setHeaderData(response)
    } catch (error) {
      console.error("Error fetching header data:", error)
    } finally {
      setIsHeaderLoading(false)
    }
  }

  // Event Handlers
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    fetchProfits(1, itemsPerPage, e.target.value, "")
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchProfits(page, itemsPerPage, searchTerm, "")
  }

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value)
    fetchProfits(1, value, searchTerm, "")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!profitRate) {
      alert('Please enter a profit rate')
      return
    }
    setIsOverlayLoading(true)
    try {
      // Handle profit rate submission
      console.log('Setting profit rate:', profitRate)
      alert(`Profit rate set to ${profitRate}%`)
      setProfitRate('')
      // Refresh data after setting rate
      fetchProfits(currentPage, itemsPerPage, searchTerm, "")
      fetchHeaderData()
    } catch (error) {
      console.error("Error setting profit rate:", error)
    } finally {
      setIsOverlayLoading(false)
    }
  }

  // Initialize data on component mount
  useEffect(() => {
    fetchProfits(1, itemsPerPage, "", "")
    fetchHeaderData()
  }, [itemsPerPage])

  const getStatusBadgeClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-emerald-900/50 text-emerald-300 border border-emerald-800'
      case 'yellow':
        return 'bg-yellow-900/50 text-yellow-300 border border-yellow-800'
      case 'red':
        return 'bg-red-900/50 text-red-300 border border-red-800'
      default:
        return 'bg-gray-700/50 text-gray-300 border border-gray-600'
    }
  }

  return (
    <div className="relative min-h-screen bg-gray-900 text-white p-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Today's Profit Rate */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Today's Profit Rate
              </p>
              {isHeaderLoading ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-600 rounded mb-1 w-16"></div>
                </div>
              ) : (
                <h3 className="text-2xl font-bold text-white">{headerData?.today_profit_rate || 0}%</h3>
              )}
            </div>
            <div className="bg-emerald-900/30 p-3 rounded-xl border border-emerald-800/50">
              <LineChart className="text-emerald-400 w-5 h-5" />
            </div>
          </div>
        </div>

        {/* Total Users Receiving Profit */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Total Users Receiving Profit
              </p>
              {isHeaderLoading ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-600 rounded mb-1 w-20"></div>
                </div>
              ) : (
                <h3 className="text-2xl font-bold text-white">{headerData?.total_users_receiving_profit || 0}</h3>
              )}
            </div>
            <div className="bg-blue-900/30 p-3 rounded-xl border border-blue-800/50">
              <Users className="text-blue-400 w-6 h-6" />
            </div>
          </div>
        </div>

        {/* Total Net Deposits */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Total Net Deposits
              </p>
              {isHeaderLoading ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-600 rounded mb-1 w-24"></div>
                </div>
              ) : (
                <h3 className="text-2xl font-bold text-white">{headerData?.total_net_deposit || "0"}</h3>
              )}
            </div>
            <div className="bg-purple-900/30 p-3 rounded-xl border border-purple-800/50">
              <Banknote className="text-purple-400 w-6 h-6" />
            </div>
          </div>
        </div>

        {/* Today's Distribution Amount */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm mb-1">
                Today's Distribution Amount
              </p>
              {isHeaderLoading ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-600 rounded mb-1 w-24"></div>
                </div>
              ) : (
                <h3 className="text-2xl font-bold text-white">{headerData?.today_distribution_amount || "0"}</h3>
              )}
            </div>
            <div className="bg-indigo-900/30 p-3 rounded-xl border border-indigo-800/50">
              <HandCoins className="text-indigo-400 w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Set Daily Profit Rate */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl mb-8">
        <div className="p-6 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-white mb-4">
            Set Daily Profit Rate
          </h3>
          <form onSubmit={handleSubmit} className="flex items-end gap-4">
            <div className="flex-1 max-w-xs">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Profit Percentage
              </label>
              <div className="relative">
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={profitRate}
                  onChange={(e) => setProfitRate(e.target.value)}
                  placeholder="Enter percentage"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  %
                </span>
              </div>
            </div>
            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-colors whitespace-nowrap"
            >
              Publish Daily Profit
            </button>
          </form>
        </div>
      </div>

      {/* Profit Distribution History */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl">
        <div className="px-6 py-4 border-b border-gray-700/50 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h3 className="text-lg font-semibold text-white">
            Profit Distribution History
          </h3>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search entries..."
                value={searchTerm}
                onChange={handleSearch}
                className="pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-full sm:w-64"
              />
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-700/30">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Profit Rate
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Total Amount
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Recipients
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50">
              {isTableLoading ? (
                <TableShimmer rows={itemsPerPage} columns={6} />
              ) : profits.length > 0 ? (
                profits.map((profit) => (
                  <tr key={profit.id} className="hover:bg-gray-700/30 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {new Date(profit.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">{profit.today_rate}%</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">{profit.distributed_amount}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">{profit.total_user_getting_profit}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClasses('green')}`}>
                        Completed
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-3">
                        <button
                          className="text-blue-400 hover:text-blue-300 transition-colors p-1 disabled:opacity-50"
                          disabled={isOverlayLoading}
                        >
                          <Eye className="w-5 h-5" />
                        </button>
                        <button
                          className="text-gray-400 hover:text-gray-300 transition-colors p-1 disabled:opacity-50"
                          disabled={isOverlayLoading}
                        >
                          <Edit className="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <TableNoDataRow
                  colSpan={6}
                  type="profits"
                  showSearchHint={!!searchTerm}
                />
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {profits.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-700/50">
            <Pagination
              handlePage={handlePageChange}
              page={currentPage}
              itemsPerPage={itemsPerPage}
              handleItemsPerPageChange={handleItemsPerPageChange}
              totalPages={totalPages}
            />
          </div>
        )}
      </div>

      {/* Overlay Loading */}
      {isOverlayLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-lg">
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-white">Processing...</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminProfit