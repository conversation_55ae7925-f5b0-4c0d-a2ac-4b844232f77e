import api from "../../../../api/axiosInstance";
import { productList } from "./product.model";

export const getProductList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<productList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(
      `products/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};