@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.date-input::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

/* Custom Scrollbar Styles for Dark Theme */
.custom-scrollbar {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #4B5563 #1F2937;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1F2937;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 4px;
  border: 1px solid #374151;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: #9CA3AF;
}

/* Global scrollbar styles for the entire app */
* {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #4B5563 #1F2937;
}

/* Webkit browsers (Chrome, Safari, Edge) - Global */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: #1F2937;
}

*::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

*::-webkit-scrollbar-corner {
  background: #1F2937;
}