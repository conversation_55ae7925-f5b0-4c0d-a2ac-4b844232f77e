// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";
// import * as echarts from "echarts";
import {
  LayoutDashboard,
  Wallet,
  ArrowRight,
  Users,
  Package,
  LineChart,
  Menu,
  Mail,
  MessageCircle
} from "lucide-react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
const UserDashboard: React.FC = () => {
  // const [activeTab, setActiveTab] = useState("dashboard");

  const navigate = useNavigate();
  const location = useLocation();


  const navigationData = (nav: string) => {
    navigate(`/user/${nav}`);
  }
  return (
    <div className="min-h-screen bg-[#0F1115] flex flex-col">
      
      {/* Main Content */}
      <div className="flex flex-1 pt-16">
        {/* Sidebar */}
        <aside className="w-64 bg-[#1A1D24] border-r border-[#2A2F3A] fixed left-0 top-16 bottom-0 hidden md:block overflow-y-auto">
          <nav className="mt-6 px-4">
          <div className="space-y-1">
  <button
    onClick={() => navigationData("dashboard")}
    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
      location.pathname.includes("dashboard")
        ? "bg-indigo-50 text-indigo-700"
        : "text-gray-700 hover:bg-gray-50"
    } cursor-pointer`}
  >
    <LayoutDashboard
      className={`w-5 h-5 mr-3 ${
        location.pathname.includes("dashboard") ? "text-indigo-700" : "text-gray-400"
      }`}
    />
    <span>Dashboard</span>
  </button>

  <button
    onClick={() => navigationData("deposit")}
    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
      location.pathname.includes("deposit")
        ? "bg-indigo-50 text-indigo-700"
        : "text-gray-700 hover:bg-gray-50"
    } cursor-pointer`}
  >
    <Wallet
      className={`w-5 h-5 mr-3 ${
        location.pathname.includes("deposit") ? "text-indigo-700" : "text-gray-400"
      }`}
    />
    <span>Deposit</span>
  </button>

  <button
    onClick={() => navigationData("withdraw")}
    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
      location.pathname.includes("withdraw")
        ? "bg-indigo-50 text-indigo-700"
        : "text-gray-700 hover:bg-gray-50"
    } cursor-pointer`}
  >
    <ArrowRight
      className={`w-5 h-5 mr-3 ${
        location.pathname.includes("withdraw") ? "text-indigo-700" : "text-gray-400"
      }`}
    />
    <span>Withdraw</span>
  </button>

  <button
    onClick={() => navigationData("referral")}
    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
      location.pathname.includes("referrals")
        ? "bg-indigo-50 text-indigo-700"
        : "text-gray-700 hover:bg-gray-50"
    } cursor-pointer`}
  >
    <Users
      className={`w-5 h-5 mr-3 ${
        location.pathname.includes("referrals") ? "text-indigo-700" : "text-gray-400"
      }`}
    />
    <span>Referrals</span>
  </button>

  <button
    onClick={() => navigationData("earnings")}
    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
      location.pathname.includes("earnings")
        ? "bg-indigo-50 text-indigo-700"
        : "text-gray-700 hover:bg-gray-50"
    } cursor-pointer`}
  >
    <LineChart
      className={`w-5 h-5 mr-3 ${
        location.pathname.includes("earnings") ? "text-indigo-700" : "text-gray-400"
      }`}
    />
    <span>Earnings</span>
  </button>

  <button
    onClick={() => navigationData("products")}
    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
      location.pathname.includes("products")
        ? "bg-indigo-50 text-indigo-700"
        : "text-gray-700 hover:bg-gray-50"
    } cursor-pointer`}
  >
    <Package
      className={`w-5 h-5 mr-3 ${
        location.pathname.includes("products") ? "text-indigo-700" : "text-gray-400"
      }`}
    />
    <span>Products</span>
  </button>
</div>

            <div className="mt-10">
              <div className="bg-[#2A2F3A] rounded-lg p-4">
                <h3 className="text-sm font-medium text-white mb-2">
                  Need Help?
                </h3>
                <p className="text-xs text-[#A1A1AA] mb-3">
                  Contact our support team for assistance with your account.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center text-[#A1A1AA]">
                    <Mail className="text-[#4C7BF4] w-4 mr-2" />
                    <a
                      href="mailto:<EMAIL>"
                      className="text-sm hover:text-[#4C7BF4] transition-colors"
                    >
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center text-[#A1A1AA]">
                    <MessageCircle className="text-[#4C7BF4] w-4 mr-2" />
                    <a
                      href="https://wa.me/***********"
                      className="text-sm hover:text-[#4C7BF4] transition-colors"
                    >
                      +****************
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </nav>
        </aside>
        {/* Mobile Sidebar Toggle */}
        <div className="fixed bottom-4 right-4 md:hidden z-20">
          <button className="bg-indigo-600 text-white h-12 w-12 rounded-full shadow-lg flex items-center justify-center cursor-pointer">
            <Menu className="w-6 h-6" />
          </button>
        </div>
        {/* Main Content Area */}
        <main className="flex-1 ml-0 md:ml-64 min-h-screen">
          <Outlet />
        </main>
      </div>
    </div>
  );
};
export default UserDashboard;
