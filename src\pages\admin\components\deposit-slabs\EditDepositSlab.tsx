import React, { useState, FormEvent, useRef, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { ArrowLeft, Save, X, Upload, FileImage } from 'lucide-react'
import { DepositSlab, UpdateDepositSlabRequest } from './deposit-slabs.model'
import { updateDepositSlab, getDepositSlabs } from './deposit-slabs-service'

function EditDepositSlab() {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()

  // Form states
  const [formData, setFormData] = useState<UpdateDepositSlabRequest>({
    name: '',
    amount: 0,
    is_active: true
  })
  const [currentSlab, setCurrentSlab] = useState<DepositSlab | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [newImage, setNewImage] = useState<File | null>(null)

  // File input ref
  const imageInputRef = useRef<HTMLInputElement>(null)

  // Load existing deposit slab data
  useEffect(() => {
    const loadDepositSlab = async () => {
      if (!id) {
        navigate('/admin/deposit-slabs')
        return
      }

      try {
        setIsLoading(true)
        // Get all slabs and find the one we need (since there's no single slab endpoint)
        const response = await getDepositSlabs(0, 1000)
        const slab = response.items.find(item => item.id === parseInt(id))

        if (!slab) {
          alert('Deposit slab not found')
          navigate('/admin/deposit-slabs')
          return
        }

        setCurrentSlab(slab)
        setFormData({
          name: slab.name,
          amount: slab.amount,
          is_active: slab.is_active
        })

        // Set existing image preview if available
        if (slab.image) {
          setImagePreview(slab.image)
        }
      } catch (error) {
        console.error('Error loading deposit slab:', error)
        alert('Failed to load deposit slab')
        navigate('/admin/deposit-slabs')
      } finally {
        setIsLoading(false)
      }
    }

    loadDepositSlab()
  }, [id, navigate])

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, image: 'Please select a valid image file' }))
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, image: 'Image size must be less than 5MB' }))
      return
    }

    // Update new image
    setNewImage(file)

    // Create preview
    const reader = new FileReader()
    reader.onload = () => {
      setImagePreview(reader.result as string)
    }
    reader.readAsDataURL(file)

    // Clear any existing error
    if (errors.image) {
      setErrors(prev => ({ ...prev, image: '' }))
    }
  }

  // Remove image
  const removeImage = () => {
    setNewImage(null)
    setImagePreview(currentSlab?.image || null)
    if (imageInputRef.current) {
      imageInputRef.current.value = ''
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.name?.trim()) {
      newErrors.name = 'Deposit slab name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters long'
    }

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0'
    } else if (formData.amount > 1000000) {
      newErrors.amount = 'Amount cannot exceed 1,000,000'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    if (!validateForm() || !id) {
      return
    }

    setIsSubmitting(true)
    try {
      // Create FormData if there's a new image, otherwise use JSON
      let requestData: any

      if (newImage) {
        // Use FormData for image upload
        const formDataToSend = new FormData()
        formDataToSend.append('name', formData.name!.trim())
        formDataToSend.append('amount', formData.amount!.toString())
        formDataToSend.append('is_active', formData.is_active!.toString())
        formDataToSend.append('image', newImage)
        requestData = formDataToSend
      } else {
        // Use regular JSON for text-only updates
        requestData = {
          name: formData.name!.trim(),
          amount: Number(formData.amount),
          is_active: Boolean(formData.is_active)
        }
      }

      console.log('Updating deposit slab:', parseInt(id), requestData)

      const result = await updateDepositSlab(parseInt(id), requestData)
      console.log('Deposit slab updated successfully:', result)

      // Show success message and redirect
      alert('Deposit slab updated successfully!')
      navigate('/admin/deposit-slabs')
    } catch (error: any) {
      console.error('Error updating deposit slab:', error)

      // Handle specific API errors
      if (error?.response?.status === 422) {
        const apiErrors = error.response.data?.detail || []
        let errorMessage = 'Validation error: '
        apiErrors.forEach((err: any, index: number) => {
          if (err.msg) {
            errorMessage += `${index > 0 ? ', ' : ''}${err.msg}`
          }
        })
        alert(errorMessage)
      } else if (error?.response?.status === 400) {
        alert('Bad request. Please check your input data.')
      } else if (error?.response?.status === 404) {
        alert('Deposit slab not found.')
      } else if (error?.response?.status === 500) {
        alert('Server error. Please try again later.')
      } else if (error?.message) {
        alert(`Error: ${error.message}`)
      } else {
        alert('Failed to update deposit slab. Please try again.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    if (formData.name !== currentSlab?.name ||
        formData.amount !== currentSlab?.amount ||
        formData.is_active !== currentSlab?.is_active ||
        newImage) {
      if (window.confirm('Are you sure you want to cancel? All changes will be lost.')) {
        navigate('/admin/deposit-slabs')
      }
    } else {
      navigate('/admin/deposit-slabs')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 p-6 flex items-center justify-center">
        <div className="text-white text-lg">Loading deposit slab...</div>
      </div>
    )
  }

  if (!currentSlab) {
    return (
      <div className="min-h-screen bg-gray-900 p-6 flex items-center justify-center">
        <div className="text-white text-lg">Deposit slab not found</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-4 mb-4">
          <button
            onClick={() => navigate('/admin/deposit-slabs')}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Deposit Slabs</span>
          </button>
        </div>
        <h1 className="text-3xl font-bold text-white">Edit Deposit Slab</h1>
        <p className="text-gray-400 mt-2">Update deposit slab information and settings</p>
      </div>

      {/* Form Card */}
      <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700 max-w-2xl">
        <div className="p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Deposit Slab Details</h2>
          <p className="text-gray-400 text-sm mt-1">Update the information below to modify the deposit slab</p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Name Field */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              Deposit Slab Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                errors.name ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter deposit slab name (e.g., Silver, Gold, Platinum)"
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-400">{errors.name}</p>
            )}
          </div>

          {/* Amount Field */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-300 mb-2">
              Amount (USDT) *
            </label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount || ''}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                errors.amount ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter amount (e.g., 1000)"
              min="0"
              step="0.01"
              disabled={isSubmitting}
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-400">{errors.amount}</p>
            )}
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Deposit Slab Image
            </label>
            <div className="space-y-4">
              {/* Upload Area */}
              {!imagePreview ? (
                <div
                  onClick={() => imageInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-gray-500 hover:bg-gray-700/20 transition-colors"
                >
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-400 mb-1">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, JPEG up to 5MB
                  </p>
                </div>
              ) : (
                /* Image Preview */
                <div className="relative border border-gray-600 rounded-lg overflow-hidden">
                  <img
                    src={imagePreview}
                    alt="Deposit slab preview"
                    className="w-full h-48 object-cover"
                  />
                  <button
                    type="button"
                    onClick={removeImage}
                    className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                    disabled={isSubmitting}
                  >
                    <X className="w-4 h-4" />
                  </button>
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2">
                    <div className="flex items-center space-x-2">
                      <FileImage className="w-4 h-4" />
                      <span className="text-sm truncate">
                        {newImage ? newImage.name : 'Current image'}
                      </span>
                      {newImage && (
                        <span className="text-xs bg-blue-600 px-2 py-1 rounded">New</span>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Hidden File Input */}
              <input
                ref={imageInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={isSubmitting}
              />

              {errors.image && (
                <p className="text-sm text-red-400">{errors.image}</p>
              )}
            </div>
          </div>

          {/* Active Status */}
          <div>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active || false}
                onChange={handleInputChange}
                className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                disabled={isSubmitting}
              />
              <div>
                <span className="text-sm font-medium text-gray-300">Active Status</span>
                <p className="text-xs text-gray-400">Enable this deposit slab for users</p>
              </div>
            </label>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-700">
            <button
              type="button"
              onClick={handleCancel}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-4 h-4" />
              <span>Cancel</span>
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              <span>{isSubmitting ? 'Updating...' : 'Update Deposit Slab'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditDepositSlab
