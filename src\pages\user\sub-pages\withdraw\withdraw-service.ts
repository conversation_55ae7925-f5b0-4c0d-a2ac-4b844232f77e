import api from "../../../../api/axiosInstance";
import { CreateWithdrawalRequest, WithdrawalList } from "./withdraw.model";

export const getUserWithdrawalList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<WithdrawalList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(
      `withdrawals/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const createWithdrawal = async (body: CreateWithdrawalRequest) => {
  try {
    const response = await api.post("/withdrawals/", body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

