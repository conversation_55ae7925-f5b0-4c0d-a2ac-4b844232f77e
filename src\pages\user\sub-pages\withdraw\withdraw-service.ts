import api from "../../../../api/axiosInstance";
import {
  CreateWithdrawalRequest,
  CreateWithdrawalResponse,
  WithdrawalList,
  UserBalance,
  WithdrawalSummary,
  NetworkOption
} from "./withdraw.model";

// Get user withdrawal history
export const getUserWithdrawalList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<WithdrawalList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(
      `/withdrawals/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching withdrawal list:', error);
    throw error;
  }
};

// Create a new withdrawal request
export const createWithdrawal = async (
  withdrawalData: CreateWithdrawalRequest
): Promise<CreateWithdrawalResponse> => {
  try {
    const response = await api.post("/withdrawals/", withdrawalData);
    return {
      success: true,
      message: "Withdrawal request submitted successfully",
      data: response.data
    };
  } catch (error: any) {
    console.error('Error creating withdrawal:', error);

    // Handle different error types
    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;

      if (status === 400) {
        return {
          success: false,
          message: errorData.message || "Invalid withdrawal request",
          error: errorData.error || "Bad request"
        };
      } else if (status === 422) {
        return {
          success: false,
          message: "Validation failed",
          error: errorData.message || "Validation error"
        };
      } else if (status === 403) {
        return {
          success: false,
          message: "Insufficient balance or daily limit exceeded",
          error: "Forbidden"
        };
      }
    }

    return {
      success: false,
      message: "Failed to submit withdrawal request. Please try again.",
      error: error.message || "Unknown error"
    };
  }
};

// Get user balance information
export const getUserBalance = async (): Promise<UserBalance> => {
  try {
    const response = await api.get("/user/balance");
    return response.data;
  } catch (error) {
    console.error('Error fetching user balance:', error);
    throw error;
  }
};

// Get withdrawal summary/statistics
export const getWithdrawalSummary = async (): Promise<WithdrawalSummary> => {
  try {
    const response = await api.get("/withdrawals/summary");
    return response.data;
  } catch (error) {
    console.error('Error fetching withdrawal summary:', error);
    throw error;
  }
};

// Get available networks and their configurations
export const getNetworkOptions = async (): Promise<NetworkOption[]> => {
  try {
    const response = await api.get("/withdrawals/networks");
    return response.data;
  } catch (error) {
    console.error('Error fetching network options:', error);
    // Return default networks if API fails
    return [
      { value: "trc20", label: "TRC20 (TRON)", fee: 1 },
      { value: "erc20", label: "ERC20 (Ethereum)", fee: 15 },
      { value: "bep20", label: "BEP20 (Binance Smart Chain)", fee: 5 },
    ];
  }
};

// Get withdrawal limits and configurations
export const getWithdrawalLimits = async () => {
  try {
    const response = await api.get("/withdrawals/limits");
    return response.data;
  } catch (error) {
    console.error('Error fetching withdrawal limits:', error);
    // Return default limits if API fails
    return {
      min_withdrawal: 25,
      max_withdrawal: 10000,
      daily_limit: 2000,
      processing_time: "1-24 hours"
    };
  }
};

// Cancel a pending withdrawal
export const cancelWithdrawal = async (withdrawalId: number) => {
  try {
    const response = await api.patch(`/withdrawals/${withdrawalId}/cancel`);
    return response.data;
  } catch (error) {
    console.error('Error canceling withdrawal:', error);
    throw error;
  }
};

// Verify 2FA code before withdrawal
export const verify2FA = async (code: string): Promise<boolean> => {
  try {
    const response = await api.post("/auth/verify-2fa", { code });
    return response.data.success || false;
  } catch (error) {
    console.error('Error verifying 2FA:', error);
    return false;
  }
};