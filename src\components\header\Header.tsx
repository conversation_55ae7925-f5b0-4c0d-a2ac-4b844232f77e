import { Bell, BellRing, ChevronDown, HelpCircle, Apple, LogOut, Settings, User, Menu } from 'lucide-react';
import React, { useState } from 'react'
import './header.scss';
import { useNavigate } from 'react-router-dom';

interface Props {
  type: string;
  isMobileMenuOpen?: boolean;
  setIsMobileMenuOpen?: (open: boolean) => void;
}

function Header({type, isMobileMenuOpen, setIsMobileMenuOpen}:Props) {
    const naviagtion = useNavigate();
    const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
    const [selectedCurrency, setSelectedCurrency] = useState("USD");
    const [showNotifications, setShowNotifications] = useState(false);
    const [showProfileDropdown, setShowProfileDropdown] = useState(false);
    const [notifications, setNotifications] = useState([
      {
        id: 1,
        message: "Your deposit has been confirmed",
        time: "2 hours ago",
        read: false,
      },
      {
        id: 2,
        message: "Daily profit added: ****%",
        time: "5 hours ago",
        read: false,
      },
      {
        id: 3,
        message: "New referral joined your network",
        time: "Yesterday",
        read: true,
      },
    ]);

    const unreadNotificationsCount = notifications.filter((n) => !n.read).length;
    const currencies = ["USD", "EUR", "GBP", "JPY", "CNY", "INR"];

    const markAllAsRead = () => {
      setNotifications(notifications.map((n) => ({ ...n, read: true })));
    };

    const handleLogout = () => {
      localStorage.removeItem('token')
      naviagtion('/')
    };

  return (
      <header className="bg-[#1A1F2E] shadow-lg border-b border-gray-700 h-16 flex items-center justify-between px-3 sm:px-4 lg:px-8 fixed top-0 left-0 right-0 z-50">
        {/* Mobile Menu Button & Logo */}
        <div className="flex items-center min-w-0 flex-1">
          {/* Mobile Menu Button */}
          {setIsMobileMenuOpen && (
            <button
              id="mobile-menu-button"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden mr-2 sm:mr-3 p-2 text-gray-300 hover:text-white hover:bg-[#131722] rounded-lg transition-colors min-h-touch-target"
              aria-label="Toggle mobile menu"
            >
              <Menu className="w-5 h-5 sm:w-6 sm:h-6" />
            </button>
          )}

          {/* Logo */}
          <div className="text-white font-bold text-lg sm:text-xl flex items-center min-w-0">
            <div className="h-7 w-7 sm:h-8 sm:w-8 bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
              <Apple className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            </div>
            <span className="hidden sm:block truncate">Fruit-O-Cart</span>
            <span className="sm:hidden text-base font-bold">FOC</span>
          </div>
        </div>
        {type === 'admin' && (
          <div className="hidden md:flex items-center ml-auto mr-8">
            <div className="relative">
              <button
                onClick={() => setShowCurrencyDropdown(!showCurrencyDropdown)}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg border border-gray-600 hover:border-blue-400 transition-colors cursor-pointer bg-[#131722]"
              >
                <span className="text-sm font-medium text-white">
                  {selectedCurrency}
                </span>
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>
              {showCurrencyDropdown && (
                <div className="absolute top-full mt-1 w-24 bg-[#1A1F2E] rounded-xl shadow-lg py-1 z-20 border border-gray-700">
                  {currencies.map((currency) => (
                    <button
                      key={currency}
                      onClick={() => {
                        setSelectedCurrency(currency);
                        setShowCurrencyDropdown(false);
                      }}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-[#131722] transition-colors ${selectedCurrency === currency ? "text-blue-400 bg-[#131722]" : "text-white"}`}
                    >
                      {currency}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* User Actions */}
        <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-4">
          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 text-gray-300 hover:text-blue-400 transition-colors cursor-pointer min-h-touch-target"
              aria-label="Notifications"
            >
              <Bell className="w-5 h-5 sm:w-6 sm:h-6" />
              {unreadNotificationsCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center text-[10px] sm:text-xs">
                  {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
                </span>
              )}
            </button>
            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-72 sm:w-80 bg-[#1A1F2E] rounded-xl shadow-lg py-2 z-20 border border-gray-700 mobile-modal">
                <div className="px-3 sm:px-4 py-2 border-b border-gray-700 flex justify-between items-center">
                  <h3 className="text-sm font-medium text-white">
                    Notifications
                  </h3>
                  <button
                    onClick={markAllAsRead}
                    className="text-xs text-blue-400 hover:text-blue-300 cursor-pointer px-2 py-1 rounded min-h-touch-target"
                  >
                    Mark all as read
                  </button>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`px-4 py-3 hover:bg-[#131722] transition-colors ${!notification.read ? "bg-blue-900/20" : ""}`}
                    >
                      <div className="flex items-start">
                        <div
                          className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${!notification.read ? "bg-blue-900/30" : "bg-gray-700"}`}
                        >
                          <BellRing
                            className={`w-4 h-4 ${!notification.read ? "text-blue-400" : "text-gray-400"}`}
                          />
                        </div>
                        <div className="ml-3 flex-1">
                          <p
                            className={`text-sm ${!notification.read ? "font-medium text-white" : "text-gray-300"}`}
                          >
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-400 mt-1">
                            {notification.time}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="px-4 py-2 border-t border-gray-700 text-center">
                  <button className="text-sm text-blue-400 hover:text-blue-300 cursor-pointer">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>
          {/* Profile */}
          <div className="relative">
            <button
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              className="flex items-center space-x-1 sm:space-x-2 cursor-pointer hover:bg-[#131722] p-1.5 sm:p-2 rounded-lg transition-colors min-h-touch-target"
              aria-label="User profile menu"
            >
              <div className="h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] flex items-center justify-center text-white text-xs sm:text-sm font-medium">
                JD
              </div>
              <span className="hidden lg:block text-sm font-medium text-white truncate max-w-24">
                John Doe
              </span>
              <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400 hidden sm:block" />
            </button>
            {/* Profile Dropdown */}
            {showProfileDropdown && (
              <div className="absolute left-0 mt-2 w-44 sm:w-48 bg-[#1A1F2E] rounded-xl shadow-lg py-2 z-20 border border-gray-700 mobile-modal" style={{ right: 'auto', left: '-160px' }}>
                <button
                  className="w-full text-left block px-3 sm:px-4 py-2.5 sm:py-2 text-sm text-gray-300 hover:bg-[#131722] hover:text-white transition-colors min-h-touch-target"
                  onClick={() => {/* Handle profile navigation */}}
                >
                  <User className="w-4 h-4 inline mr-2 text-gray-400" /> Profile
                </button>
                <button
                  className="w-full text-left block px-3 sm:px-4 py-2.5 sm:py-2 text-sm text-gray-300 hover:bg-[#131722] hover:text-white transition-colors min-h-touch-target"
                  onClick={() => {/* Handle settings navigation */}}
                >
                  <Settings className="w-4 h-4 inline mr-2 text-gray-400" />{" "}
                  Settings
                </button>
                <button
                  className="w-full text-left block px-3 sm:px-4 py-2.5 sm:py-2 text-sm text-gray-300 hover:bg-[#131722] hover:text-white transition-colors min-h-touch-target"
                  onClick={() => {/* Handle help center navigation */}}
                >
                  <HelpCircle className="w-4 h-4 inline mr-2 text-gray-400" />{" "}
                  Help Center
                </button>
                <div className="border-t border-gray-700 my-1"></div>
                <button onClick={handleLogout}
                  className="w-full text-left block px-3 sm:px-4 py-2.5 sm:py-2 text-sm text-red-400 hover:bg-red-900/20 transition-colors min-h-touch-target"
                >
                  <LogOut className="w-4 h-4 inline mr-2" /> Sign out
                </button>
              </div>
            )}
          </div>
        </div>
      </header>
  )
}

export default Header