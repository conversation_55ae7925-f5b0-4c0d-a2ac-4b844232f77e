// Admin Profit Management Service

import api from '../../../../api/axiosInstance';
import { profitList, profitHeader } from './profit.model';
// import {
//   AdminProfit,
//   ProfitLog,
//   CreateAdminProfitRequest,
//   UpdateAdminProfitRequest,
//   CreateProfitLogRequest,
//   GetAdminProfitsResponse,
//   GetAdminProfitResponse,
//   CreateAdminProfitResponse,
//   UpdateAdminProfitResponse,
//   DeleteAdminProfitResponse,
//   GetProfitLogsResponse,
//   GetProfitLogResponse,
//   CreateProfitLogResponse,
//   DeleteProfitLogResponse,
//   AdminProfitFilters,
//   ProfitLogFilters,
//   ProfitStatistics,
//   ProfitDistributionSummary,
//   BulkProfitDistributionRequest,
//   ProfitCalculationRequest,
//   ProfitCalculationResponse
// } from './profit.model';

// class AdminProfitService {
//   private readonly adminProfitUrl = '/admin_profits';
//   private readonly profitLogUrl = '/profit_logs';

//   // ==================== ADMIN PROFITS MANAGEMENT ====================

//   /**
//    * Get all admin profits with pagination and filters
//    */
//   async getAdminProfits(filters?: AdminProfitFilters): Promise<GetAdminProfitsResponse> {
//     try {
//       const params = new URLSearchParams();

//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.rate_min) params.append('rate_min', filters.rate_min.toString());
//       if (filters?.rate_max) params.append('rate_max', filters.rate_max.toString());
//       if (filters?.distribution_status) params.append('distribution_status', filters.distribution_status);
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.adminProfitUrl}?${queryString}` : this.adminProfitUrl;

//       const response = await apiMethods.get<GetAdminProfitsResponse>(url);

//       return {
//         success: true,
//         message: 'Admin profits retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get admin profits error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get admin profit by ID
//    */
//   async getAdminProfitById(profitId: number): Promise<GetAdminProfitResponse> {
//     try {
//       const response = await apiMethods.get<GetAdminProfitResponse>(`${this.adminProfitUrl}/${profitId}`);

//       return {
//         success: true,
//         message: 'Admin profit retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get admin profit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Admin profit not found',
//           error: 'Admin profit not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new admin profit
//    */
//   async createAdminProfit(profitData: CreateAdminProfitRequest): Promise<CreateAdminProfitResponse> {
//     try {
//       const response = await apiMethods.post<CreateAdminProfitResponse>(this.adminProfitUrl, profitData);

//       return {
//         success: true,
//         message: 'Admin profit created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create admin profit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update admin profit
//    */
//   async updateAdminProfit(profitId: number, profitData: UpdateAdminProfitRequest): Promise<UpdateAdminProfitResponse> {
//     try {
//       const response = await apiMethods.put<UpdateAdminProfitResponse>(`${this.adminProfitUrl}/${profitId}`, profitData);

//       return {
//         success: true,
//         message: 'Admin profit updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update admin profit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Admin profit not found',
//           error: 'Admin profit not found'
//         };
//       }

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete admin profit
//    */
//   async deleteAdminProfit(profitId: number): Promise<DeleteAdminProfitResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteAdminProfitResponse>(`${this.adminProfitUrl}/${profitId}`);

//       return {
//         success: true,
//         message: 'Admin profit deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete admin profit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Admin profit not found',
//           error: 'Admin profit not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   // ==================== PROFIT LOGS MANAGEMENT ====================

//   /**
//    * Get all profit logs with pagination and filters
//    */
//   async getProfitLogs(filters?: ProfitLogFilters): Promise<GetProfitLogsResponse> {
//     try {
//       const params = new URLSearchParams();

//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.user_id) params.append('user_id', filters.user_id.toString());
//       if (filters?.deposit_id) params.append('deposit_id', filters.deposit_id.toString());
//       if (filters?.admin_profit_id) params.append('admin_profit_id', filters.admin_profit_id.toString());
//       if (filters?.status) params.append('status', filters.status);
//       if (filters?.amount_min) params.append('amount_min', filters.amount_min.toString());
//       if (filters?.amount_max) params.append('amount_max', filters.amount_max.toString());
//       if (filters?.percent_min) params.append('percent_min', filters.percent_min.toString());
//       if (filters?.percent_max) params.append('percent_max', filters.percent_max.toString());
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.profitLogUrl}?${queryString}` : this.profitLogUrl;

//       const response = await apiMethods.get<GetProfitLogsResponse>(url);

//       return {
//         success: true,
//         message: 'Profit logs retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get profit logs error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get profit log by ID
//    */
//   async getProfitLogById(logId: number): Promise<GetProfitLogResponse> {
//     try {
//       const response = await apiMethods.get<GetProfitLogResponse>(`${this.profitLogUrl}/${logId}`);

//       return {
//         success: true,
//         message: 'Profit log retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get profit log error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Profit log not found',
//           error: 'Profit log not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new profit log
//    */
//   async createProfitLog(logData: CreateProfitLogRequest): Promise<CreateProfitLogResponse> {
//     try {
//       const response = await apiMethods.post<CreateProfitLogResponse>(this.profitLogUrl, logData);

//       return {
//         success: true,
//         message: 'Profit log created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create profit log error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete profit log
//    */
//   async deleteProfitLog(logId: number): Promise<DeleteProfitLogResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteProfitLogResponse>(`${this.profitLogUrl}/${logId}`);

//       return {
//         success: true,
//         message: 'Profit log deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete profit log error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Profit log not found',
//           error: 'Profit log not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   // ==================== HELPER METHODS ====================

//   /**
//    * Get paginated admin profits (helper method)
//    */
//   async getPaginatedAdminProfits(page: number = 1, limit: number = 10, filters?: Omit<AdminProfitFilters, 'skip' | 'limit'>): Promise<GetAdminProfitsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getAdminProfits({ ...filters, skip, limit });
//   }

//   /**
//    * Get paginated profit logs (helper method)
//    */
//   async getPaginatedProfitLogs(page: number = 1, limit: number = 10, filters?: Omit<ProfitLogFilters, 'skip' | 'limit'>): Promise<GetProfitLogsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getProfitLogs({ ...filters, skip, limit });
//   }

//   /**
//    * Search admin profits
//    */
//   async searchAdminProfits(query: string, filters?: AdminProfitFilters): Promise<GetAdminProfitsResponse> {
//     return this.getAdminProfits({ ...filters, search: query });
//   }

//   /**
//    * Search profit logs
//    */
//   async searchProfitLogs(query: string, filters?: ProfitLogFilters): Promise<GetProfitLogsResponse> {
//     return this.getProfitLogs({ ...filters, search: query });
//   }

//   /**
//    * Get profit statistics
//    */
//   async getProfitStatistics(): Promise<{ success: boolean; data?: ProfitStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: ProfitStatistics }>(`${this.adminProfitUrl}/statistics`);

//       return {
//         success: true,
//         message: 'Profit statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get profit statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get profit distribution summary
//    */
//   async getProfitDistributionSummary(): Promise<{ success: boolean; data?: ProfitDistributionSummary[]; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: ProfitDistributionSummary[] }>(`${this.adminProfitUrl}/distribution-summary`);

//       return {
//         success: true,
//         message: 'Profit distribution summary retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get profit distribution summary error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Bulk distribute profits
//    */
//   async bulkDistributeProfits(distributionData: BulkProfitDistributionRequest): Promise<{ success: boolean; message: string; error?: string; data?: any }> {
//     try {
//       const response = await apiMethods.post<any>(`${this.adminProfitUrl}/bulk-distribute`, distributionData);

//       return {
//         success: true,
//         message: 'Profit distribution initiated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Bulk distribute profits error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Calculate profit
//    */
//   async calculateProfit(calculationData: ProfitCalculationRequest): Promise<{ success: boolean; data?: ProfitCalculationResponse; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.post<{ data: ProfitCalculationResponse }>(`${this.adminProfitUrl}/calculate`, calculationData);

//       return {
//         success: true,
//         message: 'Profit calculated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Calculate profit error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get profit logs by user ID
//    */
//   async getProfitLogsByUserId(userId: number, filters?: ProfitLogFilters): Promise<GetProfitLogsResponse> {
//     return this.getProfitLogs({ ...filters, user_id: userId });
//   }

//   /**
//    * Get profit logs by deposit ID
//    */
//   async getProfitLogsByDepositId(depositId: number, filters?: ProfitLogFilters): Promise<GetProfitLogsResponse> {
//     return this.getProfitLogs({ ...filters, deposit_id: depositId });
//   }
// }

// // Export singleton instance
// export const adminProfitService = new AdminProfitService();
// export default adminProfitService;

// Active API Functions
export const getProfitList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<profitList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }
    const response = await api.get<profitList>(`admin_profits/?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getProfitHeader = async () => {
  try {
    const response = await api.get("profit_header/");
    return response.data;
  } catch (error) {
    throw error;
  }
};
