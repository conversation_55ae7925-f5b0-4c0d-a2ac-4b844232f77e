// // Admin Users Management Service

import api from "../../../../api/axiosInstance";
import { userList, KycStatusUpdateResponse } from "./admin-users.model";

// import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
// import {
//   User,
//   CreateUserRequest,
//   UpdateUserRequest,
//   GetUsersResponse,
//   GetUserResponse,
//   CreateUserResponse,
//   UpdateUserResponse,
//   DeleteUserResponse,
//   UserFilters,
//   UserStatistics
// } from './admin-users.model';

// class AdminUsersService {
//   private readonly baseUrl = '/users';

//   // ==================== CORE CRUD METHODS ====================

//   /**
//    * Get all users with pagination and filters
//    */
//   async getUsers(filters?: UserFilters): Promise<GetUsersResponse> {
//     try {
//       const params = new URLSearchParams();

//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.rank) params.append('rank', filters.rank);
//       if (filters?.kyc_status) params.append('kyc_status', filters.kyc_status);
//       if (filters?.is_admin !== undefined) params.append('is_admin', filters.is_admin.toString());
//       if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
//       if (filters?.is_kyc_verified !== undefined) params.append('is_kyc_verified', filters.is_kyc_verified.toString());
//       if (filters?.nationality) params.append('nationality', filters.nationality);
//       if (filters?.preferred_currency) params.append('preferred_currency', filters.preferred_currency);
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

//       const response = await apiMethods.get<GetUsersResponse>(url);

//       return {
//         success: true,
//         message: 'Users retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get users error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get user by ID
//    */
//   async getUserById(userId: number): Promise<GetUserResponse> {
//     try {
//       const response = await apiMethods.get<GetUserResponse>(`${this.baseUrl}/${userId}`);

//       return {
//         success: true,
//         message: 'User retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get user error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'User not found',
//           error: 'User not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new user
//    */
//   async createUser(userData: CreateUserRequest): Promise<CreateUserResponse> {
//     try {
//       const response = await apiMethods.post<CreateUserResponse>(this.baseUrl, userData);

//       return {
//         success: true,
//         message: 'User created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create user error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 409) {
//         return {
//           success: false,
//           message: 'User with this email already exists',
//           error: 'Email already exists'
//         };
//       }

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update user
//    */
//   async updateUser(userId: number, userData: UpdateUserRequest): Promise<UpdateUserResponse> {
//     try {
//       const response = await apiMethods.put<UpdateUserResponse>(`${this.baseUrl}/${userId}`, userData);

//       return {
//         success: true,
//         message: 'User updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update user error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'User not found',
//           error: 'User not found'
//         };
//       }

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete user
//    */
//   async deleteUser(userId: number): Promise<DeleteUserResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteUserResponse>(`${this.baseUrl}/${userId}`);

//       return {
//         success: true,
//         message: 'User deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete user error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'User not found',
//           error: 'User not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   // ==================== HELPER METHODS ====================

//   /**
//    * Get paginated users (helper method)
//    */
//   async getPaginatedUsers(page: number = 1, limit: number = 10, filters?: Omit<UserFilters, 'skip' | 'limit'>): Promise<GetUsersResponse> {
//     const skip = (page - 1) * limit;
//     return this.getUsers({ ...filters, skip, limit });
//   }

//   /**
//    * Search users
//    */
//   async searchUsers(query: string, filters?: UserFilters): Promise<GetUsersResponse> {
//     return this.getUsers({ ...filters, search: query });
//   }

//   /**
//    * Get user statistics
//    */
//   async getUserStatistics(): Promise<{ success: boolean; data?: UserStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: UserStatistics }>(`${this.baseUrl}/statistics`);

//       return {
//         success: true,
//         message: 'Statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get user statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }
// }

// // Export singleton instance
// export const adminUsersService = new AdminUsersService();
// export default adminUsersService;

export const getUserList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<userList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(`users/?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Update KYC Status - Using the correct endpoint as specified
export const updateKycStatus = async (
  userId: number,
  kycStatus: 'pending' | 'verified' | 'rejected',
  rejectionReason?: string
): Promise<KycStatusUpdateResponse> => {
  try {
    const requestData: any = {
      kyc_status: kycStatus
    };

    // Add rejection reason if provided
    if (rejectionReason) {
      requestData.rejection_reason = rejectionReason;
    }

    console.log('Updating KYC status:', { userId, kycStatus, rejectionReason });

    const response = await api.post(`/users/${userId}/kyc_status`, requestData);

    return {
      success: true,
      message: 'KYC status updated successfully',
      data: response.data
    };
  } catch (error: any) {
    console.error('Update KYC status error:', error);

    return {
      success: false,
      message: error.response?.data?.message || 'Failed to update KYC status',
      error: error.response?.data?.detail || error.message
    };
  }
};

// Confirm KYC (set to verified)
export const confirmKyc = async (userId: number): Promise<KycStatusUpdateResponse> => {
  return updateKycStatus(userId, 'verified');
};

// Reject KYC with reason
export const rejectKyc = async (userId: number, reason: string): Promise<KycStatusUpdateResponse> => {
  return updateKycStatus(userId, 'rejected', reason);
};

// Delete User
export const deleteUser = async (userId: number): Promise<KycStatusUpdateResponse> => {
  try {
    console.log('Deleting user:', userId);

    const response = await api.delete(`/users/${userId}/`);

    return {
      success: true,
      message: 'User deleted successfully',
      data: response.data
    };
  } catch (error: any) {
    console.error('Delete user error:', error);

    return {
      success: false,
      message: error.response?.data?.message || 'Failed to delete user',
      error: error.response?.data?.detail || error.message
    };
  }
};