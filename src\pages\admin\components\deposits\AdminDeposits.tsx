import { Check, Eye, X, Search, Filter } from "lucide-react";
import React, { useState } from "react";
import { deposite } from "./deposits.model";
import Pagination from "../../../../components/Pagination/Pagination";
import { getDepositList } from "./deposits-service";
import TableShimmer from "../../../../components/shimmers/TableShimmer";
import TableNoDataRow from "../../../../components/utils/TableNoDataRow";

// Removed unused Deposit interface - using deposite from model instead

const AdminDeposits: React.FC = () => {
  const [isShimmerLoading] = useState<boolean>(true);
  const [deposits, setDeposits] = useState<deposite[]>([]);
  const [search, setSearch] = useState<string>("");
  const [status] = useState<string>("");
  const [date] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  const getDeposits = async () => {
    try {
      const response = await getDepositList(
        (currentPage - 1) * itemsPerPage,
        itemsPerPage,
        search,
        status,
        date
      );
      setDeposits(response.items);
      setCurrentPage(response.current_page);
      setTotalPages(response.total_pages);
    } catch (error) {
      console.log(error);
    }
  };

  const handlePage = (page: number) => {
    setCurrentPage(page);
    getDeposits();
  };

  const handleItemsPerPage = (value: number) => {
    setItemsPerPage(value);
    getDeposits();
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    getDeposits();
  };

  // Removed unused handleStatus function

  return (
    <div className="bg-slate-800 rounded-lg shadow-lg border border-slate-700">
      <div className="px-6 py-4 border-b border-slate-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">
          Deposit Verification
        </h3>
        <div className="flex space-x-2">
          <div className="relative">
            <input
              type="search"
              onChange={handleSearch}
              value={search}
              placeholder="Search deposits..."
              className="pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-colors duration-200 flex items-center space-x-2">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-700/50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                User
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Amount
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Transaction ID
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {isShimmerLoading ? (
              <TableShimmer columns={6} rows={itemsPerPage} />
            ) : deposits.length === 0 ? (
              <TableNoDataRow
                colSpan={6}
                type="deposits"
                showSearchHint={!!search}
                showFilterHint={!!status}
              />
            ) : (
              deposits.map((deposit, index) => (
                <tr
                  key={index}
                  className="hover:bg-slate-700/30 transition-colors duration-150"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">
                      {deposit.user_name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">
                      {deposit.amount}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">
                      {deposit.created_at}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300 font-mono">
                      {deposit.transaction_id}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        deposit.status === "approved"
                          ? "bg-green-900 text-green-300"
                          : deposit.status === "pending"
                          ? "bg-yellow-900 text-yellow-300"
                          : "bg-red-900 text-red-300"
                      }`}
                    >
                      {deposit.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-3">
                      <button
                        className="text-blue-400 hover:text-blue-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                        aria-label="View deposit"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {deposit.status === "Pending" && (
                        <>
                          <button
                            className="text-green-400 hover:text-green-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                            aria-label="Approve deposit"
                          >
                            <Check className="w-4 h-4" />
                          </button>
                          <button
                            className="text-red-400 hover:text-red-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                            aria-label="Reject deposit"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      <Pagination
        handleItemsPerPageChange={handleItemsPerPage}
        handlePage={handlePage}
        page={currentPage}
        itemsPerPage={itemsPerPage}
        totalPages={totalPages}
      />
    </div>
  );
};

export default AdminDeposits;
