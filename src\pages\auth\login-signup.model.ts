// Authentication Models and Interfaces

// Base API Response Interface
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: Record<string, string[]>;
}

// Token Response Interface
export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type?: string;
  expires_in?: number;
}

// User Interface
export interface User {
  id: string;
  email: string;
  fullName: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  nationality?: string;
  countryOfResidence?: string;
  currency?: string;
  address?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  kycStatus: 'pending' | 'verified' | 'rejected';
  role: 'user' | 'admin';
  createdAt: string;
  updatedAt: string;
}

// Login Request Interface
export interface LoginRequest {
  email: string;
  password: string;
}

export interface loginResponse{
  access_token: string;
  refresh_token: string;
  token_type: string;
  user_role: string;
}

// Signup Request Interface
export interface SignupRequest {
  fullName: string;
  email: string;
  password: string;
  phoneNumber: string;
  dateOfBirth: string;
  nationality: string;
  countryOfResidence: string;
  currency: string;
  address: string;
  referralCode?: string;
  termsAccepted: boolean;
  privacyAccepted: boolean;
}

// Signup Response Interface
export interface SignupResponse extends ApiResponse<{
  user: User;
  tokens: TokenResponse;
  requiresEmailVerification?: boolean;
}> {}

// Forgot Password Request Interface
export interface ForgotPasswordRequest {
  email: string;
}

// Forgot Password Response Interface
export interface ForgotPasswordResponse extends ApiResponse<{
  message: string;
  resetToken?: string;
}> {}

// Reset Password Request Interface
export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// Reset Password Response Interface
export interface ResetPasswordResponse extends ApiResponse<{
  message: string;
}> {}

// Email Verification Request Interface
export interface EmailVerificationRequest {
  token: string;
}

// Email Verification Response Interface
export interface EmailVerificationResponse extends ApiResponse<{
  message: string;
  user?: User;
}> {}

// Send OTP Request Interface
export interface SendOTPRequest {
  email: string;
  type: 'email_verification' | 'password_reset';
}

// Send OTP Response Interface
export interface SendOTPResponse extends ApiResponse<{
  message: string;
  expiresIn: number;
}> {}

// Verify OTP Request Interface
export interface VerifyOTPRequest {
  email: string;
  otp: string;
  type: 'email_verification' | 'password_reset';
}

// Verify OTP Response Interface
export interface VerifyOTPResponse extends ApiResponse<{
  message: string;
  verified: boolean;
  token?: string;
}> {}

// Form Validation Errors Interface
export interface FormErrors {
  fullName?: string;
  email?: string;
  password?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  nationality?: string;
  countryOfResidence?: string;
  currency?: string;
  address?: string;
  referralCode?: string;
  termsAccepted?: string;
  privacyAccepted?: string;
  otp?: string;
  general?: string;
}

// Auth State Interface
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}

// Country Interface
export interface Country {
  code: string;
  name: string;
  flag?: string;
}

// Currency Interface
export interface Currency {
  code: string;
  name: string;
  symbol?: string;
}

// File Upload Interface
export interface FileUpload {
  file: File;
  preview: string;
  type: 'nationalid' | 'passport' | 'other';
}

// Document Upload Request Interface
export interface DocumentUploadRequest {
  userId: string;
  documentType: string;
  file: File;
}

// Document Upload Response Interface
export interface DocumentUploadResponse extends ApiResponse<{
  documentId: string;
  documentUrl: string;
  status: 'uploaded' | 'pending_verification' | 'verified' | 'rejected';
}> {}

export interface SignupRequestBody {
  name: string
  dob: string
  nationality: string
  country_of_residence: string
  preferred_currency: string
  address: string
  country_code: string
  phone: string
  email: string
  national_id: File | null
  passport: File | null
  rank: string
  is_admin: boolean
  is_active: boolean
  is_kyc_verified: boolean
  kyc_status: string
  profile_picture: string
  referral_user_code: string
  password: string
}
