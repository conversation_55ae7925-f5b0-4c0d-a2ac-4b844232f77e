# System Architecture Patterns

## Application Architecture
### Frontend Architecture
- **Framework**: React 19.1.0 with TypeScript for type safety
- **State Management**: Context API (ContextGlobal.tsx) for global state
- **Routing**: React Router DOM with nested routes for user/admin areas
- **Styling**: Hybrid approach - Tailwind CSS for utilities + SCSS for custom styles

### Folder Structure Pattern
```
src/
├── api/                    # API layer
│   ├── axiosInstance.ts   # HTTP client configuration
│   └── examples.ts        # API usage examples
├── components/            # Reusable components
│   ├── header/           # Header components
│   ├── loader/           # Loading components
│   └── sidebar/          # Navigation components
├── pages/                # Page components
│   ├── admin/           # Admin-specific pages
│   ├── auth/            # Authentication pages
│   ├── terms-policy/    # Legal pages
│   └── user/            # User-specific pages
├── models/              # TypeScript interfaces
└── App.tsx              # Main application component
```

## Design Patterns

### Component Patterns
1. **Layout Components**: Separate layouts for user and admin areas
2. **Page Components**: Individual pages within layouts
3. **Sub-page Components**: Nested components within page areas
4. **Shared Components**: Reusable UI elements

### API Patterns
1. **Service Layer**: Separate service files for API calls
2. **Model Layer**: TypeScript interfaces for data structures
3. **Instance Configuration**: Centralized axios configuration
4. **Authentication**: JWT token management with refresh logic
5. **Error Handling**: Centralized error handling and retry logic

### State Management Patterns
1. **Global Context**: Application-wide state management
2. **Local State**: Component-specific state with useState
3. **Form State**: Form handling with validation

### Routing Patterns
1. **Nested Routes**: Hierarchical routing structure
2. **Protected Routes**: Authentication-based route protection
3. **Role-based Routing**: Different routes for user/admin roles
4. **Default Redirects**: Automatic navigation to appropriate dashboards

## Technical Decisions

### Authentication Strategy
- JWT tokens with access/refresh token pattern
- Local storage for token persistence
- Automatic token refresh on API calls
- Redirect to login on authentication failure

### Styling Strategy
- Tailwind CSS for rapid development and consistency
- SCSS for complex custom styles
- Dark theme as primary design
- Responsive design with mobile-first approach

### API Strategy
- Separate auth and main API instances
- Comprehensive error handling and retry logic
- File upload support with validation
- Pagination and search helpers
- Type-safe API responses

### Development Strategy
- TypeScript for type safety
- Component-based architecture
- Separation of concerns
- Reusable utility functions

## Code Organization Principles
1. **Single Responsibility**: Each component/function has one clear purpose
2. **DRY (Don't Repeat Yourself)**: Shared logic in utilities/services
3. **Separation of Concerns**: Clear boundaries between UI, logic, and data
4. **Type Safety**: Comprehensive TypeScript usage
5. **Modularity**: Components and services are modular and reusable
