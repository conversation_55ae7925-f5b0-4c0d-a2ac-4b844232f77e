// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useRef, useEffect } from "react";
import {
  Eye,
  EyeOff,
  Upload,
  X,
  FileText,
  Loader,
  HelpCircle,
  Mail,
  Edit

} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { FormErrors } from "./login-signup.model";
import { SignupApi } from "./login-signup-serivce";
import { TermsModal, PrivacyModal } from "../../components/modals";
const Signup: React.FC = () => {
  const navigate = useNavigate();

  // Form state - matching SignupRequest interface
  const [formData, setFormData] = useState({
    name: "",
    dob: "",
    nationality: "",
    country_of_residence: "",
    preferred_currency: "",
    address: "",
    phone: "",
    email: "",
    password: "",
    referral_user_code: "",
    country_code: "+1", // Default country code
  });

  // Error state
  const [errors, setErrors] = useState<FormErrors>({});
  // Dropdown visibility states
  const [showNationalityDropdown, setShowNationalityDropdown] = useState(false);
  const [showResidenceDropdown, setShowResidenceDropdown] = useState(false);
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!(event.target as HTMLElement).closest(".relative")) {
        setShowNationalityDropdown(false);
        setShowResidenceDropdown(false);
        setShowCurrencyDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Password visibility state
  const [showPassword, setShowPassword] = useState(false);
  // Document upload states
  const [aadhaarFile, setAadhaarFile] = useState<File | null>(null);
  const [passportFile, setPassportFile] = useState<File | null>(null);
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [aadhaarPreview, setAadhaarPreview] = useState<string | null>(null);
  const [passportPreview, setPassportPreview] = useState<string | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);
  // Checkbox states
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Modal states
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);

  // Email verification states
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [otpValues, setOtpValues] = useState(['', '', '', '', '', '']);
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);
  const [canResendOTP, setCanResendOTP] = useState(true);
  const [otpError, setOtpError] = useState('');
  const [isEditingEmail, setIsEditingEmail] = useState(false);

  // Password strength
  const [passwordStrength, setPasswordStrength] = useState(0);

  // OTP Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (otpTimer > 0) {
      interval = setInterval(() => {
        setOtpTimer((prev) => {
          if (prev <= 1) {
            setCanResendOTP(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [otpTimer]);
  // File input refs
  const aadhaarInputRef = useRef<HTMLInputElement>(null);
  const passportInputRef = useRef<HTMLInputElement>(null);
  const profilePictureInputRef = useRef<HTMLInputElement>(null);
  // Country and currency data
  const countries = [
    { code: "US", name: "United States" },
    { code: "GB", name: "United Kingdom" },
    { code: "CA", name: "Canada" },
    { code: "AU", name: "Australia" },
    { code: "IN", name: "India" },
    // Add more countries as needed
  ];
  const currencies = [
    { code: "USD", name: "US Dollar" },
    { code: "EUR", name: "Euro" },
    { code: "GBP", name: "British Pound" },
    { code: "JPY", name: "Japanese Yen" },
    { code: "AUD", name: "Australian Dollar" },
    // Add more currencies as needed
  ];
  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    // Calculate password strength
    if (name === "password") {
      let strength = 0;
      if (value.length >= 8) strength += 1;
      if (/[A-Z]/.test(value)) strength += 1;
      if (/[0-9]/.test(value)) strength += 1;
      if (/[^A-Za-z0-9]/.test(value)) strength += 1;
      setPasswordStrength(strength);
    }
  };
  // Handle file uploads
  const handleFileUpload = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "aadhaar" | "passport" | "profile",
  ) => {
    const file = e.target.files?.[0] || null;
    if (!file) return;
    if (type === "aadhaar") {
      setAadhaarFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setAadhaarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else if (type === "passport") {
      setPassportFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setPassportPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else if (type === "profile") {
      setProfilePictureFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setProfilePicturePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  // Trigger file input click
  const triggerFileInput = (type: "aadhaar" | "passport" | "profile") => {
    if (type === "aadhaar" && aadhaarInputRef.current) {
      aadhaarInputRef.current.click();
    } else if (type === "passport" && passportInputRef.current) {
      passportInputRef.current.click();
    } else if (type === "profile" && profilePictureInputRef.current) {
      profilePictureInputRef.current.click();
    }
  };
  // Email verification functions
  const handleSendOTP = async () => {
    if (!formData.email) {
      setOtpError('Please enter your email first');
      return;
    }

    setIsSendingOTP(true);
    setOtpError('');

    try {
      const { sendOTPApi } = await import('./login-signup-serivce');
      const response = await sendOTPApi(formData.email);

      if (response.success) {
        setShowEmailVerification(true);
        setOtpTimer(60);
        setCanResendOTP(false);
        setOtpError('');
      } else {
        setOtpError(response.message || 'Failed to send OTP');
      }
    } catch (error: any) {
      setOtpError(error.response?.data?.message || 'Failed to send OTP. Please try again.');
    } finally {
      setIsSendingOTP(false);
    }
  };

  const handleVerifyOTP = async () => {
    const otpString = otpValues.join('');
    if (otpString.length !== 6) {
      setOtpError('Please enter a valid 6-digit OTP');
      return;
    }

    setIsVerifyingEmail(true);
    setOtpError('');

    try {
      const { verifyOTPApi } = await import('./login-signup-serivce');
      const response = await verifyOTPApi(formData.email, otpString);

      if (response.success && response.data?.verified) {
        setIsEmailVerified(true);
        setOtpError('');
      } else {
        setOtpError(response.message || 'Invalid OTP. Please try again.');
      }
    } catch (error: any) {
      setOtpError(error.response?.data?.message || 'Failed to verify OTP. Please try again.');
    } finally {
      setIsVerifyingEmail(false);
    }
  };

  const handleOTPChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      nextInput?.focus();
    }
  };

  const handleOTPKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      prevInput?.focus();
    }
  };

  const handleEditEmail = () => {
    setIsEditingEmail(true);
    setShowEmailVerification(false);
    setIsEmailVerified(false);
    setOtpValues(['', '', '', '', '', '']);
    setOtpError('');
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors: FormErrors = {};
    if (!formData.name) newErrors.fullName = "Full name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!isEmailVerified) newErrors.email = "Please verify your email address";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.phone) newErrors.phoneNumber = "Phone number is required";
    if (!formData.dob) newErrors.dateOfBirth = "Date of birth is required";
    if (!formData.nationality) newErrors.nationality = "Nationality is required";
    if (!formData.country_of_residence) newErrors.countryOfResidence = "Country of residence is required";
    if (!formData.preferred_currency) newErrors.currency = "Currency is required";
    if (!formData.address) newErrors.address = "Address is required";
    if (!termsAccepted) newErrors.termsAccepted = "You must accept the terms and conditions";
    if (!privacyAccepted) newErrors.privacyAccepted = "You must accept the privacy policy";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData for file upload
      const formDataToSend = new FormData();

      // Add all form fields
      formDataToSend.append('name', formData.name);
      formDataToSend.append('email', formData.email);
      formDataToSend.append('password', formData.password);
      formDataToSend.append('dob', formData.dob);
      formDataToSend.append('nationality', formData.nationality);
      formDataToSend.append('country_of_residence', formData.country_of_residence);
      formDataToSend.append('preferred_currency', formData.preferred_currency);
      formDataToSend.append('address', formData.address);
      formDataToSend.append('country_code', formData.country_code);
      formDataToSend.append('phone', formData.phone);
      formDataToSend.append('referral_user_code', formData.referral_user_code);

      // Add files
      if (aadhaarFile) {
        formDataToSend.append('national_id', aadhaarFile);
      }
      if (passportFile) {
        formDataToSend.append('passport', passportFile);
      }
      if (profilePictureFile) {
        formDataToSend.append('profile_picture', profilePictureFile);
      }

      // Add default values with proper types
      // formDataToSend.append('rank', 'user');
      // formDataToSend.append('is_admin', 'false');
      // formDataToSend.append('is_active', 'true');
      // formDataToSend.append('is_kyc_verified', 'false');
      // formDataToSend.append('kyc_status', 'pending');

      // Debug: Log FormData contents (remove this in production)
      console.log('FormData contents:');
      formDataToSend.forEach((value, key) => {
        console.log(key, value);
      });

      const response = await SignupApi(formDataToSend);

      if (response.success || response.access_token) {
        // Success - redirect to OTP verification page
        navigate('/verify-otp', {
          state: {
            email: formData.email,
            fromSignup: true
          }
        });
      } else {
        setErrors({ general: response.message || 'Signup failed. Please try again.' });
      }
    } catch (error: any) {
      console.error('Signup error:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);
      setErrors({
        general: error.response?.data?.message || error.response?.data?.detail || 'An error occurred during signup. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  // Calculate form completion percentage
  const calculateProgress = () => {
    let filledFields = 0;
    let totalRequiredFields = 11; // Including email verification
    if (formData.name) filledFields++;
    if (formData.dob) filledFields++;
    if (formData.nationality) filledFields++;
    if (formData.country_of_residence) filledFields++;
    if (formData.preferred_currency) filledFields++;
    if (formData.address) filledFields++;
    if (formData.phone) filledFields++;
    if (formData.email) filledFields++;
    if (isEmailVerified) filledFields++; // Email verification step
    if (formData.password) filledFields++;
    if (aadhaarFile) filledFields++;
    if (passportFile) filledFields++;
    if (profilePictureFile) filledFields++;
    if (termsAccepted) filledFields++;
    if (privacyAccepted) filledFields++;
    return Math.round((filledFields / (totalRequiredFields + 4)) * 100);
  };
  return (
    <div className="min-h-screen bg-[#0D1117] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto bg-[#1A1F2E] rounded-lg shadow-lg overflow-hidden">
        {/* Progress bar */}
        <div className="w-full bg-gray-700 h-2">
          <div
            className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] h-2 transition-all duration-500 ease-in-out"
            style={{ width: `${calculateProgress()}%` }}
          ></div>
        </div>
        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-10">
            <h1 className="text-3xl font-extrabold text-white mb-2">Sign Up</h1>
            <p className="text-gray-400 max-w-md mx-auto">
              Complete the form below to create your account. All fields marked
              with * are required.
            </p>
          </div>
          <form onSubmit={handleSubmit}>
            {/* General Error Display */}
            {errors.general && (
              <div className="mb-6 bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-md text-sm">
                {errors.general}
              </div>
            )}

            {/* Personal Information Section */}
            <div className="mb-10">
              <h2 className="text-xl font-bold text-white mb-6 pb-2 border-b border-gray-700">
                Personal Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Full Name */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    className={`w-full px-4 py-2 border rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white ${
                      errors.fullName ? 'border-red-500' : 'border-gray-700'
                    }`}
                    required
                  />
                  {errors.fullName && (
                    <p className="mt-1 text-sm text-red-400">{errors.fullName}</p>
                  )}
                </div>
                {/* Date of Birth */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="dob"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Date of Birth *
                  </label>
                  <input
                    type="date"
                    id="dob"
                    name="dob"
                    value={formData.dob}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                    style={{
                      colorScheme: 'dark'
                    }}
                    required
                  />
                </div>
                {/* Nationality */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="nationality"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Nationality *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="nationality"
                      name="nationality"
                      value={formData.nationality}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          nationality: e.target.value,
                        });
                        setShowNationalityDropdown(true);
                      }}
                      onFocus={() => setShowNationalityDropdown(true)}
                      placeholder="Search nationality"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    {showNationalityDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-[#1A1F2E] border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto custom-scrollbar">
                        {countries
                          .filter((country) =>
                            country.name
                              .toLowerCase()
                              .includes(formData.nationality.toLowerCase()),
                          )
                          .map((country) => (
                            <div
                              key={country.code}
                              className="px-4 py-2 hover:bg-[#242938] cursor-pointer text-sm text-white"
                              onClick={() => {
                                setFormData({
                                  ...formData,
                                  nationality: country.name,
                                });
                                setShowNationalityDropdown(false);
                              }}
                            >
                              {country.name}
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {/* Country of Residence */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="country_of_residence"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Country of Residence *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="country_of_residence"
                      name="country_of_residence"
                      value={formData.country_of_residence}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          country_of_residence: e.target.value,
                        });
                        setShowResidenceDropdown(true);
                      }}
                      onFocus={() => setShowResidenceDropdown(true)}
                      placeholder="Search country of residence"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    {showResidenceDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-[#1A1F2E] border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto custom-scrollbar">
                        {countries
                          .filter((country) =>
                            country.name
                              .toLowerCase()
                              .includes(
                                formData.country_of_residence.toLowerCase(),
                              ),
                          )
                          .map((country) => (
                            <div
                              key={country.code}
                              className="px-4 py-2 hover:bg-[#242938] cursor-pointer text-sm text-white"
                              onClick={() => {
                                setFormData({
                                  ...formData,
                                  country_of_residence: country.name,
                                });
                                setShowResidenceDropdown(false);
                              }}
                            >
                              {country.name}
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {/* Currency */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="preferred_currency"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Preferred Currency *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="preferred_currency"
                      name="preferred_currency"
                      value={formData.preferred_currency}
                      onChange={(e) => {
                        setFormData({ ...formData, preferred_currency: e.target.value });
                        setShowCurrencyDropdown(true);
                      }}
                      onFocus={() => setShowCurrencyDropdown(true)}
                      placeholder="Search currency"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    {showCurrencyDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-[#1A1F2E] border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto custom-scrollbar">
                        {currencies
                          .filter(
                            (currency) =>
                              currency.name
                                .toLowerCase()
                                .includes(formData.preferred_currency.toLowerCase()) ||
                              currency.code
                                .toLowerCase()
                                .includes(formData.preferred_currency.toLowerCase()),
                          )
                          .map((currency) => (
                            <div
                              key={currency.code}
                              className="px-4 py-2 hover:bg-[#242938] cursor-pointer text-sm text-white"
                              onClick={() => {
                                setFormData({
                                  ...formData,
                                  preferred_currency: `${currency.name} (${currency.code})`,
                                });
                                setShowCurrencyDropdown(false);
                              }}
                            >
                              {currency.name} ({currency.code})
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {/* Address */}
                <div className="col-span-2">
                  <label
                    htmlFor="address"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Address *
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="Enter your full address"
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                    required
                  />
                </div>
                {/* Phone Number */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Phone Number *
                  </label>
                  <div className="flex">
                    <select
                      value={formData.country_code}
                      onChange={(e) => setFormData({ ...formData, country_code: e.target.value })}
                      className="px-3 py-2 border border-r-0 border-gray-700 bg-[#1A1F2E] rounded-l-md text-white text-sm focus:ring-[#3B82F6] focus:border-[#3B82F6] focus:outline-none"
                      style={{
                        colorScheme: 'dark'
                      }}
                    >
                      <option value="+1" className="bg-[#1A1F2E] text-white">+1</option>
                      <option value="+44" className="bg-[#1A1F2E] text-white">+44</option>
                      <option value="+91" className="bg-[#1A1F2E] text-white">+91</option>
                      <option value="+61" className="bg-[#1A1F2E] text-white">+61</option>
                    </select>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Enter your phone number"
                      className="w-full px-4 py-2 border border-gray-700 bg-[#1A1F2E] rounded-r-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-white text-sm"
                      required
                    />
                  </div>
                </div>
                {/* Email Section */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Email Address *
                  </label>

                  <div className="space-y-2">
                    {/* Email Input Section */}
                    <div className="flex space-x-2">
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email address"
                        className={`flex-1 px-4 py-2 border rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white ${
                          errors.email ? 'border-red-500' : 'border-gray-700'
                        }`}
                        required
                        disabled={isEmailVerified}
                      />
                      <button
                        type="button"
                        onClick={handleSendOTP}
                        disabled={!formData.email || isSendingOTP || isEmailVerified}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                      >
                        {isSendingOTP ? (
                          <Loader className="w-4 h-4 animate-spin" />
                        ) : isEmailVerified ? (
                          '✓ Verified'
                        ) : (
                          'Verify'
                        )}
                      </button>
                    </div>

                    {/* Email Status Messages */}
                    {errors.email && (
                      <p className="text-sm text-red-400">{errors.email}</p>
                    )}
                    {!isEmailVerified && !showEmailVerification && (
                      <p className="text-xs text-gray-500">
                        Click verify to confirm your email address
                      </p>
                    )}
                    {isEmailVerified && (
                      <p className="text-xs text-green-400">
                        ✓ Email verified successfully
                      </p>
                    )}

                    {/* OTP Verification Section - Animated Toggle */}
                    <div className={`overflow-hidden transition-all duration-500 ease-in-out ${
                      showEmailVerification
                        ? 'max-h-96 opacity-100 transform translate-y-0'
                        : 'max-h-0 opacity-0 transform -translate-y-4'
                    }`}>
                      <div className="pt-4 space-y-4">
                        {/* Email Display with Edit Button */}
                        <div className="flex items-center justify-between p-3 bg-gray-700 rounded-md border border-gray-600">
                          <div className="flex items-center space-x-2">
                            <Mail className="w-4 h-4 text-blue-400" />
                            <span className="text-sm text-white">{formData.email}</span>
                          </div>
                          <button
                            type="button"
                            onClick={handleEditEmail}
                            className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors"
                          >
                            Edit
                          </button>
                        </div>

                        {/* OTP Input Section */}
                        <div className="space-y-3">
                          <p className="text-sm text-gray-300 text-center">
                            Enter the 6-digit code sent to your email:
                          </p>

                          {/* OTP Input Boxes */}
                          <div className="flex space-x-3 justify-center">
                            {otpValues.map((value, index) => (
                              <input
                                key={index}
                                id={`otp-${index}`}
                                type="text"
                                maxLength={1}
                                value={value}
                                onChange={(e) => handleOTPChange(index, e.target.value)}
                                onKeyDown={(e) => handleOTPKeyDown(index, e)}
                                className="w-12 h-12 text-center text-lg font-bold bg-gray-700 border-2 border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white transition-all duration-200 hover:border-gray-500"
                                disabled={isVerifyingEmail}
                                placeholder="•"
                              />
                            ))}
                          </div>

                          {/* OTP Actions */}
                          <div className="flex items-center justify-between pt-2">
                            <button
                              type="button"
                              onClick={handleSendOTP}
                              disabled={!canResendOTP || isSendingOTP}
                              className="text-sm text-blue-400 hover:text-blue-300 disabled:text-gray-500 disabled:cursor-not-allowed transition-colors"
                            >
                              {isSendingOTP ? (
                                <span className="flex items-center space-x-1">
                                  <Loader className="w-3 h-3 animate-spin" />
                                  <span>Sending...</span>
                                </span>
                              ) : canResendOTP ? (
                                'Resend OTP'
                              ) : (
                                `Resend in ${otpTimer}s`
                              )}
                            </button>
                            <button
                              type="button"
                              onClick={handleVerifyOTP}
                              disabled={otpValues.join('').length !== 6 || isVerifyingEmail}
                              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            >
                              {isVerifyingEmail ? (
                                <span className="flex items-center space-x-2">
                                  <Loader className="w-4 h-4 animate-spin" />
                                  <span>Verifying...</span>
                                </span>
                              ) : (
                                'Verify OTP'
                              )}
                            </button>
                          </div>

                          {/* OTP Error */}
                          {otpError && (
                            <div className="text-center">
                              <p className="text-sm text-red-400 bg-red-900/20 border border-red-800 rounded-md px-3 py-2">
                                {otpError}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Password */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Create a strong password"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="w-5 h-5 text-gray-400" />
                      ) : (
                        <Eye className="w-5 h-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {/* Password strength indicator */}
                  <div className="mt-2">
                    <div className="flex items-center">
                      <div className="w-full bg-gray-700 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            passwordStrength === 0
                              ? "bg-gray-700"
                              : passwordStrength === 1
                                ? "bg-red-500"
                                : passwordStrength === 2
                                  ? "bg-yellow-500"
                                  : passwordStrength === 3
                                    ? "bg-blue-500"
                                    : "bg-green-500"
                          }`}
                          style={{ width: `${passwordStrength * 25}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-400">
                        {passwordStrength === 0
                          ? "Weak"
                          : passwordStrength === 1
                            ? "Fair"
                            : passwordStrength === 2
                              ? "Good"
                              : passwordStrength === 3
                                ? "Strong"
                                : "Very Strong"}
                      </span>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      Use 8+ characters with a mix of letters, numbers & symbols
                    </p>
                  </div>
                </div>
                {/* Referral Code (Optional) */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="referral_user_code"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Referral Code{" "}
                    <span className="text-xs">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="referral_user_code"
                    name="referral_user_code"
                    value={formData.referral_user_code}
                    onChange={handleInputChange}
                    placeholder="Enter referral code if you have one"
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter a referral code to get special benefits
                  </p>
                </div>
              </div>
            </div>
            {/* Document Upload Section */}
            <div className="mb-10">
              <h2 className="text-xl font-bold text-white mb-6 pb-2 border-b border-gray-700">
                Document Upload
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Aadhaar Document Upload */}
                <div className="col-span-1 flex flex-col">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    National Id *
                  </label>
                  <input
                    type="file"
                    ref={aadhaarInputRef}
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, "aadhaar")}
                  />
                  <div className="flex-1">
                    {!aadhaarPreview ? (
                      <div
                        onClick={() => triggerFileInput("aadhaar")}
                        className="border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-[#242938] transition-colors h-48"
                      >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-1">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-400">
                        PDF, JPG, JPEG or PNG (Max 5MB)
                      </p>
                      </div>
                    ) : (
                      <div className="relative border rounded-lg overflow-hidden h-48">
                      <div className="absolute top-2 right-2 z-10">
                        <button
                          type="button"
                          onClick={() => {
                            setAadhaarFile(null);
                            setAadhaarPreview(null);
                          }}
                          className="bg-red-100 text-red-600 p-1 rounded-full hover:bg-red-200 transition-colors !rounded-button whitespace-nowrap"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                      {aadhaarFile?.type.includes("image") ? (
                        <img
                          src={aadhaarPreview}
                          alt="Aadhaar Preview"
                          className="w-full h-full object-cover object-top"
                        />
                      ) : (
                        <div className="w-full h-full bg-[#242938] flex items-center justify-center">
                          <div className="text-center">
                            <FileText className="w-8 h-8 text-red-500 mb-2" />
                            <p className="text-sm text-gray-300">
                              {aadhaarFile?.name}
                            </p>
                          </div>
                        </div>
                      )}
                      </div>
                    )}
                  </div>
                </div>
                {/* Passport Document Upload */}
                <div className="col-span-1 flex flex-col">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Passport Document *
                  </label>
                  <input
                    type="file"
                    ref={passportInputRef}
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, "passport")}
                  />
                  <div className="flex-1">
                    {!passportPreview ? (
                      <div
                        onClick={() => triggerFileInput("passport")}
                        className="border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-[#242938] transition-colors h-48"
                      >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-1">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-400">
                        PDF, JPG, JPEG or PNG (Max 5MB)
                      </p>
                      </div>
                    ) : (
                      <div className="relative border rounded-lg overflow-hidden h-48">
                      <div className="absolute top-2 right-2 z-10">
                        <button
                          type="button"
                          onClick={() => {
                            setPassportFile(null);
                            setPassportPreview(null);
                          }}
                          className="bg-red-100 text-red-600 p-1 rounded-full hover:bg-red-200 transition-colors !rounded-button whitespace-nowrap"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                      {passportFile?.type.includes("image") ? (
                        <img
                          src={passportPreview}
                          alt="Passport Preview"
                          className="w-full h-full object-cover object-top"
                        />
                      ) : (
                        <div className="w-full h-full bg-[#242938] flex items-center justify-center">
                          <div className="text-center">
                            <FileText className="w-8 h-8 text-red-500 mb-2" />
                            <p className="text-sm text-gray-300">
                              {passportFile?.name}
                            </p>
                          </div>
                        </div>
                      )}
                      </div>
                    )}
                  </div>
                </div>
                {/* Profile Picture Upload */}
                <div className="col-span-1 flex flex-col">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Profile Picture
                  </label>
                  <input
                    type="file"
                    ref={profilePictureInputRef}
                    className="hidden"
                    accept=".jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, "profile")}
                  />
                  <div className="flex-1">
                    {!profilePicturePreview ? (
                      <div
                        onClick={() => triggerFileInput("profile")}
                        className="border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-[#242938] transition-colors h-48"
                      >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-1">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-400">
                        JPG, JPEG or PNG (Max 5MB)
                      </p>
                      </div>
                    ) : (
                      <div className="relative border rounded-lg overflow-hidden h-48">
                      <div className="absolute top-2 right-2 z-10">
                        <button
                          type="button"
                          onClick={() => {
                            setProfilePictureFile(null);
                            setProfilePicturePreview(null);
                          }}
                          className="bg-red-100 text-red-600 p-1 rounded-full hover:bg-red-200 transition-colors !rounded-button whitespace-nowrap"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                      <img
                        src={profilePicturePreview}
                        alt="Profile Preview"
                        className="w-full h-full object-cover object-center"
                      />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* Terms and Submission */}
            <div className="mb-8">
              <div className="space-y-4">
                {/* Terms Checkbox */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="terms"
                      name="terms"
                      type="checkbox"
                      checked={termsAccepted}
                      onChange={() => setTermsAccepted(!termsAccepted)}
                      className="h-4 w-4 text-blue-600 bg-[#1A1F2E] border-gray-600 rounded focus:ring-blue-500 focus:ring-offset-0"
                      required
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label
                      htmlFor="terms"
                      className="font-medium text-gray-300"
                    >
                      I agree to the{" "}
                      <button
                        type="button"
                        onClick={() => setShowTermsModal(true)}
                        className="text-blue-400 hover:text-blue-300 hover:underline bg-transparent border-none p-0 cursor-pointer"
                      >
                        Terms and Conditions
                      </button>{" "}
                      *
                    </label>
                  </div>
                </div>
                {/* Privacy Policy Checkbox */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="privacy"
                      name="privacy"
                      type="checkbox"
                      checked={privacyAccepted}
                      onChange={() => setPrivacyAccepted(!privacyAccepted)}
                      className="h-4 w-4 text-blue-600 bg-[#1A1F2E] border-gray-600 rounded focus:ring-blue-500 focus:ring-offset-0"
                      required
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label
                      htmlFor="privacy"
                      className="font-medium text-gray-300"
                    >
                      I agree to the{" "}
                      <button
                        type="button"
                        onClick={() => setShowPrivacyModal(true)}
                        className="text-blue-400 hover:text-blue-300 hover:underline bg-transparent border-none p-0 cursor-pointer"
                      >
                        Privacy Policy
                      </button>{" "}
                      *
                    </label>
                  </div>
                </div>
              </div>
            </div>
            {/* Submit Button */}
            <div className="flex flex-col items-center">
              <button
                type="submit"
                disabled={isSubmitting || !termsAccepted || !privacyAccepted}
                className={`w-full sm:w-auto px-8 py-3 text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${
                  isSubmitting || !termsAccepted || !privacyAccepted
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                } !rounded-button whitespace-nowrap cursor-pointer`}
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <Loader className="w-5 h-5 mr-2 animate-spin" /> Creating
                    Account...
                  </span>
                ) : (
                  "Create Account"
                )}
              </button>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-400">
                  Already have an account?{" "}
                  <a
                    href="/"
                    className="text-blue-400 hover:text-blue-300 hover:underline font-medium"
                  >
                    Sign in
                  </a>
                </p>
              </div>
            </div>
          </form>
        </div>
        {/* Help Button */}
        <div className="fixed bottom-6 right-6">
          <button
            type="button"
            className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white p-4 rounded-full shadow-lg hover:from-[#3A5FEF] hover:to-[#5C7FEF] transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 focus:ring-offset-[#0D1117]"
            title="Need help?"
          >
            <HelpCircle className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Terms and Conditions Modal */}
      <TermsModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
};
export default Signup;

