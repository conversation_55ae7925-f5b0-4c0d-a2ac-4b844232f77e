import React from 'react';
import Modal from '../ui/Modal';

interface TermsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TermsModal: React.FC<TermsModalProps> = ({ isOpen, onClose }) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Terms and Conditions"
      maxWidth="2xl"
    >
      <div className="max-h-96 overflow-y-auto text-gray-300 space-y-4 custom-scrollbar">
        <div>
          <h4 className="text-lg font-semibold text-white mb-2">1. Acceptance of Terms</h4>
          <p className="text-sm leading-relaxed">
            By accessing and using this platform, you accept and agree to be bound by the terms
            and provision of this agreement. If you do not agree to abide by the above, please
            do not use this service.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">2. Use License</h4>
          <p className="text-sm leading-relaxed">
            Permission is granted to temporarily download one copy of the materials on our
            platform for personal, non-commercial transitory viewing only. This is the grant
            of a license, not a transfer of title, and under this license you may not:
          </p>
          <ul className="list-disc list-inside mt-2 text-sm space-y-1 ml-4">
            <li>modify or copy the materials</li>
            <li>use the materials for any commercial purpose or for any public display</li>
            <li>attempt to reverse engineer any software contained on the platform</li>
            <li>remove any copyright or other proprietary notations from the materials</li>
          </ul>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">3. Account Registration</h4>
          <p className="text-sm leading-relaxed">
            To access certain features of our platform, you must register for an account.
            You agree to provide accurate, current, and complete information during the
            registration process and to update such information to keep it accurate, current,
            and complete.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">4. User Responsibilities</h4>
          <p className="text-sm leading-relaxed">
            You are responsible for safeguarding the password and for maintaining the
            confidentiality of your account. You agree not to disclose your password to
            any third party and to take sole responsibility for activities that occur under
            your account.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">5. Prohibited Uses</h4>
          <p className="text-sm leading-relaxed">
            You may not use our platform for any unlawful purpose or to solicit others to
            perform unlawful acts. You may not transmit any worms or viruses or any code
            of a destructive nature.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">6. Disclaimer</h4>
          <p className="text-sm leading-relaxed">
            The information on this platform is provided on an 'as is' basis. To the fullest
            extent permitted by law, this Company excludes all representations, warranties,
            conditions and terms relating to our platform and the use of this platform.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">7. Limitations</h4>
          <p className="text-sm leading-relaxed">
            In no event shall our Company or its suppliers be liable for any damages
            (including, without limitation, damages for loss of data or profit, or due to
            business interruption) arising out of the use or inability to use the materials
            on our platform.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">8. Revisions</h4>
          <p className="text-sm leading-relaxed">
            The materials appearing on our platform could include technical, typographical,
            or photographic errors. We do not warrant that any of the materials on its
            platform are accurate, complete, or current. We may make changes to the materials
            contained on its platform at any time without notice.
          </p>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-white mb-2">9. Governing Law</h4>
          <p className="text-sm leading-relaxed">
            These terms and conditions are governed by and construed in accordance with the
            laws and you irrevocably submit to the exclusive jurisdiction of the courts in
            that state or location.
          </p>
        </div>

        <div className="pt-4 border-t border-gray-700">
          <p className="text-xs text-gray-400">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default TermsModal;
