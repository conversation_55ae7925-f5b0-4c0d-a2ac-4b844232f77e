export interface WithdrawalList {
  total_pages: number;
  current_page: number;
  items: Withdrawal[];
}

export interface Withdrawal {
  wallet_address: string;
  amount: number;
  network: string;
  status: string;
  id: number;
  user_id: number;
  reviewed_by_admin_id: number;
  approved_at: string;
  created_at: string;
  updated_at: string;
}

export interface CreateWithdrawalRequest {
  wallet_address: string;
  amount: number;
  network: string;
}