// User Withdrawal Models and Interfaces

// Withdrawal status types
export type WithdrawalStatus = 'Pending' | 'Completed' | 'Failed' | 'Processing' | 'Rejected';

// Network types for withdrawals - only TRC20 supported
export type NetworkType = 'trc20';

// Network configuration interface
export interface NetworkOption {
  value: string; // API expects string
  label: string;
  fee: number;
}

// Individual withdrawal record interface - matches API response
export interface Withdrawal {
  id: number;
  wallet_address: string;
  amount: number;
  network: string;
  status: WithdrawalStatus;
  transaction_hash?: string;
  created_at: string;
  updated_at: string;
}

// Withdrawal list response interface
export interface WithdrawalList {
  total: number;
  total_pages: number;
  current_page: number;
  items: Withdrawal[];
}

// Create withdrawal request interface - EXACT API specification match
export interface CreateWithdrawalRequest {
  wallet_address: string;
  amount: number;
  network: string;
}

// API Response interface for withdrawal creation (direct from API)
export interface WithdrawalApiResponse {
  id: number;
  wallet_address: string;
  amount: number;
  network: string;
  status: WithdrawalStatus;
  created_at: string;
  updated_at: string;
  transaction_hash?: string;
}

// Create withdrawal response interface (service layer wrapper)
export interface CreateWithdrawalResponse {
  success: boolean;
  message: string;
  data?: WithdrawalApiResponse;
  error?: string;
}

// User balance interface
export interface UserBalance {
  current_balance: number;
  available_balance: number;
  pending_withdrawals: number;
  total_withdrawn: number;
  daily_withdrawal_limit: number;
  remaining_daily_limit: number;
}

// Withdrawal summary interface
export interface WithdrawalSummary {
  total_withdrawals: number;
  pending_amount: number;
  completed_amount: number;
  failed_amount: number;
}

// Form validation errors interface
export interface WithdrawalFormErrors {
  amount?: string;
  wallet?: string;
  network?: string;
  twoFactor?: string;
  submit?: string;
  [key: string]: string | undefined;
}