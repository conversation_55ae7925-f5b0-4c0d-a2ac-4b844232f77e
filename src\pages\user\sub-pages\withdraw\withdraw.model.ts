export interface WithdrawalList {
  total_items: number;
  skip: number;
  limit: number;
  current_page: number;
  total_pages: number;
  items: Withdrawal[];
}

export interface Withdrawal {
  user_id: number;
  wallet_address: string;
  amount: number;
  network: string;
  status: string;
  approved_at: string;
  id: number;
  created_at: string;
}

export interface CreateWithdrawalRequest {
  wallet_address: string;
  amount: number;
  network: string;
}