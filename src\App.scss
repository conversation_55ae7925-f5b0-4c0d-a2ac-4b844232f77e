.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


.page-container{
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .content-container{
    height: calc(100vh - 60px);
  }
}


/* Add this to your global CSS file or import it in your component */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.toast-item {
  min-width: 320px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 18px;
  font-weight: bold;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
}

.toast-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  opacity: 0.7;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

@media (max-width: 640px) {
  .toast-container {
    left: 16px;
    right: 16px;
    top: 16px;
  }

  .toast-item {
    min-width: unset;
    width: 100%;
  }
}

/* ===== RESPONSIVE DESIGN SYSTEM ===== */

/* Base responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 2rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile-first responsive improvements */
@media (max-width: 767px) {
  /* Layout adjustments */
  .page-container {
    .content-container {
      height: calc(100vh - 64px);
      padding: 0.75rem;
    }
  }

  /* Typography scaling */
  h1 { font-size: 1.5rem !important; line-height: 2rem !important; }
  h2 { font-size: 1.25rem !important; line-height: 1.75rem !important; }
  h3 { font-size: 1.125rem !important; line-height: 1.5rem !important; }

  /* Touch-friendly interactions */
  button, .cursor-pointer, .clickable {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }

  /* Mobile-optimized spacing */
  .mobile-p-2 { padding: 0.5rem !important; }
  .mobile-p-3 { padding: 0.75rem !important; }
  .mobile-p-4 { padding: 1rem !important; }
  .mobile-m-2 { margin: 0.5rem !important; }
  .mobile-m-3 { margin: 0.75rem !important; }
  .mobile-m-4 { margin: 1rem !important; }

  /* Mobile card layouts */
  .mobile-card {
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* Mobile table alternatives */
  .mobile-table-card {
    background: #1A1F2E;
    border: 1px solid #374151;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
  }

  /* Mobile form improvements */
  .mobile-form-group {
    margin-bottom: 1rem;
  }

  .mobile-input {
    width: 100%;
    padding: 0.875rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #374151;
    background: #131722;
    color: white;
  }

  .mobile-button {
    width: 100%;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    min-height: 48px;
  }

  /* Mobile navigation */
  .mobile-nav-item {
    padding: 0.875rem 1rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.25rem;
  }

  /* Mobile modal adjustments */
  .mobile-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    border-radius: 1rem;
  }

  /* Mobile scrolling */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Hide desktop elements */
  .desktop-only {
    display: none !important;
  }

  /* Show mobile elements */
  .mobile-only {
    display: block !important;
  }
}

/* Tablet responsive improvements */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Tablet-specific layouts */
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .tablet-grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  /* Tablet spacing */
  .tablet-p-4 { padding: 1rem !important; }
  .tablet-p-6 { padding: 1.5rem !important; }

  /* Tablet typography */
  .tablet-text-lg { font-size: 1.125rem !important; }
  .tablet-text-xl { font-size: 1.25rem !important; }

  /* Hide mobile-only elements */
  .mobile-only {
    display: none !important;
  }

  /* Show tablet elements */
  .tablet-only {
    display: block !important;
  }
}

/* Desktop improvements */
@media (min-width: 1024px) {
  /* Hide mobile and tablet only elements */
  .mobile-only,
  .tablet-only {
    display: none !important;
  }

  /* Show desktop elements */
  .desktop-only {
    display: block !important;
  }

  /* Desktop-specific enhancements */
  .desktop-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }
}

/* Universal responsive utilities */
.responsive-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

@media (min-width: 768px) {
  .responsive-text {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-text {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* Responsive images and media */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.responsive-video {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
}

/* Responsive charts and graphs */
.responsive-chart {
  width: 100%;
  height: 200px;
}

@media (min-width: 768px) {
  .responsive-chart {
    height: 256px;
  }
}

@media (min-width: 1024px) {
  .responsive-chart {
    height: 320px;
  }
}

/* Accessibility improvements */
button:focus,
input:focus,
select:focus,
textarea:focus,
[role="button"]:focus {
  outline: 2px solid #4A6FFF;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(74, 111, 255, 0.1);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-gray-300 {
    color: #ffffff !important;
  }

  .text-gray-400 {
    color: #e5e7eb !important;
  }

  .border-gray-700 {
    border-color: #ffffff !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .text-gray-300 {
    color: #d1d5db;
  }

  .text-gray-400 {
    color: #9ca3af;
  }

  .bg-white {
    background-color: #1a1f2e !important;
  }

  .text-black {
    color: #ffffff !important;
  }
}