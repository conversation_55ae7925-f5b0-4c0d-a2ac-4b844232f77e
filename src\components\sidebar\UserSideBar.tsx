import { LayoutDashboard, Wallet , ArrowRight, Users, LineChart, Package, X } from 'lucide-react';
import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom';

interface UserSideBarProps {
    isMobileMenuOpen?: boolean;
    setIsMobileMenuOpen?: (open: boolean) => void;
}

function UserSideBar({ isMobileMenuOpen = false, setIsMobileMenuOpen }: UserSideBarProps) {
    const navigate = useNavigate();
    const location = useLocation();
    const [activeTab, setActiveTab] = useState("dashboard");

    const menuItems = [
        { id: "dashboard", label: "Dashboard", icon: LayoutDashboard },
        { id: "deposit", label: "Deposit", icon: Wallet },
        { id: "withdraw", label: "Withdraw", icon: ArrowRight },
        { id: "referrals", label: "Referrals", icon: Users },
        { id: "earnings", label: "Earnings", icon: Line<PERSON>hart },
        { id: "products", label: "Products", icon: Package },
    ];

    // Extract active tab from current route
    useEffect(() => {
        const path = location.pathname;
        if (path.startsWith('/user/')) {
            const tabFromPath = path.replace('/user/', '').split('/')[0];
            if (tabFromPath) {
                setActiveTab(tabFromPath);
            } else {
                setActiveTab('dashboard');
            }
        }
    }, [location.pathname]);

    const handleTabChange = (tabId:string) => {
        navigate(`/user/${tabId}`);
        // Close mobile menu after navigation
        if (setIsMobileMenuOpen) {
            setIsMobileMenuOpen(false);
        }
    };

    // Close mobile menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const sidebar = document.getElementById('mobile-sidebar');
            const menuButton = document.getElementById('mobile-menu-button');

            if (isMobileMenuOpen && sidebar && !sidebar.contains(event.target as Node) &&
                menuButton && !menuButton.contains(event.target as Node)) {
                if (setIsMobileMenuOpen) {
                    setIsMobileMenuOpen(false);
                }
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isMobileMenuOpen, setIsMobileMenuOpen]);

    return (
        <>
            {/* Desktop Sidebar */}
            <aside className="w-64 bg-[#1A1F2E] border-r border-gray-700 fixed left-0 top-16 bottom-0 hidden md:block overflow-y-auto z-10">
            <nav className="mt-6 px-4">
                <div className="space-y-2">
                    {menuItems.map((item) => {
                        const IconComponent = item.icon;
                        return (
                            <button
                                key={item.id}
                                onClick={() => handleTabChange(item.id)}
                                className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out group desktop-hover ${
                                    activeTab === item.id
                                        ? "bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white shadow-lg shadow-blue-500/20"
                                        : "text-gray-300 hover:bg-[#131722] hover:text-white hover:shadow-md"
                                }`}
                            >
                                <IconComponent
                                    className={`w-5 h-5 mr-3 transition-all duration-200 ${
                                        activeTab === item.id
                                            ? "text-white"
                                            : "text-gray-400 group-hover:text-blue-400"
                                    }`}
                                />
                                <span className="transition-all duration-200">{item.label}</span>
                            </button>
                        );
                    })}
                </div>

                <div className="mt-10">
                    <div className="bg-gradient-to-br from-[#131722] to-[#0D1117] rounded-xl p-4 border border-gray-700 shadow-lg">
                        <h3 className="text-sm font-semibold text-white mb-2">
                            Need Help?
                        </h3>
                        <p className="text-xs text-gray-300 mb-4 leading-relaxed">
                            Contact our support team for assistance with your account.
                        </p>
                        <div className="space-y-3">
                            <div className="flex items-center text-gray-300 group">
                                <div className="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center mr-3 group-hover:bg-blue-500/20 transition-colors duration-200">
                                    <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                </div>
                                <a
                                    href="mailto:<EMAIL>"
                                    className="text-sm hover:text-blue-400 transition-colors duration-200 font-medium"
                                >
                                    <EMAIL>
                                </a>
                            </div>
                            <div className="flex items-center text-gray-300 group">
                                <div className="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center mr-3 group-hover:bg-blue-500/20 transition-colors duration-200">
                                    <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.51 3.515"/>
                                    </svg>
                                </div>
                                <a
                                    href="https://wa.me/12345678900"
                                    className="text-sm hover:text-blue-400 transition-colors duration-200 font-medium"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    +****************
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

            {/* Mobile Sidebar Overlay */}
            {isMobileMenuOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                    onClick={() => setIsMobileMenuOpen && setIsMobileMenuOpen(false)}
                />
            )}

            {/* Mobile Sidebar */}
            <aside
                id="mobile-sidebar"
                className={`fixed left-0 top-0 bottom-0 w-72 sm:w-80 bg-[#1A1F2E] border-r border-gray-700 transform transition-transform duration-300 ease-in-out z-50 md:hidden ${
                    isMobileMenuOpen ? 'translate-x-0 animate-slide-in-right' : '-translate-x-full animate-slide-out-right'
                } overflow-y-auto mobile-scroll`}
            >
                {/* Mobile Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-700 min-h-[64px]">
                    <div className="text-white font-bold text-lg sm:text-xl flex items-center min-w-0">
                        <div className="h-7 w-7 sm:h-8 sm:w-8 bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                            <Package className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                        </div>
                        <span className="truncate">Fruit-O-Cart</span>
                    </div>
                    <button
                        onClick={() => setIsMobileMenuOpen && setIsMobileMenuOpen(false)}
                        className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg min-h-touch-target"
                        aria-label="Close menu"
                    >
                        <X className="w-5 h-5 sm:w-6 sm:h-6" />
                    </button>
                </div>

                {/* Mobile Navigation */}
                <nav className="mt-6 px-4 pb-6">
                    <div className="space-y-2">
                        {menuItems.map((item) => {
                            const IconComponent = item.icon;
                            return (
                                <button
                                    key={item.id}
                                    onClick={() => handleTabChange(item.id)}
                                    className={`mobile-nav-item w-full flex items-center px-4 py-4 text-base font-medium rounded-lg transition-all duration-200 ease-in-out group min-h-touch-target ${
                                        activeTab === item.id
                                            ? "bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white shadow-lg shadow-blue-500/20"
                                            : "text-gray-300 hover:bg-[#131722] hover:text-white hover:shadow-md"
                                    }`}
                                >
                                    <IconComponent
                                        className={`w-5 h-5 mr-4 transition-all duration-200 flex-shrink-0 ${
                                            activeTab === item.id
                                                ? "text-white"
                                                : "text-gray-400 group-hover:text-blue-400"
                                        }`}
                                    />
                                    <span className="transition-all duration-200 text-left">{item.label}</span>
                                </button>
                            );
                        })}
                    </div>

                    {/* Mobile Help Section */}
                    <div className="mt-10">
                        <div className="bg-gradient-to-br from-[#131722] to-[#0D1117] rounded-xl p-4 border border-gray-700 shadow-lg">
                            <h3 className="text-sm font-semibold text-white mb-2">
                                Need Help?
                            </h3>
                            <p className="text-xs text-gray-300 mb-4 leading-relaxed">
                                Contact our support team for assistance with your account.
                            </p>
                            <div className="space-y-3">
                                <div className="flex items-center text-gray-300 group">
                                    <div className="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center mr-3 group-hover:bg-blue-500/20 transition-colors duration-200">
                                        <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                        </svg>
                                    </div>
                                    <a
                                        href="mailto:<EMAIL>"
                                        className="text-sm hover:text-blue-400 transition-colors duration-200 font-medium"
                                    >
                                        <EMAIL>
                                    </a>
                                </div>
                                <div className="flex items-center text-gray-300 group">
                                    <div className="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center mr-3 group-hover:bg-blue-500/20 transition-colors duration-200">
                                        <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.51 3.515"/>
                                        </svg>
                                    </div>
                                    <a
                                        href="https://wa.me/12345678900"
                                        className="text-sm hover:text-blue-400 transition-colors duration-200 font-medium"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        +****************
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </aside>
        </>
    )
}

export default UserSideBar