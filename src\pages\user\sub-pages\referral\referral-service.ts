import api from "../../../../api/axiosInstance";
import { ReferralTreeResponse, ReferralStats, ReferralList } from "./referral.model";

export const getUserReferrals = async (user_id: number): Promise<ReferralTreeResponse> => {
  try {
    const response = await api.get(`/users/${user_id}/referrals`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user referrals:', error);
    throw error;
  }
};

export const getReferralStats = async (): Promise<ReferralStats> => {
  try {
    const response = await api.get('/referrals/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching referral stats:', error);
    throw error;
  }
};

export const getReferralList = async (  
  skip: number,
  limit: number,
  search: string = '',
  status?: string
): Promise<ReferralList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(`/referrals/list?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching referral list:', error);
    throw error;
  }
};