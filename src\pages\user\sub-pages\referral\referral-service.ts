import api from "../../../../api/axiosInstance";
import { ReferralTreeResponse, ReferralStats, ReferralList } from "./referral.model";

export const getUserReferrals = async (user_id: number): Promise<ReferralTreeResponse> => {
  try {
    console.log('Making API call to:', `/users/${user_id}/referrals`);
    const response = await api.get(`/users/${user_id}/referrals`);
    console.log('Raw API response:', response);
    console.log('Response data:', response.data);

    // Handle different possible response structures
    if (response.data && typeof response.data === 'object') {
      // If the response has a referrals property, use it
      if (response.data.referrals) {
        return response.data;
      }
      // If the response is directly an array, wrap it
      if (Array.isArray(response.data)) {
        return {
          user_id: user_id,
          referrals: response.data
        };
      }
      // If the response has other structure, try to extract referrals
      if (response.data.data) {
        return {
          user_id: user_id,
          referrals: Array.isArray(response.data.data) ? response.data.data : []
        };
      }
    }

    // Fallback: return empty structure
    return {
      user_id: user_id,
      referrals: []
    };
  } catch (error: any) {
    console.error('Error fetching user referrals:', error);
    console.error('Error details:', error?.response?.data);
    // Return empty structure instead of throwing
    return {
      user_id: user_id,
      referrals: []
    };
  }
};

export const getReferralStats = async (): Promise<ReferralStats> => {
  try {
    const response = await api.get('/referrals/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching referral stats:', error);
    throw error;
  }
};

export const getReferralList = async (
  skip: number,
  limit: number,
  search: string = '',
  status?: string
): Promise<ReferralList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(`/referrals/list?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching referral list:', error);
    throw error;
  }
};