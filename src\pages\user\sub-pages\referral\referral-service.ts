import api from "../../../../api/axiosInstance";
import { create_deposit, deposit_list } from "./deposit.model";

export const getUserDepositList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<deposit_list> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(
      `deposits/?${queryParams.toString()}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const createDeposite = async (body:create_deposit) => {
  try {
    const response = await api.post("/deposits/",body);
    return response.data;
  } catch (error) {
    throw error;
  }
}