import api from "../../../../api/axiosInstance";
import { ReferralTreeResponse, ReferralStats, ReferralList } from "./referral.model";

export const getUserReferrals = async (user_id: number): Promise<ReferralTreeResponse> => {
  try {
    console.log('Making API call to:', `/users/${user_id}/referrals`);
    const response = await api.get(`/users/${user_id}/referrals`);
    console.log('Raw API response:', response);
    console.log('Response data:', response.data);
    console.log('Response status:', response.status);

    // Based on the API documentation, the response structure is:
    // {
    //   "additionalProp1": {}
    // }

    if (response.data && typeof response.data === 'object') {
      console.log('Processing API response data...');

      // Return the raw response data as it contains dynamic property names
      return response.data;
    }

    // Fallback: return empty structure
    console.log('No valid response data, returning empty structure');
    return {};
  } catch (error: any) {
    console.error('Error fetching user referrals:', error);
    console.error('Error details:', error?.response?.data);
    console.error('Error status:', error?.response?.status);

    // Return empty structure instead of throwing
    return {};
  }
};

export const getReferralStats = async (): Promise<ReferralStats> => {
  try {
    const response = await api.get('/referrals/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching referral stats:', error);
    throw error;
  }
};

export const getReferralList = async (
  skip: number,
  limit: number,
  search: string = '',
  status?: string
): Promise<ReferralList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }

    const response = await api.get(`/referrals/list?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching referral list:', error);
    throw error;
  }
};