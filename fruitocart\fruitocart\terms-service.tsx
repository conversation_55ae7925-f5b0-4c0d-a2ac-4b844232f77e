// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.

import React from "react";
import { ArrowLeft } from "lucide-react";

const App: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#0D1117] text-white flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-[#1A1F2E] border-b border-gray-800 px-6 py-4 flex items-center justify-between">
        <a
          href="https://readdy.ai/home/<USER>/f523cd6e-f72d-47c1-b177-0fe54feda32d"
          data-readdy="true"
          className="flex items-center text-gray-300 hover:text-white transition-colors cursor-pointer"
        >
          <ArrowLeft className="mr-2" size={20} />
          <span>Back</span>
        </a>
        <h1 className="text-xl font-bold">Terms of Service</h1>
        <div className="text-sm text-gray-400">Last updated: May 3, 2025</div>
      </header>

      {/* Main Content */}
      <main className="flex-1 container mx-auto max-w-4xl px-6 py-8 mb-20">
        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            1. Introduction
          </h2>
          <p className="text-gray-300 mb-4">
            Welcome to Fruit-O-Cart. These Terms of Service ("Terms") govern
            your access to and use of our website, products, and services
            ("Services"). By accessing or using our Services, you agree to be
            bound by these Terms. If you do not agree to these Terms, please do
            not use our Services.
          </p>
          <p className="text-gray-300 mb-4">
            We may modify these Terms at any time. If we do so, we will notify
            you by publishing the modified Terms on this site. Your continued
            use of the Services after we publish the modified Terms constitutes
            your agreement to the modified Terms.
          </p>
        </section>

        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            2. User Agreements
          </h2>
          <p className="text-gray-300 mb-4">
            By creating an account and using our Services, you represent and
            warrant that:
          </p>
          <ul className="list-disc ml-6 mb-4 text-gray-300 space-y-2">
            <li>You are at least 18 years of age.</li>
            <li>
              You have the right, authority, and capacity to enter into these
              Terms and to abide by all of the terms and conditions set forth
              herein.
            </li>
            <li>
              You will not use the Services for any purpose that is unlawful or
              prohibited by these Terms.
            </li>
            <li>
              All information you provide to us is true, accurate, complete, and
              current, and you will maintain and update such information to keep
              it true, accurate, complete, and current.
            </li>
          </ul>
          <p className="text-gray-300 mb-4">
            You are responsible for safeguarding your password and for all
            activities that occur under your account. You agree to notify us
            immediately of any unauthorized use of your account.
          </p>
        </section>

        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            3. Acceptable Use Policy
          </h2>
          <p className="text-gray-300 mb-4">
            When using our Services, you agree not to:
          </p>
          <ul className="list-disc ml-6 mb-4 text-gray-300 space-y-2">
            <li>Violate any applicable laws or regulations.</li>
            <li>
              Infringe upon the rights of others or violate their privacy or
              publicity rights.
            </li>
            <li>
              Use the Services to distribute unsolicited commercial messages
              ("spam").
            </li>
            <li>
              Upload or transmit viruses, malware, or other types of malicious
              software.
            </li>
            <li>
              Attempt to gain unauthorized access to our Services, user
              accounts, or computer systems.
            </li>
            <li>
              Engage in any activity that interferes with or disrupts the
              Services.
            </li>
            <li>
              Reproduce, duplicate, copy, sell, trade, resell or exploit any
              portion of the Services without our express written permission.
            </li>
          </ul>
          <p className="text-gray-300 mb-4">
            We reserve the right to terminate or suspend your access to the
            Services immediately, without prior notice or liability, for any
            reason whatsoever, including without limitation if you breach these
            Terms.
          </p>
        </section>

        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            4. Intellectual Property Rights
          </h2>
          <p className="text-gray-300 mb-4">
            The Services and their original content, features, and functionality
            are and will remain the exclusive property of Fruit-O-Cart and its
            licensors. The Services are protected by copyright, trademark, and
            other laws of both the United States and foreign countries.
          </p>
          <p className="text-gray-300 mb-4">
            Our trademarks and trade dress may not be used in connection with
            any product or service without the prior written consent of
            Fruit-O-Cart.
          </p>
          <p className="text-gray-300 mb-4">
            You retain any and all of your rights to any content you submit,
            post, or display on or through the Services. By submitting, posting,
            or displaying content on or through the Services, you grant us a
            worldwide, non-exclusive, royalty-free license to use, reproduce,
            adapt, publish, translate, and distribute your content in any and
            all media or distribution methods.
          </p>
        </section>

        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            5. Liability Limitations
          </h2>
          <p className="text-gray-300 mb-4">
            To the maximum extent permitted by applicable law, in no event shall
            Fruit-O-Cart, its affiliates, agents, directors, employees,
            suppliers, or licensors be liable for any indirect, punitive,
            incidental, special, consequential, or exemplary damages, including
            without limitation damages for loss of profits, goodwill, use, data,
            or other intangible losses, arising out of or relating to the use
            of, or inability to use, the Services.
          </p>
          <p className="text-gray-300 mb-4">
            To the maximum extent permitted by applicable law, Fruit-O-Cart
            assumes no liability or responsibility for any:
          </p>
          <ul className="list-disc ml-6 mb-4 text-gray-300 space-y-2">
            <li>Errors, mistakes, or inaccuracies of content.</li>
            <li>
              Personal injury or property damage, of any nature whatsoever,
              resulting from your access to or use of our Services.
            </li>
            <li>
              Unauthorized access to or use of our secure servers and/or any and
              all personal information stored therein.
            </li>
            <li>
              Interruption or cessation of transmission to or from the Services.
            </li>
            <li>
              Bugs, viruses, trojan horses, or the like that may be transmitted
              to or through our Services by any third party.
            </li>
            <li>
              Errors or omissions in any content or for any loss or damage
              incurred as a result of the use of any content posted, emailed,
              transmitted, or otherwise made available through the Services.
            </li>
          </ul>
        </section>

        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            6. Termination Policies
          </h2>
          <p className="text-gray-300 mb-4">
            We may terminate or suspend your account and bar access to the
            Services immediately, without prior notice or liability, under our
            sole discretion, for any reason whatsoever and without limitation,
            including but not limited to a breach of the Terms.
          </p>
          <p className="text-gray-300 mb-4">
            If you wish to terminate your account, you may simply discontinue
            using the Services or contact us to request account deletion.
          </p>
          <p className="text-gray-300 mb-4">
            All provisions of the Terms which by their nature should survive
            termination shall survive termination, including, without
            limitation, ownership provisions, warranty disclaimers, indemnity,
            and limitations of liability.
          </p>
        </section>

        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-4 text-blue-500">
            7. Contact Information
          </h2>
          <p className="text-gray-300 mb-4">
            If you have any questions about these Terms, please contact us at:
          </p>
          <div className="bg-[#131722] p-4 rounded-lg">
            <p className="text-gray-300">Fruit-O-Cart, Inc.</p>
            <p className="text-gray-300">1234 Market Street</p>
            <p className="text-gray-300">San Francisco, CA 94103</p>
            <p className="text-gray-300">Email: <EMAIL></p>
            <p className="text-gray-300">Phone: (*************</p>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-[#131722] py-6 px-6 text-center text-sm text-gray-400">
        <p>© 2025 Fruit-O-Cart, Inc. All rights reserved.</p>
      </footer>

      {/* Fixed Back to Login Button */}
      <div className="fixed bottom-6 left-0 right-0 flex justify-center">
        <a
          href="https://readdy.ai/home/<USER>/f523cd6e-f72d-47c1-b177-0fe54feda32d"
          data-readdy="true"
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-button shadow-lg transition-colors duration-200 whitespace-nowrap cursor-pointer"
        >
          Back to Login
        </a>
      </div>
    </div>
  );
};

export default App;
