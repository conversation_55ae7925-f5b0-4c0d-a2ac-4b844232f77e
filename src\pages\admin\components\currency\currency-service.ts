// // Admin Currency Management Service

// import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
// import {
//   CurrencyConversion,
//   CreateCurrencyConversionRequest,
//   UpdateCurrencyConversionRequest,
//   GetCurrencyConversionsResponse,
//   GetCurrencyConversionResponse,
//   CreateCurrencyConversionResponse,
//   UpdateCurrencyConversionResponse,
//   DeleteCurrencyConversionResponse,
//   CurrencyConversionFilters,
//   CurrencyStatistics,
//   CurrencyRateUpdate,
//   BulkCurrencyUpdateRequest,
//   ExternalExchangeRate,
//   SupportedCurrency
// } from './currency.model';

// class AdminCurrencyService {
//   private readonly baseUrl = '/currency_conversions';

//   /**
//    * Get all currency conversions with pagination and filters
//    */
//   async getCurrencyConversions(filters?: CurrencyConversionFilters): Promise<GetCurrencyConversionsResponse> {
//     try {
//       const params = new URLSearchParams();
      
//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());
      
//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.from_currency) params.append('from_currency', filters.from_currency);
//       if (filters?.code) params.append('code', filters.code);
//       if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
      
//       const response = await apiMethods.get<GetCurrencyConversionsResponse>(url);
      
//       return {
//         success: true,
//         message: 'Currency conversions retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get currency conversions error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get currency conversion by ID
//    */
//   async getCurrencyConversionById(conversionId: number): Promise<GetCurrencyConversionResponse> {
//     try {
//       const response = await apiMethods.get<GetCurrencyConversionResponse>(`${this.baseUrl}/${conversionId}`);
      
//       return {
//         success: true,
//         message: 'Currency conversion retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get currency conversion error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Currency conversion not found',
//           error: 'Currency conversion not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new currency conversion
//    */
//   async createCurrencyConversion(conversionData: CreateCurrencyConversionRequest): Promise<CreateCurrencyConversionResponse> {
//     try {
//       const response = await apiMethods.post<CreateCurrencyConversionResponse>(this.baseUrl, conversionData);
      
//       return {
//         success: true,
//         message: 'Currency conversion created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create currency conversion error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 409) {
//         return {
//           success: false,
//           message: 'Currency conversion already exists for this pair',
//           error: 'Duplicate currency pair'
//         };
//       }
      
//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update currency conversion
//    */
//   async updateCurrencyConversion(conversionId: number, conversionData: UpdateCurrencyConversionRequest): Promise<UpdateCurrencyConversionResponse> {
//     try {
//       const response = await apiMethods.put<UpdateCurrencyConversionResponse>(`${this.baseUrl}/${conversionId}`, conversionData);
      
//       return {
//         success: true,
//         message: 'Currency conversion updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update currency conversion error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Currency conversion not found',
//           error: 'Currency conversion not found'
//         };
//       }
      
//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete currency conversion
//    */
//   async deleteCurrencyConversion(conversionId: number): Promise<DeleteCurrencyConversionResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteCurrencyConversionResponse>(`${this.baseUrl}/${conversionId}`);
      
//       return {
//         success: true,
//         message: 'Currency conversion deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete currency conversion error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Currency conversion not found',
//           error: 'Currency conversion not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get active currency conversions only
//    */
//   async getActiveCurrencyConversions(filters?: CurrencyConversionFilters): Promise<GetCurrencyConversionsResponse> {
//     return this.getCurrencyConversions({ ...filters, is_active: true });
//   }

//   /**
//    * Get inactive currency conversions only
//    */
//   async getInactiveCurrencyConversions(filters?: CurrencyConversionFilters): Promise<GetCurrencyConversionsResponse> {
//     return this.getCurrencyConversions({ ...filters, is_active: false });
//   }

//   /**
//    * Bulk update currency rates
//    */
//   async bulkUpdateCurrencyRates(updateData: BulkCurrencyUpdateRequest): Promise<{ success: boolean; message: string; error?: string; data?: any }> {
//     try {
//       const response = await apiMethods.post<any>(`${this.baseUrl}/bulk-update`, updateData);
      
//       return {
//         success: true,
//         message: 'Currency rates updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Bulk update currency rates error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get currency conversion by currency pair
//    */
//   async getCurrencyConversionByPair(fromCurrency: string, toCurrency: string): Promise<GetCurrencyConversionResponse> {
//     try {
//       const response = await apiMethods.get<GetCurrencyConversionResponse>(`${this.baseUrl}/pair/${fromCurrency}/${toCurrency}`);
      
//       return {
//         success: true,
//         message: 'Currency conversion retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get currency conversion by pair error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Currency conversion not found for this pair',
//           error: 'Currency pair not found'
//         };
//       }
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Convert amount between currencies
//    */
//   async convertCurrency(fromCurrency: string, toCurrency: string, amount: number): Promise<{ success: boolean; data?: { converted_amount: number; rate: number }; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.post<any>(`${this.baseUrl}/convert`, {
//         from_currency: fromCurrency,
//         to_currency: toCurrency,
//         amount
//       });
      
//       return {
//         success: true,
//         message: 'Currency converted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Convert currency error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get paginated currency conversions (helper method)
//    */
//   async getPaginatedCurrencyConversions(page: number = 1, limit: number = 10, filters?: Omit<CurrencyConversionFilters, 'skip' | 'limit'>): Promise<GetCurrencyConversionsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getCurrencyConversions({ ...filters, skip, limit });
//   }

//   /**
//    * Search currency conversions
//    */
//   async searchCurrencyConversions(query: string, filters?: CurrencyConversionFilters): Promise<GetCurrencyConversionsResponse> {
//     return this.getCurrencyConversions({ ...filters, search: query });
//   }

//   /**
//    * Get currency statistics
//    */
//   async getCurrencyStatistics(): Promise<{ success: boolean; data?: CurrencyStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: CurrencyStatistics }>(`${this.baseUrl}/statistics`);
      
//       return {
//         success: true,
//         message: 'Currency statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get currency statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get supported currencies
//    */
//   async getSupportedCurrencies(): Promise<{ success: boolean; data?: SupportedCurrency[]; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: SupportedCurrency[] }>(`${this.baseUrl}/supported`);
      
//       return {
//         success: true,
//         message: 'Supported currencies retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get supported currencies error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Sync rates from external API
//    */
//   async syncExternalRates(): Promise<{ success: boolean; data?: ExternalExchangeRate[]; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.post<{ data: ExternalExchangeRate[] }>(`${this.baseUrl}/sync-external`);
      
//       return {
//         success: true,
//         message: 'External rates synced successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Sync external rates error:', error);
//       const errorInfo = apiUtils.handleApiError(error);
      
//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }
// }

// // Export singleton instance
// export const adminCurrencyService = new AdminCurrencyService();
// export default adminCurrencyService;
export {};
