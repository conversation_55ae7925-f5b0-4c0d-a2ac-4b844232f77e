// Color Variables for Fruit-O-Cart Application
// Based on the design system from utils/designSystem.ts

// Primary Colors
$primary_color: #4A6FFF;
$primary_color_hover: #3B5FEF;
$secondary_color: #6C8FFF;

// Background Colors
$primary_background: #0D1117;
$secondary_background: #1A1F2E;
$tertiary_background: #131722;

// Text Colors
$text_primary: #FFFFFF;
$text_secondary: #E5E7EB;
$text_tertiary: #9CA3AF;
$text_muted: #6B7280;

// Black Color Variations
$black_color1: #000000;
$black_color2: #1F2937;
$black_color3: #374151;
$black_color4: #4B5563;
$black_color5: #6B7280;

// Status Colors
$success_color: #10B981;
$warning_color: #F59E0B;
$error_color: #EF4444;
$info_color: #3B82F6;

// Border Colors
$border_color: #374151;
$border_light: #4B5563;
$border_dark: #1F2937;

// Additional Colors
$white_color: #FFFFFF;
$gray_100: #F3F4F6;
$gray_200: #E5E7EB;
$gray_300: #D1D5DB;
$gray_400: #9CA3AF;
$gray_500: #6B7280;
$gray_600: #4B5563;
$gray_700: #374151;
$gray_800: #1F2937;
$gray_900: #111827;

// CSS Custom Properties for dynamic theming
:root {
  --primary-color: #{$primary_color};
  --primary-color-hover: #{$primary_color_hover};
  --secondary-color: #{$secondary_color};
  --primary-background: #{$primary_background};
  --secondary-background: #{$secondary_background};
  --tertiary-background: #{$tertiary_background};
  --text-primary: #{$text_primary};
  --text-secondary: #{$text_secondary};
  --success-color: #{$success_color};
  --warning-color: #{$warning_color};
  --error-color: #{$error_color};
  --info-color: #{$info_color};
  --border-color: #{$border_color};
}

// Utility Classes
.primary-background {
  background-color: $primary_color;
  color: $white_color;
}

.secondary-background {
  background-color: $secondary_color;
  color: $white_color;
}

.text-primary {
  color: $text_primary;
}

.text-secondary {
  color: $text_secondary;
}

.border-primary {
  border-color: $border_color;
}
