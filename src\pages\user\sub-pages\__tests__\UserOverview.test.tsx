import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import UserOverview from '../dashboard/UserOverview';

// Mock the clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(() => Promise.resolve()),
  },
});

// Mock the chart library
jest.mock('echarts', () => ({
  init: jest.fn(() => ({
    setOption: jest.fn(),
    resize: jest.fn(),
    dispose: jest.fn(),
  })),
}));

describe('UserOverview Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('renders dashboard title', () => {
    render(<UserOverview />);
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  test('displays balance cards with correct information', () => {
    render(<UserOverview />);
    
    // Check for balance card
    expect(screen.getByText('Total USDT Balance')).toBeInTheDocument();
    expect(screen.getByText('5,280.42')).toBeInTheDocument();
    expect(screen.getByText('≈ ₹438,274.86 INR')).toBeInTheDocument();
    
    // Check for profit cards
    expect(screen.getByText('Total Profit')).toBeInTheDocument();
    expect(screen.getByText('658.42')).toBeInTheDocument();
    expect(screen.getByText('Today\'s Profit')).toBeInTheDocument();
    expect(screen.getByText('+142.57')).toBeInTheDocument();
  });

  test('displays quick actions section', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Deposit')).toBeInTheDocument();
    expect(screen.getByText('Withdraw')).toBeInTheDocument();
    expect(screen.getByText('Referrals')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
  });

  test('displays membership status section', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Membership Status')).toBeInTheDocument();
    expect(screen.getByText('Bronze Member')).toBeInTheDocument();
    expect(screen.getByText('Referrals Progress')).toBeInTheDocument();
    expect(screen.getByText('24/25')).toBeInTheDocument();
    expect(screen.getByText('Deposit Progress')).toBeInTheDocument();
    expect(screen.getByText('₹20,000/₹25,000')).toBeInTheDocument();
  });

  test('displays referral section with link', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Your Referral Link')).toBeInTheDocument();
    expect(screen.getByText('Total Referrals:')).toBeInTheDocument();
    expect(screen.getByText('24')).toBeInTheDocument();
    expect(screen.getByText('https://trading.com/ref/YOUR_UNIQUE_CODE')).toBeInTheDocument();
  });

  test('copy referral link functionality', async () => {
    render(<UserOverview />);
    
    const copyButton = screen.getByRole('button', { name: /copy/i });
    fireEvent.click(copyButton);
    
    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('https://trading.com/ref/YOUR_UNIQUE_CODE');
    });
  });

  test('displays recent transactions table', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Recent Transactions')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Amount')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Date')).toBeInTheDocument();
    
    // Check for transaction entries
    expect(screen.getByText('Deposit')).toBeInTheDocument();
    expect(screen.getByText('+1,000 USDT')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  test('displays profit chart section', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Profit History')).toBeInTheDocument();
    expect(screen.getByText('Week')).toBeInTheDocument();
    expect(screen.getByText('Month')).toBeInTheDocument();
    expect(screen.getByText('Year')).toBeInTheDocument();
  });

  test('displays referral earnings statistics', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('$1,245.80')).toBeInTheDocument();
    expect(screen.getByText('Total Referral Earnings')).toBeInTheDocument();
    expect(screen.getByText('Total Referrals')).toBeInTheDocument();
    expect(screen.getByText('10%')).toBeInTheDocument();
    expect(screen.getByText('Commission Rate')).toBeInTheDocument();
  });

  test('has proper styling classes applied', () => {
    render(<UserOverview />);
    
    const mainContainer = screen.getByText('Dashboard').closest('div');
    expect(mainContainer).toHaveClass('min-h-screen', 'bg-[#0D1117]', 'text-white');
  });

  test('copy notification appears and disappears', async () => {
    render(<UserOverview />);
    
    const copyButton = screen.getByRole('button', { name: /copy/i });
    fireEvent.click(copyButton);
    
    const notification = document.getElementById('copy-notification');
    expect(notification).toBeInTheDocument();
    expect(notification).toHaveTextContent('Link copied to clipboard!');
  });

  test('membership tier display', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Bronze')).toBeInTheDocument();
    expect(screen.getByText('Silver')).toBeInTheDocument();
    expect(screen.getByText('Gold')).toBeInTheDocument();
    expect(screen.getByText('Platinum')).toBeInTheDocument();
  });

  test('next tier benefits display', () => {
    render(<UserOverview />);
    
    expect(screen.getByText('Next tier benefits:')).toBeInTheDocument();
    expect(screen.getByText('Increased referral commission to 12%')).toBeInTheDocument();
    expect(screen.getByText('Priority customer support')).toBeInTheDocument();
    expect(screen.getByText('Lower trading fees')).toBeInTheDocument();
  });

  test('action buttons are present', () => {
    render(<UserOverview />);
    
    const depositButtons = screen.getAllByText('Deposit');
    const withdrawButtons = screen.getAllByText('Withdraw');
    const shareButton = screen.getByText('Share');
    const viewAllButton = screen.getByText('View All');
    
    expect(depositButtons.length).toBeGreaterThan(0);
    expect(withdrawButtons.length).toBeGreaterThan(0);
    expect(shareButton).toBeInTheDocument();
    expect(viewAllButton).toBeInTheDocument();
  });
});
