// // Admin Deposit Slabs Management Models and Interfaces

// import { ApiResponse } from '../../../../api/axiosInstance';

// // Base pagination interface
// export interface PaginationParams {
//   skip?: number;
//   limit?: number;
// }

// export interface PaginationResponse<T> {
//   total: number;
//   skip: number;
//   limit: number;
//   has_next: boolean;
//   has_prev: boolean;
//   items: T[];
// }

// // Deposit slab interface
// export interface DepositSlab {
//   id: number;
//   name: string;
//   amount: number;
//   is_active: boolean;
//   created_at: string;
//   updated_at?: string;
//   // Additional fields that might be included
//   description?: string;
//   min_amount?: number;
//   max_amount?: number;
//   profit_percentage?: number;
//   duration_days?: number;
//   features?: string[];
//   popularity_rank?: number;
//   total_deposits?: number;
//   active_deposits?: number;
// }

// // Create deposit slab request interface
// export interface CreateDepositSlabRequest {
//   name: string;
//   amount: number;
//   is_active?: boolean;
//   description?: string;
//   min_amount?: number;
//   max_amount?: number;
//   profit_percentage?: number;
//   duration_days?: number;
//   features?: string[];
// }

// // Update deposit slab request interface
// export interface UpdateDepositSlabRequest {
//   name?: string;
//   amount?: number;
//   is_active?: boolean;
//   description?: string;
//   min_amount?: number;
//   max_amount?: number;
//   profit_percentage?: number;
//   duration_days?: number;
//   features?: string[];
//   popularity_rank?: number;
// }

// // API Response interfaces
// export interface GetDepositSlabsResponse extends ApiResponse<PaginationResponse<DepositSlab>> {}
// export interface GetDepositSlabResponse extends ApiResponse<DepositSlab> {}
// export interface CreateDepositSlabResponse extends ApiResponse<DepositSlab> {}
// export interface UpdateDepositSlabResponse extends ApiResponse<DepositSlab> {}
// export interface DeleteDepositSlabResponse extends ApiResponse<{ message: string }> {}

// // Form validation errors
// export interface DepositSlabFormErrors {
//   name?: string;
//   amount?: string;
//   is_active?: string;
//   description?: string;
//   min_amount?: string;
//   max_amount?: string;
//   profit_percentage?: string;
//   duration_days?: string;
//   features?: string;
//   general?: string;
// }

// // Deposit slab filters for search/filtering
// export interface DepositSlabFilters extends PaginationParams {
//   search?: string;
//   is_active?: boolean;
//   amount_min?: number;
//   amount_max?: number;
//   profit_percentage_min?: number;
//   profit_percentage_max?: number;
//   duration_min?: number;
//   duration_max?: number;
//   created_from?: string;
//   created_to?: string;
// }

// // Deposit slab statistics interface
// export interface DepositSlabStatistics {
//   total_slabs: number;
//   active_slabs: number;
//   inactive_slabs: number;
//   total_deposits_across_slabs: number;
//   total_amount_across_slabs: number;
//   most_popular_slab: {
//     id: number;
//     name: string;
//     deposit_count: number;
//   };
//   average_slab_amount: number;
//   slab_performance: Array<{
//     slab_id: number;
//     slab_name: string;
//     total_deposits: number;
//     total_amount: number;
//     active_deposits: number;
//     completion_rate: number;
//   }>;
// }

// // Deposit slab analytics interface
// export interface DepositSlabAnalytics {
//   slab_id: number;
//   slab_name: string;
//   total_deposits: number;
//   total_amount: number;
//   active_deposits: number;
//   completed_deposits: number;
//   average_deposit_amount: number;
//   profit_generated: number;
//   user_retention_rate: number;
//   monthly_growth: number;
//   conversion_rate: number;
// }

// // Bulk slab operations
// export interface BulkSlabUpdateRequest {
//   slab_ids: number[];
//   updates: {
//     is_active?: boolean;
//     profit_percentage?: number;
//     duration_days?: number;
//   };
// }

// // Slab comparison interface
// export interface SlabComparison {
//   slab_a: DepositSlab;
//   slab_b: DepositSlab;
//   comparison_metrics: {
//     deposit_count_difference: number;
//     amount_difference: number;
//     profit_difference: number;
//     popularity_difference: number;
//   };
// }
export {};
