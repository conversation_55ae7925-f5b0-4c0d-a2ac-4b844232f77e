import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { product } from "./product.model";
import { getProductList } from "./product-service";

// Shimmer Loading Component
const ShimmerCard: React.FC = () => (
  <div className="bg-[#1A1D24] border border-[#2A2F3A] rounded-lg shadow-lg overflow-hidden animate-pulse">
    <div className="h-48 bg-[#2A2F3A]"></div>
    <div className="p-6">
      <div className="h-6 bg-[#2A2F3A] rounded mb-2"></div>
      <div className="h-4 bg-[#2A2F3A] rounded mb-2"></div>
      <div className="h-4 bg-[#2A2F3A] rounded mb-4 w-3/4"></div>
      <div className="h-10 bg-[#2A2F3A] rounded w-24"></div>
    </div>
  </div>
);

const Products: React.FC = () => {
  const [products, setProducts] = useState<product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<product | null>(null);
  const [isShimmerLoading, setIsShimmerLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const getList = async () => {
    setIsShimmerLoading(true);
    setError(null);
    try {
      const response = await getProductList(0, 100, "");
      // Filter only active products
      const activeProducts = response.items.filter(product => product.active);
      setProducts(activeProducts);
    } catch (error) {
      console.error("Error fetching products:", error);
      setError("Failed to load products. Please try again later.");
    } finally {
      setIsShimmerLoading(false);
    }
  };

  useEffect(() => {
    getList();
  }, []);

  const handleProductClick = (product: product) => {
    setSelectedProduct(product);
  };

  const closeModal = () => {
    setSelectedProduct(null);
  };

  return (
    <div className="min-h-screen bg-[#0F1419] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Fresh Products
          </h1>
          <p className="text-xl text-[#A1A1AA]">
            Discover our selection of premium fresh products sourced from the finest suppliers
          </p>
        </div>

        {error && (
          <div className="mb-8 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <p className="text-red-400">{error}</p>
            <button
              onClick={getList}
              className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
            >
              Retry
            </button>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {isShimmerLoading ? (
            // Show shimmer cards while loading
            Array.from({ length: 6 }).map((_, index) => (
              <ShimmerCard key={index} />
            ))
          ) : products.length > 0 ? (
            // Show actual products
            products.map((product) => (
              <div
                key={product.id}
                className="bg-[#1A1D24] border border-[#2A2F3A] rounded-lg shadow-lg overflow-hidden hover:shadow-xl hover:shadow-[#4C7BF4]/10 transition-all duration-300 hover:border-[#4C7BF4]/30 group cursor-pointer"
                onClick={() => handleProductClick(product)}
              >
                <div className="h-48 overflow-hidden">
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://via.placeholder.com/400x300/2A2F3A/A1A1AA?text=No+Image';
                    }}
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#4C7BF4] transition-colors duration-200">
                    {product.name}
                  </h3>
                  <p className="text-[#A1A1AA] text-sm line-clamp-2 mb-4 leading-relaxed">
                    {product.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleProductClick(product);
                      }}
                      className="inline-flex items-center px-4 py-2 bg-[#4C7BF4] text-white text-sm font-medium rounded-lg hover:bg-[#3B6DE8] transition-all duration-200 shadow-md hover:shadow-lg hover:shadow-[#4C7BF4]/20"
                    >
                      Read More
                    </button>
                    <span className="text-xs text-[#6B7280]">
                      {new Date(product.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            // Show empty state
            <div className="col-span-full text-center py-12">
              <div className="text-[#A1A1AA] text-lg mb-4">No products found</div>
              <button
                onClick={getList}
                className="px-6 py-3 bg-[#4C7BF4] hover:bg-[#3B6DE8] text-white rounded-lg transition-colors duration-200"
              >
                Refresh
              </button>
            </div>
          )}
        </div>

        {selectedProduct && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-[#1A1D24] border border-[#2A2F3A] rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <div className="relative">
                <img
                  src={selectedProduct.image_url}
                  alt={selectedProduct.name}
                  className="w-full h-64 object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://via.placeholder.com/800x300/2A2F3A/A1A1AA?text=No+Image';
                  }}
                />
                <button
                  onClick={closeModal}
                  className="absolute top-4 right-4 bg-[#2A2F3A] hover:bg-[#4C7BF4] text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:shadow-xl border border-[#3A3F4A] hover:border-[#4C7BF4]"
                >
                  <X className="w-5 h-5" />
                </button>
                <div className="absolute inset-0 bg-gradient-to-t from-[#1A1D24]/80 via-transparent to-transparent pointer-events-none"></div>
              </div>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-2xl font-semibold text-white">
                    {selectedProduct.name}
                  </h3>
                  <span className="px-3 py-1 bg-green-600/20 text-green-400 text-sm rounded-full border border-green-600/30">
                    Active
                  </span>
                </div>
                <p className="text-[#A1A1AA] leading-relaxed text-base mb-4">
                  {selectedProduct.description}
                </p>
                <div className="text-sm text-[#6B7280] mb-6">
                  <span>Created: {new Date(selectedProduct.created_at).toLocaleDateString()}</span>
                  <span className="mx-2">•</span>
                  <span>ID: {selectedProduct.id}</span>
                </div>
                <div className="pt-4 border-t border-[#2A2F3A]">
                  <button
                    onClick={closeModal}
                    className="inline-flex items-center px-6 py-3 bg-[#2A2F3A] hover:bg-[#4C7BF4] text-white font-medium rounded-lg transition-all duration-200 border border-[#3A3F4A] hover:border-[#4C7BF4] hover:shadow-lg"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Products;