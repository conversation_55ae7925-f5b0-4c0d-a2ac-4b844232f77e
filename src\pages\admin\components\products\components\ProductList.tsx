import { Edit, ToggleRight, Trash2, Search } from 'lucide-react'
import React from 'react'
import { product } from '../../../../user/sub-pages/product/product.model'



// Product List Component
interface ProductListProps {
  products: product[]
  loading?: boolean
  searchTerm: string
  onSearchChange: (term: string) => void
  onEdit: (product: product) => void
  onDelete: (productId: number) => void
  onToggleStatus: (productId: number, currentStatus: boolean) => void
  pagination?: {
    currentPage: number
    totalPages: number
    totalItems: number
    onPageChange: (page: number) => void
  }
}

const ProductList: React.FC<ProductListProps> = ({
  products,
  loading = false,
  searchTerm,
  onSearchChange,
  onEdit,
  onDelete,
  onToggleStatus,
  pagination
}) => {
  return (
    <div className="bg-gray-800 rounded-xl shadow-lg border border-gray-700">
      <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
        <h3 className="text-xl font-bold text-white">
          Product List
        </h3>
        <div className="flex space-x-2">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Search products..."
              className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm w-64"
              disabled={loading}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          </div>
        </div>
      </div>
      
      {loading ? (
        <div className="p-6 text-center">
          <div className="text-gray-400">Loading products...</div>
        </div>
      ) : products.length === 0 ? (
        <div className="p-6 text-center">
          <div className="text-gray-400">No products found</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-gray-700 rounded-lg border border-gray-600 overflow-hidden hover:shadow-xl hover:border-gray-500 transition-all duration-300"
            >
              <div className="aspect-w-4 aspect-h-3 relative">
                <div className="h-48 overflow-hidden">
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x200?text=No+Image'
                    }}
                  />
                </div>
                <div className="absolute top-2 right-2">
                  <span
                    className={`px-3 py-1 text-xs font-semibold rounded-full ${
                      product.active 
                        ? "bg-green-900 text-green-300" 
                        : "bg-red-900 text-red-300"
                    }`}
                  >
                    {product.active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h4 className="text-lg font-medium text-white mb-2">
                  {product.name}
                </h4>
                <p className="text-sm text-gray-400 mb-4 line-clamp-3">
                  {product.description}
                </p>
                <div className="flex justify-end space-x-2">
                  <button 
                    onClick={() => onEdit(product)}
                    className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                    title="Edit product"
                  >
                    <Edit className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => onDelete(product.id)}
                    className="text-red-400 hover:text-red-300 transition-colors p-1"
                    title="Delete product"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => onToggleStatus(product.id, product.active)}
                    className="text-gray-400 hover:text-gray-300 transition-colors p-1"
                    title={`${product.active ? 'Deactivate' : 'Activate'} product`}
                  >
                    <ToggleRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {pagination && (
        <div className="px-6 py-4 border-t border-gray-700 flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Showing <span className="font-medium text-gray-300">{((pagination.currentPage - 1) * 10) + 1}</span> to{" "}
            <span className="font-medium text-gray-300">{Math.min(pagination.currentPage * 10, pagination.totalItems)}</span> of{" "}
            <span className="font-medium text-gray-300">{pagination.totalItems}</span> results
          </div>
          <div className="flex space-x-2">
            <button 
              onClick={() => pagination.onPageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage <= 1}
              className="px-4 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button className="px-4 py-2 border border-blue-500 bg-blue-600 text-white rounded-lg text-sm font-medium">
              {pagination.currentPage}
            </button>
            <button 
              onClick={() => pagination.onPageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage >= pagination.totalPages}
              className="px-4 py-2 border border-gray-600 rounded-lg text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProductList