// import { Edit, ToggleRight, Trash2, Search } from 'lucide-react'
// import React from 'react'
// import { product } from '../../../../user/sub-pages/product/product.model'
// import Pagination from '../../../../../components/Pagination/Pagination'



// // Product List Component
// interface ProductListProps {
//   products: product[]
//   loading?: boolean
//   searchTerm: string
//   onSearchChange: (term: string) => void
//   onEdit: (product: product) => void
//   onDelete: (productId: number) => void
//   onToggleStatus: (productId: number, currentStatus: boolean) => void
//   pagination?: {
//     currentPage: number
//     totalPages: number
//     itemsPerPage: number
//     onPageChange: (page: number) => void
//     handleItemsPerChnage : (items: number) => void
//   }
// }

// const ProductList: React.FC<ProductListProps> = ({
//   products,
//   loading = false,
//   searchTerm,
//   onSearchChange,
//   onEdit,
//   onDelete,
//   onToggleStatus,
//   pagination
// }) => {
//   return (
//     <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-2xl border border-gray-700 overflow-hidden">
//       <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-4 flex justify-between items-center">
//         <h3 className="text-xl font-bold text-white flex items-center">
//           <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs font-semibold mr-3">LIST</span>
//           Product List
//         </h3>
//         <div className="flex space-x-2">
//           <div className="relative">
//             <input
//               type="text"
//               value={searchTerm}
//               onChange={(e) => onSearchChange(e.target.value)}
//               placeholder="Search products..."
//               className="pl-10 pr-4 py-2 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-white focus:border-opacity-50 text-white placeholder-gray-300 text-sm w-64 backdrop-blur-sm transition-all duration-200"
//               disabled={loading}
//             />
//             <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 w-4 h-4" />
//           </div>
//         </div>
//       </div>

//       {loading ? (
//         <div className="p-6 text-center">
//           <div className="text-gray-400">Loading products...</div>
//         </div>
//       ) : products.length === 0 ? (
//         <div className="p-6 text-center">
//           <div className="text-gray-400">No products found</div>
//         </div>
//       ) : (
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6 bg-gray-800">
//           {products.map((product) => (
//             <div
//               key={product.id}
//               className="bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl border border-gray-600 overflow-hidden hover:shadow-2xl hover:border-gray-500 hover:scale-105 transition-all duration-300 group"
//             >
//               <div className="aspect-w-4 aspect-h-3 relative">
//                 <div className="h-48 overflow-hidden relative">
//                   <img
//                     src={product.image_url}
//                     alt={product.name}
//                     className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
//                     onError={(e) => {
//                       (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x200?text=No+Image'
//                     }}
//                   />
//                   <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
//                 </div>
//                 <div className="absolute top-3 right-3">
//                   <span
//                     className={`px-3 py-1 text-xs font-bold rounded-full shadow-lg backdrop-blur-sm ${
//                       product.active
//                         ? "bg-green-500 bg-opacity-90 text-white"
//                         : "bg-red-500 bg-opacity-90 text-white"
//                     }`}
//                   >
//                     {product.active ? '● Active' : '● Inactive'}
//                   </span>
//                 </div>
//               </div>
//               <div className="p-4">
//                 <h4 className="text-lg font-medium text-white mb-2">
//                   {product.name}
//                 </h4>
//                 <p className="text-sm text-gray-400 mb-4 line-clamp-3">
//                   {product.description}
//                 </p>
//                 <div className="flex justify-end space-x-2">
//                   <button
//                     onClick={() => onEdit(product)}
//                     className="bg-blue-500 bg-opacity-20 text-blue-400 hover:bg-opacity-30 hover:text-blue-300 transition-all duration-200 p-2 rounded-lg backdrop-blur-sm"
//                     title="Edit product"
//                   >
//                     <Edit className="w-4 h-4" />
//                   </button>
//                   <button
//                     onClick={() => onDelete(product.id)}
//                     className="bg-red-500 bg-opacity-20 text-red-400 hover:bg-opacity-30 hover:text-red-300 transition-all duration-200 p-2 rounded-lg backdrop-blur-sm"
//                     title="Delete product"
//                   >
//                     <Trash2 className="w-4 h-4" />
//                   </button>
//                   <button
//                     onClick={() => onToggleStatus(product.id, product.active)}
//                     className={`${product.active ? 'bg-yellow-500' : 'bg-green-500'} bg-opacity-20 ${product.active ? 'text-yellow-400 hover:text-yellow-300' : 'text-green-400 hover:text-green-300'} hover:bg-opacity-30 transition-all duration-200 p-2 rounded-lg backdrop-blur-sm`}
//                     title={`${product.active ? 'Deactivate' : 'Activate'} product`}
//                   >
//                     <ToggleRight className="w-4 h-4" />
//                   </button>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//       )}

//       {pagination && (
//         <Pagination 
//           handlePage={pagination.onPageChange}
//           page={pagination.currentPage}
//           itemsPerPage={pagination.itemsPerPage}
//           handleItemsPerPageChange={pagination.handleItemsPerChnage}
//           totalPages={pagination.totalPages}
//         />
//       )}
//     </div>
//   )
// }

// export default ProductList

import React from 'react'

function ProductList() {
  return (
    <div>ProductList</div>
  )
}

export default ProductList