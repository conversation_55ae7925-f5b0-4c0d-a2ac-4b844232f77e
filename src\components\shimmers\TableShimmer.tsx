import React from 'react';

// Reusable Table Shimmer Component
const TableShimmer = ({ rows = 5, columns = 4 }) => {
  return (
    <>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <tr key={rowIndex} className="hover:bg-slate-750 transition-colors">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <td key={colIndex} className="px-6 py-4 whitespace-nowrap">
              <div className="animate-pulse">
                <div 
                  className={`bg-slate-700 rounded ${
                    colIndex === 0 ? 'h-4 w-32' : 
                    colIndex === 1 ? 'h-4 w-24' :
                    colIndex === 2 ? 'h-4 w-20' : 
                    'h-6 w-16'
                  }`}
                />
              </div>
            </td>
          ))}
        </tr>
      ))}
    </>
  );
};

export default TableShimmer;