// // Admin Withdrawals Management Models and Interfaces

// import { ApiResponse } from '../../../../api/axiosInstance';

// // Base pagination interface
// export interface PaginationParams {
//   skip?: number;
//   limit?: number;
// }

// export interface PaginationResponse<T> {
//   total: number;
//   skip: number;
//   limit: number;
//   has_next: boolean;
//   has_prev: boolean;
//   items: T[];
// }

// // Withdrawal status enum
// export type WithdrawalStatus = 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';

// // Network types
// export type NetworkType = 'ETH' | 'BTC' | 'USDT' | 'BNB' | 'MATIC' | 'TRX';

// // Withdrawal interface
// export interface Withdrawal {
//   id: number;
//   user_id: number;
//   wallet_address: string;
//   amount: number;
//   network: NetworkType;
//   status: WithdrawalStatus;
//   approved_at: string | null;
//   processed_at: string | null;
//   completed_at: string | null;
//   transaction_hash: string | null;
//   rejection_reason: string | null;
//   admin_notes: string | null;
//   created_at: string;
//   updated_at: string;
//   // Additional fields that might be included
//   user?: {
//     id: number;
//     name: string;
//     email: string;
//   };
//   approved_by?: {
//     id: number;
//     name: string;
//     email: string;
//   };
// }

// // Create withdrawal request interface
// export interface CreateWithdrawalRequest {
//   wallet_address: string;
//   amount: number;
//   network: NetworkType;
// }

// // Update withdrawal request interface
// export interface UpdateWithdrawalRequest {
//   status?: WithdrawalStatus;
//   approved_at?: string;
//   processed_at?: string;
//   completed_at?: string;
//   transaction_hash?: string;
//   rejection_reason?: string;
//   admin_notes?: string;
// }

// // Approve withdrawal request interface
// export interface ApproveWithdrawalRequest {
//   admin_notes?: string;
// }

// // Process withdrawal request interface
// export interface ProcessWithdrawalRequest {
//   transaction_hash: string;
//   admin_notes?: string;
// }

// // API Response interfaces
// export interface GetWithdrawalsResponse extends ApiResponse<PaginationResponse<Withdrawal>> {}
// export interface GetWithdrawalResponse extends ApiResponse<Withdrawal> {}
// export interface CreateWithdrawalResponse extends ApiResponse<Withdrawal> {}
// export interface UpdateWithdrawalResponse extends ApiResponse<Withdrawal> {}
// export interface ApproveWithdrawalResponse extends ApiResponse<Withdrawal> {}
// export interface ProcessWithdrawalResponse extends ApiResponse<Withdrawal> {}
// export interface DeleteWithdrawalResponse extends ApiResponse<{ message: string }> {}

// // Form validation errors
// export interface WithdrawalFormErrors {
//   wallet_address?: string;
//   amount?: string;
//   network?: string;
//   status?: string;
//   transaction_hash?: string;
//   rejection_reason?: string;
//   admin_notes?: string;
//   general?: string;
// }

// // Withdrawal filters for search/filtering
// export interface WithdrawalFilters extends PaginationParams {
//   search?: string;
//   status?: WithdrawalStatus;
//   network?: NetworkType;
//   user_id?: number;
//   amount_min?: number;
//   amount_max?: number;
//   created_from?: string;
//   created_to?: string;
//   approved_from?: string;
//   approved_to?: string;
//   completed_from?: string;
//   completed_to?: string;
// }

// // Withdrawal statistics interface
// export interface WithdrawalStatistics {
//   total_withdrawals: number;
//   pending_withdrawals: number;
//   approved_withdrawals: number;
//   rejected_withdrawals: number;
//   processing_withdrawals: number;
//   completed_withdrawals: number;
//   total_withdrawal_amount: number;
//   approved_withdrawal_amount: number;
//   pending_withdrawal_amount: number;
//   completed_withdrawal_amount: number;
//   average_withdrawal_amount: number;
//   withdrawals_today: number;
//   withdrawals_this_week: number;
//   withdrawals_this_month: number;
//   recent_withdrawals: number;
//   withdrawals_by_network: Record<NetworkType, number>;
// }

// // Withdrawal summary for dashboard
// export interface WithdrawalSummary {
//   total_amount: number;
//   count: number;
//   status: WithdrawalStatus;
//   percentage_change?: number;
// }

// // Network configuration
// export interface NetworkConfig {
//   network: NetworkType;
//   name: string;
//   symbol: string;
//   min_withdrawal: number;
//   max_withdrawal: number;
//   fee: number;
//   fee_percentage: number;
//   confirmation_blocks: number;
//   is_active: boolean;
// }
export {};
