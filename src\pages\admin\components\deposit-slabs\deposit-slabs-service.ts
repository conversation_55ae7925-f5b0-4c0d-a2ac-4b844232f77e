// // Admin Deposit Slabs Management Service

// import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
// import {
//   DepositSlab,
//   CreateDepositSlabRequest,
//   UpdateDepositSlabRequest,
//   GetDepositSlabsResponse,
//   GetDepositSlabResponse,
//   CreateDepositSlabResponse,
//   UpdateDepositSlabResponse,
//   DeleteDepositSlabResponse,
//   DepositSlabFilters,
//   DepositSlabStatistics,
//   DepositSlabAnalytics,
//   BulkSlabUpdateRequest,
//   SlabComparison
// } from './deposit-slabs.model';

// class AdminDepositSlabsService {
//   private readonly baseUrl = '/deposite_slabs'; // Note: API uses 'deposite_slabs' (typo in API)

//   /**
//    * Get all deposit slabs with pagination and filters
//    */
//   async getDepositSlabs(filters?: DepositSlabFilters): Promise<GetDepositSlabsResponse> {
//     try {
//       const params = new URLSearchParams();

//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
//       if (filters?.amount_min) params.append('amount_min', filters.amount_min.toString());
//       if (filters?.amount_max) params.append('amount_max', filters.amount_max.toString());
//       if (filters?.profit_percentage_min) params.append('profit_percentage_min', filters.profit_percentage_min.toString());
//       if (filters?.profit_percentage_max) params.append('profit_percentage_max', filters.profit_percentage_max.toString());
//       if (filters?.duration_min) params.append('duration_min', filters.duration_min.toString());
//       if (filters?.duration_max) params.append('duration_max', filters.duration_max.toString());
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

//       const response = await apiMethods.get<GetDepositSlabsResponse>(url);

//       return {
//         success: true,
//         message: 'Deposit slabs retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit slabs error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get deposit slab by ID
//    */
//   async getDepositSlabById(slabId: number): Promise<GetDepositSlabResponse> {
//     try {
//       const response = await apiMethods.get<GetDepositSlabResponse>(`${this.baseUrl}/${slabId}`);

//       return {
//         success: true,
//         message: 'Deposit slab retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit slab error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit slab not found',
//           error: 'Deposit slab not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new deposit slab
//    */
//   async createDepositSlab(slabData: CreateDepositSlabRequest): Promise<CreateDepositSlabResponse> {
//     try {
//       const response = await apiMethods.post<CreateDepositSlabResponse>(this.baseUrl, slabData);

//       return {
//         success: true,
//         message: 'Deposit slab created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create deposit slab error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 409) {
//         return {
//           success: false,
//           message: 'Deposit slab with this name already exists',
//           error: 'Duplicate slab name'
//         };
//       }

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update deposit slab
//    */
//   async updateDepositSlab(slabId: number, slabData: UpdateDepositSlabRequest): Promise<UpdateDepositSlabResponse> {
//     try {
//       const response = await apiMethods.put<UpdateDepositSlabResponse>(`${this.baseUrl}/${slabId}`, slabData);

//       return {
//         success: true,
//         message: 'Deposit slab updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update deposit slab error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit slab not found',
//           error: 'Deposit slab not found'
//         };
//       }

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Delete deposit slab
//    */
//   async deleteDepositSlab(slabId: number): Promise<DeleteDepositSlabResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteDepositSlabResponse>(`${this.baseUrl}/${slabId}`);

//       return {
//         success: true,
//         message: 'Deposit slab deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete deposit slab error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Deposit slab not found',
//           error: 'Deposit slab not found'
//         };
//       }

//       if (errorInfo.status === 400) {
//         return {
//           success: false,
//           message: 'Cannot delete slab with active deposits',
//           error: 'Active deposits exist'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get active deposit slabs only
//    */
//   async getActiveDepositSlabs(filters?: DepositSlabFilters): Promise<GetDepositSlabsResponse> {
//     return this.getDepositSlabs({ ...filters, is_active: true });
//   }

//   /**
//    * Get inactive deposit slabs only
//    */
//   async getInactiveDepositSlabs(filters?: DepositSlabFilters): Promise<GetDepositSlabsResponse> {
//     return this.getDepositSlabs({ ...filters, is_active: false });
//   }

//   /**
//    * Toggle deposit slab status
//    */
//   async toggleDepositSlabStatus(slabId: number): Promise<UpdateDepositSlabResponse> {
//     try {
//       // First get current status
//       const currentSlab = await this.getDepositSlabById(slabId);
//       if (!currentSlab.success || !currentSlab.data) {
//         return {
//           success: false,
//           message: 'Deposit slab not found',
//           error: 'Deposit slab not found'
//         };
//       }

//       // Toggle the status
//       return this.updateDepositSlab(slabId, {
//         is_active: !currentSlab.data.is_active
//       });
//     } catch (error: any) {
//       console.error('Toggle deposit slab status error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Bulk update deposit slabs
//    */
//   async bulkUpdateDepositSlabs(updateData: BulkSlabUpdateRequest): Promise<{ success: boolean; message: string; error?: string; data?: any }> {
//     try {
//       const response = await apiMethods.post<any>(`${this.baseUrl}/bulk-update`, updateData);

//       return {
//         success: true,
//         message: 'Deposit slabs updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Bulk update deposit slabs error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get paginated deposit slabs (helper method)
//    */
//   async getPaginatedDepositSlabs(page: number = 1, limit: number = 10, filters?: Omit<DepositSlabFilters, 'skip' | 'limit'>): Promise<GetDepositSlabsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getDepositSlabs({ ...filters, skip, limit });
//   }

//   /**
//    * Search deposit slabs
//    */
//   async searchDepositSlabs(query: string, filters?: DepositSlabFilters): Promise<GetDepositSlabsResponse> {
//     return this.getDepositSlabs({ ...filters, search: query });
//   }

//   /**
//    * Get deposit slab statistics
//    */
//   async getDepositSlabStatistics(): Promise<{ success: boolean; data?: DepositSlabStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: DepositSlabStatistics }>(`${this.baseUrl}/statistics`);

//       return {
//         success: true,
//         message: 'Deposit slab statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit slab statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get deposit slab analytics
//    */
//   async getDepositSlabAnalytics(slabId: number): Promise<{ success: boolean; data?: DepositSlabAnalytics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: DepositSlabAnalytics }>(`${this.baseUrl}/${slabId}/analytics`);

//       return {
//         success: true,
//         message: 'Deposit slab analytics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get deposit slab analytics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Compare two deposit slabs
//    */
//   async compareDepositSlabs(slabAId: number, slabBId: number): Promise<{ success: boolean; data?: SlabComparison; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: SlabComparison }>(`${this.baseUrl}/compare/${slabAId}/${slabBId}`);

//       return {
//         success: true,
//         message: 'Deposit slab comparison retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Compare deposit slabs error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get popular deposit slabs (sorted by usage)
//    */
//   async getPopularDepositSlabs(limit: number = 5): Promise<GetDepositSlabsResponse> {
//     return this.getDepositSlabs({
//       limit,
//       skip: 0,
//       is_active: true
//     });
//   }
// }

// // Export singleton instance
// export const adminDepositSlabsService = new AdminDepositSlabsService();
// export default adminDepositSlabsService;
import api from "../../../../api/axiosInstance";
import {
  DepositSlab,
  DepositSlabListResponse,
  CreateDepositSlabRequest,
  UpdateDepositSlabRequest
} from "./deposit-slabs.model";

// Get all deposit slabs with pagination
export const getDepositSlabs = async (
  skip: number = 0,
  limit: number = 10
): Promise<DepositSlabListResponse> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });

    console.log('Making API call to:', `/deposite_slabs/?${queryParams.toString()}`);
    const response = await api.get(`/deposite_slabs/?${queryParams.toString()}`);
    console.log('API Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching deposit slabs:', error);
    throw error;
  }
};

// Get deposit slab by ID
export const getDepositSlabById = async (slabId: number): Promise<DepositSlab> => {
  try {
    console.log('Getting deposit slab by ID:', slabId);
    const response = await api.get(`/deposite_slabs/${slabId}`);
    console.log('Deposit slab response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching deposit slab by ID:', error);
    throw error;
  }
};

// Create new deposit slab
export const createDepositSlab = async (body: CreateDepositSlabRequest | FormData): Promise<DepositSlab> => {
  try {
    let requestData: any;
    let headers: any = {};

    // Check if body is FormData (for file upload) or regular object
    if (body instanceof FormData) {
      requestData = body;
      // Don't set Content-Type header for FormData - let browser set it with boundary
      console.log('Creating deposit slab with FormData (includes image)');
    } else {
      // Regular JSON request
      requestData = {
        name: body.name,
        amount: Number(body.amount), // Ensure amount is a number
        is_active: Boolean(body.is_active) // Ensure is_active is a boolean
      };
      headers['Content-Type'] = 'application/json';
      console.log('Creating deposit slab with JSON:', requestData);
    }

    console.log('API endpoint:', '/deposite_slabs/');

    const response = await api.post("/deposite_slabs/", requestData, {
      headers
    });

    console.log('Create response status:', response.status);
    console.log('Create response data:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating deposit slab:', error);
    console.error('Error response:', error.response?.data);
    console.error('Error status:', error.response?.status);
    throw error;
  }
};

// Update existing deposit slab
export const updateDepositSlab = async (
  slabId: number,
  body: UpdateDepositSlabRequest | FormData
): Promise<DepositSlab> => {
  try {
    let requestData: any;
    let headers: any = {};

    // Check if body is FormData (for file upload) or regular object
    if (body instanceof FormData) {
      requestData = body;
      // Don't set Content-Type header for FormData - let browser set it with boundary
      console.log('Updating deposit slab with FormData (includes image):', slabId);
    } else {
      // Regular JSON request
      requestData = body;
      headers['Content-Type'] = 'application/json';
      console.log('Updating deposit slab with JSON:', slabId, body);
    }

    const response = await api.put(`/deposite_slabs/${slabId}`, requestData, {
      headers
    });

    console.log('Update response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating deposit slab:', error);
    throw error;
  }
};

// Delete deposit slab
export const deleteDepositSlab = async (slabId: number): Promise<void> => {
  try {
    console.log('Deleting deposit slab:', slabId);
    await api.delete(`/deposite_slabs/${slabId}`);
    console.log('Delete successful');
  } catch (error) {
    console.error('Error deleting deposit slab:', error);
    throw error;
  }
};
