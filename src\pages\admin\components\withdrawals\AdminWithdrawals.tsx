import { CheckCircle, Clock, Wallet, XCircle, Search, Eye, Check, X } from "lucide-react";
import React, { useState, useEffect } from "react";
import { withdrawal, withdrawalHeader } from "./withdrawals.model";
import { getWithdrawalList, getWithdrawalHeader } from "./withdrawals-service";
import Pagination from "../../../../components/Pagination/Pagination";
import TableShimmer from "../../../../components/shimmers/TableShimmer";
import TableNoDataRow from "../../../../components/utils/TableNoDataRow";
import NoDataMessage from "../../../../components/utils/NoDataMessage";

const AdminWithdrawals: React.FC = () => {
  // State management
  const [withdrawals, setWithdrawals] = useState<withdrawal[]>([]);
  const [headerData, setHeaderData] = useState<withdrawalHeader | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(0);

  // Loading states
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false); // Shimmer loading for table
  const [isOverlayLoading, setIsOverlayLoading] = useState<boolean>(false); // Overlay loading for actions
  const [isHeaderLoading, setIsHeaderLoading] = useState<boolean>(true); // Loading for header tiles

  // API Functions
  const fetchWithdrawals = async (
    page: number,
    itemsPerPageCount: number,
    searchValue: string,
    status: string
  ) => {
    try {
      setIsTableLoading(true);
      const response = await getWithdrawalList(
        (page - 1) * itemsPerPageCount,
        itemsPerPageCount,
        searchValue,
        status
      );
      setWithdrawals(response.items);
      setCurrentPage(response.current_page);
      setTotalPages(response.total_pages);
    } catch (error) {
      console.error("Error fetching withdrawals:", error);
    } finally {
      setIsTableLoading(false);
    }
  };

  const fetchHeaderData = async () => {
    try {
      setIsHeaderLoading(true);
      const response = await getWithdrawalHeader();
      setHeaderData(response);
    } catch (error) {
      console.error("Error fetching header data:", error);
    } finally {
      setIsHeaderLoading(false);
    }
  };

  // Event Handlers
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    fetchWithdrawals(1, itemsPerPage, e.target.value, statusFilter);
  };

  const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
    fetchWithdrawals(1, itemsPerPage, searchTerm, e.target.value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchWithdrawals(page, itemsPerPage, searchTerm, statusFilter);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    fetchWithdrawals(1, value, searchTerm, statusFilter);
  };

  const handleApprove = async (withdrawalId: number) => {
    setIsOverlayLoading(true);
    try {
      // Add approve API call here
      console.log("Approving withdrawal:", withdrawalId);
      // Refresh data after action
      fetchWithdrawals(currentPage, itemsPerPage, searchTerm, statusFilter);
    } catch (error) {
      console.error("Error approving withdrawal:", error);
    } finally {
      setIsOverlayLoading(false);
    }
  };

  const handleReject = async (withdrawalId: number) => {
    setIsOverlayLoading(true);
    try {
      // Add reject API call here
      console.log("Rejecting withdrawal:", withdrawalId);
      // Refresh data after action
      fetchWithdrawals(currentPage, itemsPerPage, searchTerm, statusFilter);
    } catch (error) {
      console.error("Error rejecting withdrawal:", error);
    } finally {
      setIsOverlayLoading(false);
    }
  };

  // Initialize data on component mount
  useEffect(() => {
    fetchWithdrawals(1, itemsPerPage, "", "");
    fetchHeaderData();
  }, []);

  return (
    <div className="relative bg-slate-800 rounded-lg shadow-lg border border-slate-700 responsive-container">
      <div className="px-3 sm:px-4 lg:px-6 py-4 border-b border-slate-700">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
          <h3 className="text-lg font-semibold text-white">
            Withdrawal Requests
          </h3>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearch}
                placeholder="Search withdrawals..."
                className="mobile-input w-full sm:w-auto pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
            </div>
            <select
              value={statusFilter}
              onChange={handleStatusFilter}
              className="mobile-input w-full sm:w-auto px-4 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      <div className="p-3 sm:p-4 lg:p-6 responsive-grid gap-4">
        {/* Total Withdrawals */}
        <div className="mobile-card bg-gradient-to-r from-purple-600 to-indigo-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Total Withdrawals</span>
            <Wallet className="w-5 h-5" />
          </div>
          {isHeaderLoading ? (
            <div className="animate-pulse">
              <div className="h-8 bg-white bg-opacity-20 rounded mb-2"></div>
              <div className="h-3 bg-white bg-opacity-20 rounded w-3/4"></div>
            </div>
          ) : (
            <>
              <div className="text-xl sm:text-2xl font-bold">{headerData?.total_amount || "0"}</div>
              <div className="text-xs mt-2">
                <span className="opacity-75">{headerData?.total_withdrawals || 0} total requests</span>
              </div>
            </>
          )}
        </div>

        {/* Pending Requests */}
        <div className="mobile-card bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Pending Requests</span>
            <Clock className="w-5 h-5" />
          </div>
          {isHeaderLoading ? (
            <div className="animate-pulse">
              <div className="h-8 bg-white bg-opacity-20 rounded mb-2"></div>
              <div className="h-3 bg-white bg-opacity-20 rounded w-3/4"></div>
            </div>
          ) : (
            <>
              <div className="text-xl sm:text-2xl font-bold">{headerData?.pending_requests || 0}</div>
              <div className="text-xs mt-2">
                <span className="opacity-75">{headerData?.pending_amount || "0"} pending amount</span>
              </div>
            </>
          )}
        </div>

        {/* Approved Today */}
        <div className="mobile-card bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Approved Today</span>
            <CheckCircle className="w-5 h-5" />
          </div>
          {isHeaderLoading ? (
            <div className="animate-pulse">
              <div className="h-8 bg-white bg-opacity-20 rounded mb-2"></div>
              <div className="h-3 bg-white bg-opacity-20 rounded w-3/4"></div>
            </div>
          ) : (
            <>
              <div className="text-xl sm:text-2xl font-bold">{headerData?.approved_today || 0}</div>
              <div className="text-xs mt-2">
                <span className="opacity-75">{headerData?.approved_amount || "0"} total amount</span>
              </div>
            </>
          )}
        </div>

        {/* Rejected Today */}
        <div className="mobile-card bg-gradient-to-r from-red-600 to-red-700 rounded-lg p-4 text-white border border-slate-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm opacity-90">Rejected Today</span>
            <XCircle className="w-5 h-5" />
          </div>
          {isHeaderLoading ? (
            <div className="animate-pulse">
              <div className="h-8 bg-white bg-opacity-20 rounded mb-2"></div>
              <div className="h-3 bg-white bg-opacity-20 rounded w-3/4"></div>
            </div>
          ) : (
            <>
              <div className="text-xl sm:text-2xl font-bold">{headerData?.rejected_today || 0}</div>
              <div className="text-xs mt-2">
                <span className="opacity-75">{headerData?.rejected_amount || "0"} total amount</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-700">
          <thead className="bg-slate-700/50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                User
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Amount
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Wallet/Bank Details
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Reference ID
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-slate-800 divide-y divide-slate-700">
            {isTableLoading ? (
              <TableShimmer rows={itemsPerPage} columns={7} />
            ) : withdrawals.length > 0 ? (
              withdrawals.map((withdrawal) => (
                <tr key={withdrawal.id} className="hover:bg-slate-700/30 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-blue-600/20 border border-blue-500 flex items-center justify-center">
                        <span className="text-blue-400 font-medium">
                          {withdrawal.user_name ? withdrawal.user_name.charAt(0) : 'U'}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-white">
                          {withdrawal.user_name || `User ${withdrawal.user_id}`}
                        </div>
                        <div className="text-sm text-slate-300">
                          ID: {withdrawal.user_id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">
                      {withdrawal.amount} {withdrawal.network}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300 max-w-xs truncate font-mono">
                      {withdrawal.wallet_address}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">
                      {new Date(withdrawal.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300 font-mono">
                      WDR-{withdrawal.id}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        withdrawal.status === 'approved' ? 'bg-green-900 text-green-300' :
                        withdrawal.status === 'pending' ? 'bg-yellow-900 text-yellow-300' :
                        'bg-red-900 text-red-300'
                      }`}
                    >
                      {withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-3">
                      <button
                        className="text-blue-400 hover:text-blue-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700"
                        aria-label="View withdrawal"
                        disabled={isOverlayLoading}
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {withdrawal.status === "pending" && (
                        <>
                          <button
                            onClick={() => handleApprove(withdrawal.id)}
                            className="text-green-400 hover:text-green-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700 disabled:opacity-50"
                            aria-label="Approve withdrawal"
                            disabled={isOverlayLoading}
                          >
                            <Check className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleReject(withdrawal.id)}
                            className="text-red-400 hover:text-red-300 transition-colors duration-200 p-1 rounded hover:bg-slate-700 disabled:opacity-50"
                            aria-label="Reject withdrawal"
                            disabled={isOverlayLoading}
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <TableNoDataRow
                colSpan={7}
                type="withdrawals"
                showSearchHint={!!searchTerm}
                showFilterHint={!!statusFilter}
              />
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden p-3 sm:p-4 space-y-3">
        {isTableLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="mobile-table-card animate-pulse">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="h-10 w-10 bg-slate-600 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-slate-600 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-slate-700 rounded w-full"></div>
                  <div className="h-3 bg-slate-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : withdrawals.length > 0 ? (
          withdrawals.map((withdrawal) => (
            <div key={withdrawal.id} className="mobile-table-card">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <div className="h-10 w-10 rounded-full bg-blue-600/20 border border-blue-500 flex items-center justify-center flex-shrink-0">
                    <span className="text-blue-400 font-medium text-sm">
                      {withdrawal.user_name ? withdrawal.user_name.charAt(0) : 'U'}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-sm font-medium text-white truncate">
                      {withdrawal.user_name || `User ${withdrawal.user_id}`}
                    </div>
                    <div className="text-xs text-slate-400">
                      ID: {withdrawal.user_id}
                    </div>
                  </div>
                </div>
                <span
                  className={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full flex-shrink-0 ${
                    withdrawal.status === 'approved' ? 'bg-green-900 text-green-300' :
                    withdrawal.status === 'pending' ? 'bg-yellow-900 text-yellow-300' :
                    'bg-red-900 text-red-300'
                  }`}
                >
                  {withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}
                </span>
              </div>

              <div className="space-y-2 mb-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Amount:</span>
                  <span className="text-sm font-medium text-white">{withdrawal.amount} {withdrawal.network}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Date:</span>
                  <span className="text-xs text-slate-300">{new Date(withdrawal.created_at).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Reference:</span>
                  <span className="text-xs text-slate-300 font-mono">WDR-{withdrawal.id}</span>
                </div>
                <div className="flex justify-between items-start">
                  <span className="text-xs text-slate-400">Wallet:</span>
                  <span className="text-xs text-slate-300 font-mono text-right max-w-32 truncate">{withdrawal.wallet_address}</span>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  className="text-blue-400 hover:text-blue-300 transition-colors duration-200 p-2 rounded hover:bg-slate-700 min-h-touch-target"
                  aria-label="View withdrawal"
                  disabled={isOverlayLoading}
                >
                  <Eye className="w-4 h-4" />
                </button>
                {withdrawal.status === "pending" && (
                  <>
                    <button
                      onClick={() => handleApprove(withdrawal.id)}
                      className="text-green-400 hover:text-green-300 transition-colors duration-200 p-2 rounded hover:bg-slate-700 disabled:opacity-50 min-h-touch-target"
                      aria-label="Approve withdrawal"
                      disabled={isOverlayLoading}
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleReject(withdrawal.id)}
                      className="text-red-400 hover:text-red-300 transition-colors duration-200 p-2 rounded hover:bg-slate-700 disabled:opacity-50 min-h-touch-target"
                      aria-label="Reject withdrawal"
                      disabled={isOverlayLoading}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </>
                )}
              </div>
            </div>
          ))
        ) : (
          <NoDataMessage
            type="withdrawals"
            showSearchHint={!!searchTerm}
            showFilterHint={!!statusFilter}
            height="medium"
          />
        )}
      </div>

      {/* Pagination */}
      {withdrawals.length > 0 && (
        <div className="px-3 sm:px-4 lg:px-6 py-4 border-t border-slate-700">
          <Pagination
            handlePage={handlePageChange}
            page={currentPage}
            itemsPerPage={itemsPerPage}
            handleItemsPerPageChange={handleItemsPerPageChange}
            totalPages={totalPages}
          />
        </div>
      )}

      {/* Overlay Loading */}
      {isOverlayLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-lg">
          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-white">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminWithdrawals;