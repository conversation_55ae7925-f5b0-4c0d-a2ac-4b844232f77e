# Fruit-O-Cart Product Context

## Project Overview
Fruit-O-Cart is a React-based web application that appears to be a financial/investment platform with fruit-themed branding. The application provides both user and admin interfaces for managing investments, deposits, withdrawals, and referrals.

## Core Purpose
- **Investment Platform**: Users can make deposits, track earnings, and manage withdrawals
- **Referral System**: Built-in referral program for user acquisition
- **Product Management**: Users can view and interact with investment products
- **Admin Dashboard**: Complete administrative control over users, transactions, and system settings

## Key Features
1. **User Management**: Registration, login, profile management
2. **Financial Operations**: Deposits, withdrawals, earnings tracking
3. **Referral Program**: User referral system with tracking
4. **Product Catalog**: Investment products with management capabilities
5. **Admin Controls**: User management, transaction oversight, system configuration
6. **Multi-currency Support**: Currency management system
7. **Deposit Slabs**: Tiered deposit structure

## Target Users
- **Primary Users**: Individual investors looking for investment opportunities
- **Administrators**: Platform managers overseeing operations
- **Referrers**: Users participating in the referral program

## Technology Stack
- **Frontend**: React 19.1.0 with TypeScript
- **Styling**: Tailwind CSS + SCSS
- **Routing**: React Router DOM 7.5.3
- **HTTP Client**: Axios 1.9.0
- **Charts**: ECharts 5.6.0
- **Icons**: Lucide React 0.503.0
- **Build Tool**: React Scripts 5.0.1

## Current State
The application has a solid foundation with:
- Authentication system (login/signup)
- Routing structure for user and admin areas
- API integration setup
- Component structure in place
- Professional dark theme design

## Business Model
Based on the structure, this appears to be an investment platform where:
- Users deposit funds into various investment products
- Platform generates returns/profits
- Users can withdraw earnings
- Referral system drives user acquisition
- Admin manages the entire ecosystem
