// // Admin Withdrawals Management Service

import api from "../../../../api/axiosInstance";
import { withdrawList } from "./withdrawals.model";

// import { apiMethods, apiUtils } from '../../../../api/axiosInstance';
// import {
//   Withdrawal,
//   CreateWithdrawalRequest,
//   UpdateWithdrawalRequest,
//   ApproveWithdrawalRequest,
//   ProcessWithdrawalRequest,
//   GetWithdrawalsResponse,
//   GetWithdrawalResponse,
//   CreateWithdrawalResponse,
//   UpdateWithdrawalResponse,
//   ApproveWithdrawalResponse,
//   ProcessWithdrawalResponse,
//   DeleteWithdrawalResponse,
//   WithdrawalFilters,
//   WithdrawalStatistics,
//   WithdrawalSummary,
//   NetworkConfig
// } from './withdrawals.model';

// class AdminWithdrawalsService {
//   private readonly baseUrl = '/withdrawals';

//   /**
//    * Get all withdrawals with pagination and filters
//    */
//   async getWithdrawals(filters?: WithdrawalFilters): Promise<GetWithdrawalsResponse> {
//     try {
//       const params = new URLSearchParams();

//       // Add pagination params
//       if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
//       if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

//       // Add filter params
//       if (filters?.search) params.append('search', filters.search);
//       if (filters?.status) params.append('status', filters.status);
//       if (filters?.network) params.append('network', filters.network);
//       if (filters?.user_id) params.append('user_id', filters.user_id.toString());
//       if (filters?.amount_min) params.append('amount_min', filters.amount_min.toString());
//       if (filters?.amount_max) params.append('amount_max', filters.amount_max.toString());
//       if (filters?.created_from) params.append('created_from', filters.created_from);
//       if (filters?.created_to) params.append('created_to', filters.created_to);
//       if (filters?.approved_from) params.append('approved_from', filters.approved_from);
//       if (filters?.approved_to) params.append('approved_to', filters.approved_to);
//       if (filters?.completed_from) params.append('completed_from', filters.completed_from);
//       if (filters?.completed_to) params.append('completed_to', filters.completed_to);

//       const queryString = params.toString();
//       const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

//       const response = await apiMethods.get<GetWithdrawalsResponse>(url);

//       return {
//         success: true,
//         message: 'Withdrawals retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get withdrawals error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get withdrawal by ID
//    */
//   async getWithdrawalById(withdrawalId: number): Promise<GetWithdrawalResponse> {
//     try {
//       const response = await apiMethods.get<GetWithdrawalResponse>(`${this.baseUrl}/${withdrawalId}`);

//       return {
//         success: true,
//         message: 'Withdrawal retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get withdrawal error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Withdrawal not found',
//           error: 'Withdrawal not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Create new withdrawal
//    */
//   async createWithdrawal(withdrawalData: CreateWithdrawalRequest): Promise<CreateWithdrawalResponse> {
//     try {
//       const response = await apiMethods.post<CreateWithdrawalResponse>(this.baseUrl, withdrawalData);

//       return {
//         success: true,
//         message: 'Withdrawal created successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Create withdrawal error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       if (errorInfo.status === 400) {
//         return {
//           success: false,
//           message: 'Insufficient balance or invalid withdrawal request',
//           error: 'Invalid request'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Update withdrawal
//    */
//   async updateWithdrawal(withdrawalId: number, withdrawalData: UpdateWithdrawalRequest): Promise<UpdateWithdrawalResponse> {
//     try {
//       const response = await apiMethods.put<UpdateWithdrawalResponse>(`${this.baseUrl}/${withdrawalId}`, withdrawalData);

//       return {
//         success: true,
//         message: 'Withdrawal updated successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Update withdrawal error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Withdrawal not found',
//           error: 'Withdrawal not found'
//         };
//       }

//       if (errorInfo.status === 422) {
//         return {
//           success: false,
//           message: 'Validation failed',
//           errors: errorInfo.errors
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Approve withdrawal
//    */
//   async approveWithdrawal(withdrawalId: number, approvalData?: ApproveWithdrawalRequest): Promise<ApproveWithdrawalResponse> {
//     try {
//       const response = await apiMethods.post<ApproveWithdrawalResponse>(`${this.baseUrl}/${withdrawalId}/approve`, approvalData || {});

//       return {
//         success: true,
//         message: 'Withdrawal approved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Approve withdrawal error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Withdrawal not found',
//           error: 'Withdrawal not found'
//         };
//       }

//       if (errorInfo.status === 403) {
//         return {
//           success: false,
//           message: 'Insufficient permissions to approve withdrawals',
//           error: 'Access denied'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Process withdrawal (mark as processing with transaction hash)
//    */
//   async processWithdrawal(withdrawalId: number, processData: ProcessWithdrawalRequest): Promise<ProcessWithdrawalResponse> {
//     try {
//       const response = await apiMethods.post<ProcessWithdrawalResponse>(`${this.baseUrl}/${withdrawalId}/process`, processData);

//       return {
//         success: true,
//         message: 'Withdrawal processed successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Process withdrawal error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Withdrawal not found',
//           error: 'Withdrawal not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Reject withdrawal
//    */
//   async rejectWithdrawal(withdrawalId: number, rejectionReason: string): Promise<UpdateWithdrawalResponse> {
//     return this.updateWithdrawal(withdrawalId, {
//       status: 'rejected',
//       rejection_reason: rejectionReason
//     });
//   }

//   /**
//    * Complete withdrawal
//    */
//   async completeWithdrawal(withdrawalId: number): Promise<UpdateWithdrawalResponse> {
//     return this.updateWithdrawal(withdrawalId, {
//       status: 'completed',
//       completed_at: new Date().toISOString()
//     });
//   }

//   /**
//    * Delete withdrawal
//    */
//   async deleteWithdrawal(withdrawalId: number): Promise<DeleteWithdrawalResponse> {
//     try {
//       const response = await apiMethods.delete<DeleteWithdrawalResponse>(`${this.baseUrl}/${withdrawalId}`);

//       return {
//         success: true,
//         message: 'Withdrawal deleted successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Delete withdrawal error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       if (errorInfo.status === 404) {
//         return {
//           success: false,
//           message: 'Withdrawal not found',
//           error: 'Withdrawal not found'
//         };
//       }

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get pending withdrawals
//    */
//   async getPendingWithdrawals(filters?: WithdrawalFilters): Promise<GetWithdrawalsResponse> {
//     return this.getWithdrawals({ ...filters, status: 'pending' });
//   }

//   /**
//    * Get approved withdrawals
//    */
//   async getApprovedWithdrawals(filters?: WithdrawalFilters): Promise<GetWithdrawalsResponse> {
//     return this.getWithdrawals({ ...filters, status: 'approved' });
//   }

//   /**
//    * Get processing withdrawals
//    */
//   async getProcessingWithdrawals(filters?: WithdrawalFilters): Promise<GetWithdrawalsResponse> {
//     return this.getWithdrawals({ ...filters, status: 'processing' });
//   }

//   /**
//    * Get completed withdrawals
//    */
//   async getCompletedWithdrawals(filters?: WithdrawalFilters): Promise<GetWithdrawalsResponse> {
//     return this.getWithdrawals({ ...filters, status: 'completed' });
//   }

//   /**
//    * Get paginated withdrawals (helper method)
//    */
//   async getPaginatedWithdrawals(page: number = 1, limit: number = 10, filters?: Omit<WithdrawalFilters, 'skip' | 'limit'>): Promise<GetWithdrawalsResponse> {
//     const skip = (page - 1) * limit;
//     return this.getWithdrawals({ ...filters, skip, limit });
//   }

//   /**
//    * Search withdrawals
//    */
//   async searchWithdrawals(query: string, filters?: WithdrawalFilters): Promise<GetWithdrawalsResponse> {
//     return this.getWithdrawals({ ...filters, search: query });
//   }

//   /**
//    * Get withdrawal statistics
//    */
//   async getWithdrawalStatistics(): Promise<{ success: boolean; data?: WithdrawalStatistics; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: WithdrawalStatistics }>(`${this.baseUrl}/statistics`);

//       return {
//         success: true,
//         message: 'Statistics retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get withdrawal statistics error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get withdrawal summary by status
//    */
//   async getWithdrawalSummary(): Promise<{ success: boolean; data?: WithdrawalSummary[]; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: WithdrawalSummary[] }>(`${this.baseUrl}/summary`);

//       return {
//         success: true,
//         message: 'Withdrawal summary retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get withdrawal summary error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }

//   /**
//    * Get network configurations
//    */
//   async getNetworkConfigs(): Promise<{ success: boolean; data?: NetworkConfig[]; message: string; error?: string }> {
//     try {
//       const response = await apiMethods.get<{ data: NetworkConfig[] }>(`${this.baseUrl}/networks`);

//       return {
//         success: true,
//         message: 'Network configurations retrieved successfully',
//         data: response.data.data
//       };
//     } catch (error: any) {
//       console.error('Get network configs error:', error);
//       const errorInfo = apiUtils.handleApiError(error);

//       return {
//         success: false,
//         message: errorInfo.message,
//         error: errorInfo.message
//       };
//     }
//   }
// }

// // Export singleton instance
// export const adminWithdrawalsService = new AdminWithdrawalsService();
// export default adminWithdrawalsService;


export const getWithdrawalList = async (
  skip: number,
  itemsPerPage: number,
  search: string,
  status?: string
): Promise<withdrawList> => {
  try {
    const queryParams = new URLSearchParams({
      skip: skip.toString(),
      limit: itemsPerPage.toString(),
      search: search,
    });

    if (status) {
      queryParams.append("status", encodeURIComponent(status));
    }
    const response = await api.get<withdrawList>(`withdrawals/?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getWithdrawalHeader = async () => {
  try {
    const response = await api.get("withdrawal_header/");
    return response.data;
  } catch (error) {
    throw error;
  }
};