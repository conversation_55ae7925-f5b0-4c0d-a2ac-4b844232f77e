import React, { useState } from "react";
import { Apple, Mail, Lock, Loader, Shield } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { FormErrors } from "./login-signup.model";
import ResetPasswordModal from "./components/ResetPasswoard";
import { loginApi } from "./login-signup-serivce";

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({}); // Only clear errors, not form data

    // Basic validation
    const newErrors: FormErrors = {};
    if (!email) newErrors.email = "Email is required";
    if (!password) newErrors.password = "Password is required";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      setIsLoading(true);
      const response = await loginApi({ email, password });
      
      // Only clear form data on successful login
      if (response.access_token) {
        localStorage.setItem('token', response.access_token);
        localStorage.setItem('refresh_token', response.refresh_token);
        
        // Clear form data only after successful login
        setEmail("");
        setPassword("");
        setErrors({});
        
        if (response.user_role === 'admin') {
          navigate('/admin/dashboard');
        } else {
          navigate('/user/dashboard');
        }
      }
    } catch (error: any) {
      // Don't clear form data on error - user keeps their input
      console.error('Login error:', error);
      
      // Set appropriate error message based on error type
      let errorMessage = 'An unexpected error occurred. Please try again.';
      
      if (error.response?.status === 401) {
        errorMessage = 'Invalid email or password. Please try again.';
      } else if (error.response?.status === 429) {
        errorMessage = 'Too many login attempts. Please try again later.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setErrors({ general: errorMessage });
    } finally {
      setIsLoading(false); // Always set loading to false
    }
  };

  const handleForgotPasswordClick = () => {
    setShowForgotPassword(true);
  };

  const handleResetPasswordClose = () => {
    setShowForgotPassword(false);
  };

  // Optional: Clear only specific errors when user starts typing
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    // Clear email error when user starts typing
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: undefined }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    // Clear password error when user starts typing
    if (errors.password) {
      setErrors(prev => ({ ...prev, password: undefined }));
    }
  };

  return (
    <div className="min-h-screen bg-[#0D1117] flex items-center justify-center px-4 sm:px-6 lg:px-8">
      
      <ResetPasswordModal 
        isOpen={showForgotPassword} 
        onClose={handleResetPasswordClose}
        initialEmail={email}
      />

      <div className="max-w-md w-full space-y-8 bg-[#1A1F2E] p-10 rounded-xl shadow-lg">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
              <Apple className="text-white" size={32} />
            </div>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-white">
            Fruit-O-Cart
          </h2>
          <p className="mt-2 text-sm text-gray-400">Sign in to your account</p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {errors.general && (
            <div className="bg-red-900/20 border border-red-800 text-red-400 px-4 py-3 rounded-md text-sm">
              {errors.general}
            </div>
          )}

          <div className="rounded-md -space-y-px">
            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-300 mb-1 text-left"
              >
                Email or Phone
              </label>
              <div className="relative flex items-center">
                <Mail className="absolute left-3 text-gray-400" size={20} />
                <input
                  id="email"
                  name="email"
                  type="text"
                  autoComplete="email"
                  value={email}
                  onChange={handleEmailChange} // Updated handler
                  className={`appearance-none rounded-md block w-full pl-10 py-3 px-4 border ${
                    errors.email ? "border-red-300" : "border-gray-700"
                  } bg-[#131722] placeholder-gray-500 text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors`}
                  placeholder="Email address or phone number"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div className="mb-2">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-300 mb-1 text-left"
              >
                Password
              </label>
              <div className="relative flex items-center">
                <Lock className="absolute left-3 text-gray-400" size={20} />
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={handlePasswordChange} // Updated handler
                  className={`appearance-none rounded-md block w-full pl-10 py-3 px-4 border ${
                    errors.password ? "border-red-300" : "border-gray-700"
                  } bg-[#131722] placeholder-gray-500 text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors`}
                  placeholder="Password"
                />
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-300 cursor-pointer"
              >
                Remember me
              </label>
            </div>
            <div className="text-sm">
              <button
                type="button"
                onClick={handleForgotPasswordClick}
                className="font-medium text-blue-600 hover:text-blue-500 border-none bg-transparent cursor-pointer transition-colors"
              >
                Forgot your password?
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] hover:from-[#3A5FEF] hover:to-[#5C7FEF] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <Loader className="animate-spin mr-2" size={20} />
                  Signing in...
                </span>
              ) : (
                "Sign in"
              )}
            </button>
          </div>
        </form>

        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <a
              href="/signup"
              className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
            >
              Sign up
            </a>
          </p>
        </div>

        <div className="mt-6">
          <div className="bg-[#131722] rounded-lg p-4">
            <div className="flex items-center justify-center space-x-2 text-blue-400">
              <Shield className="h-5 w-5" />
              <span className="text-sm font-medium">
                Your data is securely encrypted
              </span>
            </div>
            <p className="mt-2 text-xs text-center text-gray-400">
              We use industry-standard encryption to protect your personal
              information. Your security is our top priority.
            </p>
          </div>
          <div className="mt-4 text-center text-xs text-gray-400">
            By signing in, you agree to our{" "}
            <a
              href="https://readdy.ai/home/<USER>/eac7f9ce-ba14-4ad7-a5fe-b0c72a1c0ff7"
              data-readdy="true"
              className="text-blue-600 hover:underline"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <button className="text-blue-600 hover:underline">
              Privacy Policy
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;