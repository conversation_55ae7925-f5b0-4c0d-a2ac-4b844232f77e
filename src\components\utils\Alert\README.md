# Alert Component

A flexible, accessible, and customizable alert/modal component for React applications.

## Features

- ✅ **Multiple Alert Types**: Success, Error, Info with appropriate icons
- ✅ **Flexible Positioning**: Center, corners, and edges
- ✅ **Input Support**: Text inputs and textareas
- ✅ **Auto-close**: Configurable auto-close timer
- ✅ **Keyboard Support**: ESC to close, Enter to confirm
- ✅ **Accessibility**: ARIA attributes, screen reader friendly
- ✅ **Responsive Design**: Mobile-friendly layout
- ✅ **TypeScript**: Full TypeScript support
- ✅ **Customizable**: Extensive styling options

## Installation

The Alert component is already included in the project. No additional installation required.

## Setup

1. Wrap your app with the `AlertProvider`:

```tsx
import { AlertProvider } from './components/utils/Alert';

function App() {
  return (
    <AlertProvider>
      {/* Your app content */}
    </AlertProvider>
  );
}
```

2. Use the `useAlert` hook in your components:

```tsx
import { useAlert } from './components/utils/Alert';

function MyComponent() {
  const { fire } = useAlert();

  const showAlert = () => {
    fire({
      icon: 'success',
      title: 'Success!',
      text: 'Operation completed successfully.',
      confirmButtonText: 'OK'
    });
  };

  return <button onClick={showAlert}>Show Alert</button>;
}
```

## API Reference

### AlertOptions

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `position` | `"center" \| "top-right" \| "top-left" \| "bottom-right" \| "bottom-left"` | `"center"` | Alert position on screen |
| `icon` | `"success" \| "error" \| "info"` | `"info"` | Icon type to display |
| `title` | `string` | `""` | Alert title |
| `text` | `string` | `""` | Alert message |
| `autoClose` | `number` | `0` | Auto-close time in milliseconds (0 = no auto-close) |
| `showInput` | `boolean` | `false` | Show input field |
| `inputType` | `"text" \| "textarea" \| "email" \| "password"` | `"text"` | Input field type |
| `initialValue` | `string` | `""` | Initial input value |
| `placeholder` | `string` | `""` | Input placeholder text |
| `confirmButtonText` | `string` | `""` | Confirm button text |
| `cancelButtonText` | `string` | `""` | Cancel button text |
| `onConfirm` | `(inputValue: string) => void` | - | Confirm callback |
| `onCancel` | `() => void` | - | Cancel callback |
| `onClose` | `() => void` | - | Close callback |

## Usage Examples

### Basic Alert

```tsx
fire({
  icon: 'success',
  title: 'Success!',
  text: 'Your changes have been saved.',
  confirmButtonText: 'OK'
});
```

### Confirmation Dialog

```tsx
fire({
  icon: 'error',
  title: 'Delete Item',
  text: 'Are you sure you want to delete this item? This action cannot be undone.',
  confirmButtonText: 'Delete',
  cancelButtonText: 'Cancel',
  onConfirm: () => {
    // Handle deletion
    console.log('Item deleted');
  },
  onCancel: () => {
    console.log('Deletion cancelled');
  }
});
```

### Input Dialog

```tsx
fire({
  icon: 'info',
  title: 'Enter Your Name',
  text: 'Please provide your name:',
  showInput: true,
  inputType: 'text',
  placeholder: 'Your name...',
  confirmButtonText: 'Submit',
  cancelButtonText: 'Cancel',
  onConfirm: (inputValue) => {
    console.log('User entered:', inputValue);
  }
});
```

### Auto-close Notification

```tsx
fire({
  icon: 'success',
  title: 'Saved!',
  text: 'Your changes have been saved automatically.',
  autoClose: 3000,
  position: 'top-right'
});
```

### Textarea Input

```tsx
fire({
  icon: 'info',
  title: 'Feedback',
  text: 'Please share your thoughts:',
  showInput: true,
  inputType: 'textarea',
  placeholder: 'Enter your feedback...',
  confirmButtonText: 'Submit',
  cancelButtonText: 'Cancel',
  onConfirm: (feedback) => {
    console.log('Feedback:', feedback);
  }
});
```

## Keyboard Shortcuts

- **ESC**: Close alert or trigger cancel action
- **Enter**: Confirm action (when no input field is focused)

## Styling

The component uses SCSS for styling with CSS custom properties for theming. Key classes:

- `.alert-box`: Main alert container
- `.text-area`: Input/textarea styling
- `.alert-icons`: Icon container
- `.primary-background`: Primary button styling

## Accessibility

The component includes:

- ARIA roles and attributes
- Keyboard navigation support
- Screen reader friendly labels
- Focus management
- Semantic HTML structure

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Requires CSS Grid and Flexbox support

## Dependencies

- React 19+
- Lucide React (for icons)
- SCSS support
- Tailwind CSS (for utility classes)
