// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";
import { Apple, Mail, Lock, Loader, Shield, X, KeyRound } from "lucide-react";
const App: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>(
    {},
  );
  const [isLoading, setIsLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetStep, setResetStep] = useState(1);
  const [resetEmail, setResetEmail] = useState("");
  const [resetOtp, setResetOtp] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [resetError, setResetError] = useState("");

  const handleResetSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setResetError("");

    if (resetStep === 1) {
      if (!resetEmail) {
        setResetError("Please enter your email");
        return;
      }
      // Simulate sending OTP
      setTimeout(() => {
        setResetStep(2);
      }, 1000);
    } else if (resetStep === 2) {
      if (!resetOtp || resetOtp.length !== 6) {
        setResetError("Please enter valid 6-digit OTP");
        return;
      }
      // Simulate OTP verification
      setTimeout(() => {
        setResetStep(3);
      }, 1000);
    } else if (resetStep === 3) {
      if (!newPassword || !confirmPassword) {
        setResetError("Please fill in all fields");
        return;
      }
      if (newPassword !== confirmPassword) {
        setResetError("Passwords do not match");
        return;
      }
      if (newPassword.length < 8) {
        setResetError("Password must be at least 8 characters");
        return;
      }
      // Simulate password update
      setTimeout(() => {
        setShowForgotPassword(false);
        setResetStep(1);
        setResetEmail("");
        setResetOtp("");
        setNewPassword("");
        setConfirmPassword("");
        alert("Password updated successfully!");
      }, 1000);
    }
  };

  const resetPasswordModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-[#1A1F2E] p-8 rounded-xl max-w-md w-full relative">
          <button
            onClick={() => {
              setShowForgotPassword(false);
              setResetStep(1);
              setResetError("");
            }}
            className="absolute right-4 top-4 text-gray-400 hover:text-white"
          >
            <X size={20} />
          </button>

          <div className="flex items-center justify-center mb-6">
            <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
              <KeyRound className="text-white" size={24} />
            </div>
          </div>

          <h3 className="text-xl font-bold text-white text-center mb-6">
            {resetStep === 1
              ? "Reset Password"
              : resetStep === 2
                ? "Enter OTP"
                : "Create New Password"}
          </h3>

          <form onSubmit={handleResetSubmit} className="space-y-4">
            {resetStep === 1 && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your email"
                />
              </div>
            )}

            {resetStep === 2 && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Enter OTP
                </label>
                <input
                  type="text"
                  maxLength={6}
                  value={resetOtp}
                  onChange={(e) =>
                    setResetOtp(e.target.value.replace(/\D/g, ""))
                  }
                  className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter 6-digit OTP"
                />
              </div>
            )}

            {resetStep === 3 && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    New Password
                  </label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter new password"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full px-4 py-3 rounded-md bg-[#131722] border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Confirm new password"
                  />
                </div>
              </>
            )}

            {resetError && <p className="text-red-500 text-sm">{resetError}</p>}

            <button
              type="submit"
              className="w-full py-3 px-4 rounded-button text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap"
            >
              {resetStep === 1
                ? "Send OTP"
                : resetStep === 2
                  ? "Verify OTP"
                  : "Update Password"}
            </button>
          </form>
        </div>
      </div>
    );
  };
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});
    // Basic validation
    const newErrors: { email?: string; password?: string } = {};
    if (!email) newErrors.email = "Email is required";
    if (!password) newErrors.password = "Password is required";
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    // Simulate login
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      // Redirect would happen here in a real app
      alert("Login successful! Redirecting...");
    }, 1500);
  };
  return (
    <div className="min-h-screen bg-[#0D1117] flex items-center justify-center px-4 sm:px-6 lg:px-8">
      {showForgotPassword && resetPasswordModal()}
      <div className="max-w-md w-full space-y-8 bg-[#1A1F2E] p-10 rounded-xl shadow-lg">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
              <Apple className="text-white" size={32} />
            </div>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-white">
            Fruit-O-Cart
          </h2>
          <p className="mt-2 text-sm text-gray-400">Sign in to your account</p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md -space-y-px">
            <div className="mb-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-300 mb-1"
              >
                Email or Phone
              </label>
              <div className="relative flex items-center">
                <Mail className="absolute left-3 text-gray-400" size={20} />
                <input
                  id="email"
                  name="email"
                  type="text"
                  autoComplete="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`appearance-none rounded-md block w-full pl-10 py-3 px-4 border ${
                    errors.email ? "border-red-300" : "border-gray-700"
                  } bg-[#131722] placeholder-gray-500 text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  placeholder="Email address or phone number"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>
            <div className="mb-2">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-300 mb-1"
              >
                Password
              </label>
              <div className="relative flex items-center">
                <Lock className="absolute left-3 text-gray-400" size={20} />
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`appearance-none rounded-md block w-full pl-10 py-3 px-4 border ${
                    errors.password ? "border-red-300" : "border-gray-700"
                  } bg-[#131722] placeholder-gray-500 text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  placeholder="Password"
                />
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-300 cursor-pointer"
              >
                Remember me
              </label>
            </div>
            <div className="text-sm">
              <button
                onClick={() => setShowForgotPassword(true)}
                className="font-medium text-blue-600 hover:text-blue-500 border-none bg-transparent cursor-pointer"
              >
                Forgot your password?
              </button>
            </div>
          </div>
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-button text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 whitespace-nowrap cursor-pointer"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <Loader className="animate-spin mr-2" size={20} />
                  Signing in...
                </span>
              ) : (
                "Sign in"
              )}
            </button>
          </div>
        </form>
        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <a
              href="/signup"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              Sign up
            </a>
          </p>
        </div>
        <div className="mt-6">
          <div className="bg-[#131722] rounded-lg p-4">
            <div className="flex items-center justify-center space-x-2 text-blue-400">
              <Shield className="h-5 w-5" />
              <span className="text-sm font-medium">
                Your data is securely encrypted
              </span>
            </div>
            <p className="mt-2 text-xs text-center text-gray-400">
              We use industry-standard encryption to protect your personal
              information. Your security is our top priority.
            </p>
          </div>
          <div className="mt-4 text-center text-xs text-gray-400">
            By signing in, you agree to our{" "}
            <a
              href="https://readdy.ai/home/<USER>/eac7f9ce-ba14-4ad7-a5fe-b0c72a1c0ff7"
              data-readdy="true"
              className="text-blue-600 hover:underline"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-blue-600 hover:underline">
              Privacy Policy
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
export default App;
