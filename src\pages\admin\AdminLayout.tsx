import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'
import AdminSideBar from '../../components/sidebar/AdminSideBar'
import Header from '../../components/header/Header'

function AdminLayout() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
        <div className="min-h-screen bg-[#0F1115] flex flex-col">
          <Header
            type='admin'
            isMobileMenuOpen={isMobileMenuOpen}
            setIsMobileMenuOpen={setIsMobileMenuOpen}
          />
          {/* Main Content */}
          <div className="flex flex-1 pt-16">
            <AdminSideBar
              isMobileMenuOpen={isMobileMenuOpen}
              setIsMobileMenuOpen={setIsMobileMenuOpen}
            />
            {/* Main Content Area */}
            <main className="flex-1 ml-0 md:ml-64 min-h-screen p-4 md:p-6">
              <Outlet />
            </main>
          </div>
        </div>
  )
}

export default AdminLayout