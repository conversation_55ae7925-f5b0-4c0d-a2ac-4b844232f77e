export interface ReferralTreeNode {
  user_id: number;
  username?: string;
  email?: string;
  level: number;
  total_deposit: number;
  commission_earned: number;
  join_date: string;
  status: 'active' | 'inactive';
  children?: ReferralTreeNode[];
}

// Based on the API documentation, the response structure is different
export interface ReferralTreeResponse {
  [key: string]: any; // The API returns dynamic property names like "additionalProp1"
}

export interface ReferralStats {
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  conversion_rate: number;
  avg_earnings_per_referral: number;
}

export interface ReferredUser {
  id: number;
  name: string;
  email: string;
  date: string;
  status: 'Active' | 'Inactive';
  earnings: number;
  level: number;
}

export interface ReferralList {
  total_pages: number;
  current_page: number;
  items: ReferredUser[];
}