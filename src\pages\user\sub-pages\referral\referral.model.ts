export interface ReferralTreeNode {
  user_id: number;
  username?: string;
  email?: string;
  level: number;
  total_deposit: number;
  commission_earned: number;
  join_date: string;
  status: 'active' | 'inactive';
  children?: ReferralTreeNode[];
}

export interface ReferralTreeResponse {
  user_id: number;
  referrals: ReferralTreeNode[];
}

export interface ReferralStats {
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  conversion_rate: number;
  avg_earnings_per_referral: number;
}

export interface ReferredUser {
  id: number;
  name: string;
  email: string;
  date: string;
  status: 'Active' | 'Inactive';
  earnings: number;
  level: number;
}

export interface ReferralList {
  total_pages: number;
  current_page: number;
  items: ReferredUser[];
}