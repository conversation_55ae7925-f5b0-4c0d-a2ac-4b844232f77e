export interface ReferralTreeNode {
  user_id: number;
  referrer_id?: number | null;
  level: number;
  name: string;
  total_deposit: number;
  commission_to_root: string;
  legs: ReferralTreeNode[];
}

// Based on the actual API response, it returns an array
export interface ReferralTreeResponse extends Array<ReferralTreeNode> {}

export interface ReferralStats {
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  conversion_rate: number;
  avg_earnings_per_referral: number;
}

export interface ReferredUser {
  id: number;
  name: string;
  email: string;
  date: string;
  status: 'Active' | 'Inactive';
  earnings: number;
  level: number;
}

export interface ReferralList {
  total_pages: number;
  current_page: number;
  items: ReferredUser[];
}