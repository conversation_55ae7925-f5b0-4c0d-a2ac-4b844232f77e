import React from 'react';
import NoDataMessage from './NoDataMessage';

interface TableNoDataRowProps {
  colSpan: number;
  type?: 'users' | 'deposits' | 'withdrawals' | 'profits' | 'products' | 'transactions' | 'currency' | 'general';
  message?: string;
  description?: string;
  showSearchHint?: boolean;
  showFilterHint?: boolean;
  className?: string;
}

const TableNoDataRow: React.FC<TableNoDataRowProps> = ({
  colSpan,
  type = 'general',
  message,
  description,
  showSearchHint = false,
  showFilterHint = false,
  className = ''
}) => {
  return (
    <tr>
      <td
        colSpan={colSpan}
        className={`px-6 py-8 ${className}`}
      >
        <NoDataMessage
          type={type}
          message={message}
          description={description}
          showSearchHint={showSearchHint}
          showFilterHint={showFilterHint}
          height="medium"
        />
      </td>
    </tr>
  );
};

export default TableNoDataRow;
